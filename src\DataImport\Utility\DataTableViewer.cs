using System;
using System.Data;
using System.IO;
using System.Diagnostics;
using System.Threading;
using OfficeOpenXml;

namespace DataImport.Utility
{
    public static class DataTableViewer
    {
        public static void ShowDataTableInExcel(DataTable dataTable, string title = "Data Preview")
        {
            // Create a temporary Excel file
            string tempFile = Path.Combine(Path.GetTempPath(), $"{title}_{Guid.NewGuid()}.xlsx");
            
            try
            {
                // Save the DataTable to the Excel file
                SaveDataTableToExcel(dataTable, tempFile);
                
                // Open the Excel file
                Process excelProcess = Process.Start(new ProcessStartInfo
                {
                    FileName = tempFile,
                    UseShellExecute = true
                });
                
                Console.WriteLine($"Data opened in Excel. Close Excel to continue...");
                
                // Wait for the Excel process to exit
                if (excelProcess != null)
                {
                    excelProcess.WaitForExit();
                }
                else
                {
                    // If we couldn't get the process, wait for user input
                    Console.WriteLine("Press Enter after closing Excel to continue...");
                    Console.ReadLine();
                }
                
                Console.WriteLine("Excel closed, continuing execution...");
            }
            finally
            {
                // Try to clean up the temporary file
                try
                {
                    // Wait a moment to ensure Excel has released the file
                    Thread.Sleep(1000);
                    
                    if (File.Exists(tempFile))
                    {
                        File.Delete(tempFile);
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Warning: Could not delete temporary file: {ex.Message}");
                }
            }
        }
        
        private static void SaveDataTableToExcel(DataTable dataTable, string filePath)
        {
            FileInfo fileInfo = new FileInfo(filePath);
            
            using (var package = new ExcelPackage(fileInfo))
            {
                var worksheet = package.Workbook.Worksheets.Add("Data");
                
                // Add headers
                for (int i = 0; i < dataTable.Columns.Count; i++)
                {
                    worksheet.Cells[1, i + 1].Value = dataTable.Columns[i].ColumnName;
                    // Make headers bold
                    worksheet.Cells[1, i + 1].Style.Font.Bold = true;
                }
                
                // Add data
                for (int row = 0; row < dataTable.Rows.Count; row++)
                {
                    for (int col = 0; col < dataTable.Columns.Count; col++)
                    {
                        // Excel rows are 1-based, add 2 to account for header row
                        worksheet.Cells[row + 2, col + 1].Value = 
                            dataTable.Rows[row][col] == DBNull.Value ? null : dataTable.Rows[row][col];
                    }
                }
                
                // Auto-fit columns
                worksheet.Cells.AutoFitColumns();
                
                // Add table formatting
                var range = worksheet.Cells[1, 1, dataTable.Rows.Count + 1, dataTable.Columns.Count];
                var table = worksheet.Tables.Add(range, "DataTable");
                table.ShowHeader = true;
                table.TableStyle = OfficeOpenXml.Table.TableStyles.Medium2;
                
                package.Save();
            }
        }
    }
}

