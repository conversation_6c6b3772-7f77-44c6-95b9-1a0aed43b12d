using DataImport.ImportPipeline;
using DataImport.Config;
using DataImport.Utility;
using System;
using System.Data;
using System.IO;
using OfficeOpenXml;
using System.Windows.Forms;

class Program
{
    static void Main(string[] args)
    {
        // Set the EPPlus license for version 8+
        // ExcelPackage.License = new OfficeOpenXml.LicenseContext(LicenseContext.NonCommercial);
        ExcelPackage.License.SetNonCommercialPersonal("DataETL");


        #region 1. College Selection

        System.Console.WriteLine("\n\n\n\n---------------------------------------------------------------------");
        System.Console.WriteLine("Which College You Want To Import Data For [Enter Respective Number] ?");
        System.Console.WriteLine("0. Local Database");
        System.Console.WriteLine("1. ITM");
        System.Console.WriteLine("2. DSI");

        string collegeIndex = System.Console.ReadLine();

        #endregion College Selection

        #region 2. Initialization of variables based on college index

        string connectionString = "";
        string intermediateFolderPath = "";

        if (collegeIndex == "0")
        {
            //connectionString = ImportConfig.LocalDataBaseConnectionString;
            //intermediateFolderPath = ImportConfig.LocalDataBaseIntermediateFolderPath;
        }
        else if (collegeIndex == "1")
        {
            // ITM
            //connectionString = ImportConfig.ITMConnectionString;
            //intermediateFolderPath = ImportConfig.ITMIntermediateFolderPath;
        }
        else if (collegeIndex == "2")
        {
            // DSI
            connectionString = ImportConfig.DSIConnectionString;
            intermediateFolderPath = ImportConfig.DSIIntermediateFolderPath;
        }
        else
        {
            System.Console.WriteLine("Invalid College Index !");
            throw new Exception("Invalid College Index !");
        }

        #endregion Initialization of variables based on college index

        #region 3. Confirmation of connection string

        // System.Console.WriteLine("Connection String: " + connectionString);
        // string response = Console.ReadLine();
        // if (response.ToLower() != "y")
        // {
        //     return;
        // }
        // System.Console.WriteLine("\n\n");

        // confirmation with popup dialog
        DialogResult resultPopUpConnectionString = MessageBox.Show(
            "Do you want to proceed with the following connection string \n\n[" + connectionString + "] \n\n?",
            "Confirm Connection",
            MessageBoxButtons.YesNo,
            MessageBoxIcon.Question);
        if (resultPopUpConnectionString != DialogResult.Yes)
        {
            return;
        }

        #endregion Confirmation of connection string

        #region 4. Data import into Legacy Tables and Main Tables

        // Data import into Legacy Tables
        // System.Console.WriteLine("Do you want to proceed to import into legacy tables? (yes/no)");
        // response = Console.ReadLine();
        // if (response.ToLower() == "yes")
        // {
        //     // Import into legacy tables
        //     LegacyImportBase.ImportIntoLegacy(intermediateFolderPath, connectionString);
        //     System.Console.WriteLine("Data imported into legacy tables.");
        // }

        // confirmation with popup dialog
        DialogResult resultPopUpLegacy = MessageBox.Show(
            "Do you want to proceed to import into legacy tables?",
            "Confirm Import",
            MessageBoxButtons.YesNo,
            MessageBoxIcon.Question);
        if (resultPopUpLegacy == DialogResult.Yes)
        {
            LegacyImportBase.ImportIntoLegacy(intermediateFolderPath, connectionString);
            System.Console.WriteLine("Data imported into legacy tables.");
        }

        // Data import into Main Tables
        // System.Console.WriteLine("\n\n----------------------------------------------------------");
        // System.Console.WriteLine("Do you want to proceed to import into main tables? (yes/no)");
        // response = Console.ReadLine();
        // if (response.ToLower() == "yes")
        // {
        //     // Import into main tables

        // }

        // confirmation with popup dialog
        DialogResult resultPopUpMain = MessageBox.Show(
            "Do you want to proceed to import into main tables?",
            "Confirm Import",
            MessageBoxButtons.YesNo,
            MessageBoxIcon.Question);
        if (resultPopUpMain == DialogResult.Yes)
        {
            MainImportBase.ImportIntoMain(connectionString);
            System.Console.WriteLine("Data imported into main tables.");
        }


        #endregion Data import into Legacy Tables and Main Tables

    }
}
