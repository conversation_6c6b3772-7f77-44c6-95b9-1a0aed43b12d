// using System;
// using System.Data;

// namespace DataImport.ITMClient
// {
//     public class MainTablesImporter : ImportBase
//     {
//         public MainTablesImporter(DatabaseHandler dbHandler) : base(dbHandler) { }

//         public void Import(DataTable data)
//         {
//             Console.WriteLine("Starting import into main tables...");

//             // Example: Import into MainTable1
//             if (ConfirmImport("MainTable1"))
//             {
//                 ImportIntoTable(data, "MainTable1");
//                 Console.WriteLine("Data imported into MainTable1.");
//             }

//             // Example: Import into MainTable2
//             if (ConfirmImport("MainTable2"))
//             {
//                 ImportIntoTable(data, "MainTable2");
//                 Console.WriteLine("Data imported into MainTable2.");
//             }
//         }

//         private void ImportIntoTable(DataTable data, string tableName)
//         {
//             // Logic to import data into the specified table
//         }
//     }
// }