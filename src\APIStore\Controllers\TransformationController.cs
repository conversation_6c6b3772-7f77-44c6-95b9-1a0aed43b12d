using Microsoft.AspNetCore.Mvc;
using DataTransformation.ITMClient;
using DataTransformation.NADData;
using DataTransformation.PPSUData;

namespace APIStore.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class TransformationController : ControllerBase
    {
        private readonly IWebHostEnvironment _environment;
        private readonly ILogger<TransformationController> _logger;

        public TransformationController(IWebHostEnvironment environment, ILogger<TransformationController> logger)
        {
            _environment = environment;
            _logger = logger;
        }

        private void CleanupDataStore()
        {
            try
            {
                // Clean input directory
                var inputDir = Path.Combine(_environment.ContentRootPath, "DataStore", "InputData");
                if (Directory.Exists(inputDir))
                {
                    Directory.Delete(inputDir, true);
                    Directory.CreateDirectory(inputDir);
                }

                // Clean output directory
                var outputDir = Path.Combine(_environment.ContentRootPath, "DataStore", "OutputData");
                if (Directory.Exists(outputDir))
                {
                    Directory.Delete(outputDir, true);
                    Directory.CreateDirectory(outputDir);
                }

                _logger.LogInformation("DataStore cleanup completed successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during DataStore cleanup");
                throw;
            }
        }

        [HttpPost("upload")]
        public async Task<IActionResult> Upload(IFormFile file)
        {
            try
            {
                if (file == null || file.Length == 0)
                    return BadRequest("No file uploaded");

                // Cleanup existing data before new upload
                CleanupDataStore();

                // Create input directory
                var inputDir = Path.Combine(_environment.ContentRootPath, "DataStore", "InputData");
                Directory.CreateDirectory(inputDir);

                // Save the uploaded file
                var filePath = Path.Combine(inputDir, file.FileName);
                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await file.CopyToAsync(stream);
                }

                return Ok(new { message = "File uploaded successfully", filePath });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading file");
                return StatusCode(500, new { error = "Internal server error" });
            }
        }

        [HttpPost("transform")]
        public IActionResult Transform([FromBody] TransformationRequest request)
        {
            try
            {
                var inputPath = Path.Combine(_environment.ContentRootPath, "DataStore", "InputData");
                var outputPath = Path.Combine(_environment.ContentRootPath, "DataStore", "OutputData");

                // Clean output directory before transformation
                if (Directory.Exists(outputPath))
                {
                    Directory.Delete(outputPath, true);
                }
                Directory.CreateDirectory(outputPath);

                switch (request.College.ToUpper())
                {
                    case "ITM":
                        var itmPipeline = new ITMTransformationPipeline();
                        var itmOutputPath = Path.Combine(outputPath, "IntermediateData_ToImport.xlsx");
                        itmPipeline.Run(inputPath, itmOutputPath);
                        break;

                    case "NAD":
                        var nadPipeline = new NADTransformationPipeline();
                        nadPipeline.Run(inputPath, outputPath);
                        break;

                    default:
                        return BadRequest(new { error = "Unsupported college format" });
                }

                return Ok(new { message = "Transformation completed successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during transformation");
                return StatusCode(500, new { error = "Internal server error" });
            }
        }

        [HttpGet("download/{college}")]
        public IActionResult DownloadTransformedFile(string college)
        {
            try
            {
                var outputPath = Path.Combine(_environment.ContentRootPath, "DataStore", "OutputData");

                System.Console.WriteLine("_____________________________________________");
                System.Console.WriteLine(college);
                System.Console.WriteLine("_____________________________________________");

                switch (college)
                {
                    case "NAD":
                        // var files = Directory.GetFiles(outputPath, "*.xlsx");
                        
                        // if (files.Length == 0)
                        // {
                        //     return NotFound(new { error = "No transformed files found" });
                        // }

                        // using (var memoryStream = new MemoryStream())
                        // {
                        //     using (var archive = new System.IO.Compression.ZipArchive(memoryStream, 
                        //         System.IO.Compression.ZipArchiveMode.Create, true))
                        //     {
                        //         foreach (var file in files)
                        //         {
                        //             var entryName = Path.GetFileName(file);
                        //             var entry = archive.CreateEntry(entryName);
                        //             using (var entryStream = entry.Open())
                        //             using (var fileStream = new FileStream(file, FileMode.Open, FileAccess.Read))
                        //             {
                        //                 fileStream.CopyTo(entryStream);
                        //             }
                        //         }
                        //     }

                        //     memoryStream.Position = 0;
                        //     Response.Headers.Add("Content-Disposition", "attachment; filename=NAD_Transformed_Data.zip");
                        //     return File(memoryStream.ToArray(), "application/zip");
                        // }

                        // var nadPath = Path.Combine(outputPath, "IntermediateData_ToImport.xlsx");

                        var nadFiles = Directory.GetFiles(outputPath, "*.xlsx");
                        
                        if (nadFiles.Length == 0)
                        {
                            return NotFound(new { error = "No transformed files found" });
                        }

                        // Get the first transformed file (assuming single file for now)
                        var nadFilePath = nadFiles[0];
                        var fileName = Path.GetFileName(nadFilePath);
                        
                        if (!System.IO.File.Exists(nadFilePath))
                        {
                            return NotFound(new { error = "Transformed file not found" });
                        }
                        
                        Response.Headers.Add("Content-Disposition", $"attachment; filename={fileName}");
                        return File(System.IO.File.ReadAllBytes(nadFilePath), 
                            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

                    case "ITM":
                        var itmPath = Path.Combine(outputPath, "IntermediateData_ToImport.xlsx");
                        
                        if (!System.IO.File.Exists(itmPath))
                        {
                            return NotFound(new { error = "Transformed file not found" });
                        }
                        
                        Response.Headers.Add("Content-Disposition", "attachment; filename=IntermediateData_ToImport.xlsx");
                        return File(System.IO.File.ReadAllBytes(itmPath), 
                            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

                    default:
                        return BadRequest(new { error = "Unsupported college format" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error downloading transformed file");
                return StatusCode(500, new { error = ex.Message });
            }
        }

        [HttpGet("test")]
        public IActionResult TestConnection()
        {
            return Ok(new { message = "API is running" });
        }

        [HttpPost("cleanup")]
        public IActionResult CleanupData()
        {
            try
            {
                CleanupDataStore();
                return Ok(new { message = "DataStore cleaned successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during manual cleanup");
                return StatusCode(500, new { error = "Internal server error" });
            }
        }

        [HttpGet("logs")]
        public IActionResult GetTransformationLogs()
        {
            try
            {
                System.Console.WriteLine("_____________________ executing transformation logs api _____________________");
                // string logPath = Path.Combine(_environment.ContentRootPath, "Logs", "transformation_logs.txt");
                string logPath = Path.Combine(Directory.GetParent(_environment.ContentRootPath).FullName, "Logs", "data_transformation.txt");
                System.Console.WriteLine(logPath);

                if (!System.IO.File.Exists(logPath))
                {
                    return NotFound(new { error = "No transformation logs found" });
                }

                string logs = System.IO.File.ReadAllText(logPath);
                return Ok(new { logs });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching transformation logs");
                return StatusCode(500, new { error = $"Failed to fetch logs: {ex.Message}" });
            }
        }
    }

    public class TransformationRequest
    {
        public string College { get; set; } = string.Empty;
    }
}





