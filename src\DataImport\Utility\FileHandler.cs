using System;
using System.Data;
using System.IO;
using OfficeOpenXml;

namespace DataImport.Utility
{
    public class FileHandler
    {

        public static DataTable ReadExcelAndCsvFile(string filePath)
        {
            if (Path.GetExtension(filePath).Equals(".csv", StringComparison.OrdinalIgnoreCase))
            {
                // CSV file handling
                return ReadCsvFile(filePath);
            }
            else
            {
                // Excel file handling
                return ReadExcelFile(filePath);
            }
        }


        // Read Excel file and convert it into DataTable
        public static DataTable ReadExcelFile(string filePath)
        {
            DataTable dataTable = new DataTable();
            using (var package = new ExcelPackage(new FileInfo(filePath)))
            {
                var worksheet = package.Workbook.Worksheets[0];
                // Read data from Excel into DataTable
                int rows = worksheet.Dimension.Rows;
                int cols = worksheet.Dimension.Columns;

                // Add columns
                for (int col = 1; col <= cols; col++)
                {
                    dataTable.Columns.Add(worksheet.Cells[1, col].Value?.ToString() ?? $"Column{col}");
                }

                // Add data rows
                for (int row = 2; row <= rows; row++)
                {
                    DataRow dataRow = dataTable.NewRow();
                    for (int col = 1; col <= cols; col++)
                    {
                        dataRow[col - 1] = worksheet.Cells[row, col].Value ?? DBNull.Value;
                    }
                    dataTable.Rows.Add(dataRow);
                }
            }
            return dataTable;
        }

        private static DataTable ReadCsvFile(string filePath)
        {
            DataTable dataTable = new DataTable();

            using (var reader = new StreamReader(filePath))
            using (var csv = new Microsoft.VisualBasic.FileIO.TextFieldParser(reader))
            {
                csv.TextFieldType = Microsoft.VisualBasic.FileIO.FieldType.Delimited;
                csv.SetDelimiters(",");

                // Read header
                if (!csv.EndOfData)
                {
                    string[] headers = csv.ReadFields();
                    foreach (string header in headers)
                    {
                        dataTable.Columns.Add(header);
                    }
                }

                // Read data rows
                while (!csv.EndOfData)
                {
                    try
                    {
                        string[] fields = csv.ReadFields();
                        DataRow dataRow = dataTable.NewRow();

                        for (int i = 0; i < fields.Length; i++)
                        {
                            object value = string.IsNullOrEmpty(fields[i]) ? DBNull.Value : (object)fields[i];
                            System.Console.WriteLine(value.ToString());
                            dataRow[i] = value;
                        }

                        dataTable.Rows.Add(dataRow);
                    }
                    catch (Microsoft.VisualBasic.FileIO.MalformedLineException ex)
                    {
                        Console.WriteLine($"Error reading CSV line: {ex.Message}");
                    }
                }
            }

            return dataTable;
        }

        // Read Excel file and convert it into DataTable
        // public static DataTable ReadExcelFile(string filePath)
        // {
        //     DataTable dataTable = new DataTable();
        //     using (var package = new ExcelPackage(new FileInfo(filePath)))
        //     {
        //         var worksheet = package.Workbook.Worksheets[0];
        //         // Read data from Excel into DataTable
        //         int rows = worksheet.Dimension.Rows;
        //         int cols = worksheet.Dimension.Columns;

        //         // Add columns
        //         for (int col = 1; col <= cols; col++)
        //         {
        //             dataTable.Columns.Add(worksheet.Cells[1, col].Value?.ToString() ?? $"Column{col}");
        //         }

        //         // Add data rows
        //         for (int row = 2; row <= rows; row++)
        //         {
        //             DataRow dataRow = dataTable.NewRow();
        //             for (int col = 1; col <= cols; col++)
        //             {
        //                 dataRow[col - 1] = worksheet.Cells[row, col].Value ?? DBNull.Value;
        //             }
        //             dataTable.Rows.Add(dataRow);
        //         }
        //     }
        //     return dataTable;
        // }

        // public DataTable ReadExcelFile(string filePath)
        // {
        //     DataTable dataTable = new DataTable();
        //     using (var package = new ExcelPackage(new FileInfo(filePath)))
        //     {
        //         var worksheet = package.Workbook.Worksheets[0];
        //         dataTable = worksheet.Cells.LoadFromDataTable(dataTable, true);
        //     }
        //     return dataTable;
        // }

        // public void SaveExcelFile(DataTable dataTable, string filePath)
        // {
        //     using (var package = new ExcelPackage())
        //     {
        //         var worksheet = package.Workbook.Worksheets.Add("Data");
        //         worksheet.Cells["A1"].LoadFromDataTable(dataTable, true);
        //         package.SaveAs(new FileInfo(filePath));
        //     }
        // }

        // Function to save the DataTable to an Excel file
        public static void SaveDataTableToExcel(DataTable dataTable, string filePath)
        {
            System.Console.WriteLine("Saving to Excel file : " + filePath);

            FileInfo fileInfo = new FileInfo(filePath);
            string directoryPath = Path.GetDirectoryName(filePath);

            // Check if the directory exists, if not, create it and all parent directories
            if (!Directory.Exists(directoryPath))
            {
                Directory.CreateDirectory(directoryPath);
            }

            // If the file already exists, delete it to overwrite
            if (fileInfo.Exists)
            {
                fileInfo.Delete();
            }

            // Create a new Excel package and add the worksheet
            using (var package = new ExcelPackage(fileInfo))
            {
                // Add a worksheet, the name can be customized
                var worksheet = package.Workbook.Worksheets.Add("Data");

                // Load the DataTable into the worksheet, starting at cell A1
                worksheet.Cells["A1"].LoadFromDataTable(dataTable, true);

                // Save the file
                package.Save();
            }
        }
        
        public static void LogMessage(string message, string filePath)
        {
            File.AppendAllText(filePath, message + "\n");
        }

        public static void ClearLogFile(string filePath)
        {
            File.WriteAllText(filePath, string.Empty);
        }
    }
}
