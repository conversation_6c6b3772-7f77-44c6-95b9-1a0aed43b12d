using System;
using System.Collections.Generic;
using System.Data;
using System.IO;

namespace DataTransformation.ITMClient
{
    public static class ITMConfig
    {
        #region Configuration Variables

        public static readonly string LogFilePath = "";
        public static string inputFolderPath = @"";
        public static string outputFolderPath = @"";
        

        #endregion Configuration Variables

        #region Configure Exam Name 
        // append exam name with manual configuration
        public static void AppendExamNameWithManulaConfiguration(ref DataTable dataTable, string inputFilePath)
        {
            // Get the file name without extension
            string fileName = Path.GetFileNameWithoutExtension(inputFilePath);

            // Add a new column for ExamName
            dataTable.Columns.Add("ExamName", typeof(string));

            // Populate the ExamName column
            foreach (DataRow row in dataTable.Rows)
            {
                row["ExamName"] = fileName + " Semester - " + row["Semester"];
            }
        }
        #endregion Configure Exam Name 

        #region Data Store

        // renaming header name as per our requirement for our database
        public static Dictionary<string, string> GetHeaderMappings()
        {
            return new Dictionary<string, string>()
            {
                // { "COURSE_NAME", "CourseName" }
            };
        }

        public static List<string> GetColumnsToKeep()
        {
            return new List<string>()
            {
                // ITM Ideal Format 
                // "COURSE_NAME","REGN_NO","CNAME","SEM","TERM_TYPE","EXAM_TYPE","CGPA","SGPA", "MONTH", "YEAR"
            };
        }

        public static Dictionary<string, string> GetSubjectDetailHeaderMapping()
        {
            return new Dictionary<string, string>()
            {
                // {"SUB1NM",              "SUBNM"},
                // {"SUB1",                "SUB"},
                // {"SUB1_GRADE",          "SUB_GRADE"},
                // {"SUB1_GRADE_POINTS",   "SUB_GRADE_POINTS"},
                // {"SUB1_CREDIT",         "SUB_CREDIT"},
                // {"SUB1_CREDIT_POINTS",  "SUB_CREDIT_POINTS"},
                // {"SUB1_REMARKS",        "SUB_REMARKS"},
            };
        }

        public static List<string> GetPrimaryHeaders()
        {
            // must be renamed header name (as per our database, executed after renaming header function)
            return new List<string>()
            {
                // "CourseName", "EnrollmentNo", "StudentName", "Semester", "Term", "ExamType", "CGPA", "SGPA", "Month", "Year"
            };
        }

        public static List<List<string>> GetSecondaryHeaderGroups(int maxSubjectCount)
        {
            List<List<string>> secondaryHeaderGroups = new List<List<string>>();

            for (int i = 0; i < maxSubjectCount; i++)
            {
                secondaryHeaderGroups.Add(
                                new List<string>() {    $"SUB{i+1}NM",
                                                        $"SUB{i+1}",
                                                        $"SUB{i+1}_GRADE",
                                                        $"SUB{i+1}_GRADE_POINTS",
                                                        $"SUB{i+1}_CREDIT",
                                                        $"SUB{i+1}_CREDIT_POINTS",
                                                        $"SUB{i+1}_REMARKS"
                                                    }
                                );
            }

            return secondaryHeaderGroups;
        }

        public static List<string> GetCommonSecondaryHeaders()
        {
            return new List<string>()
            {
                "SubjectName", "SubjectCode", "GradeName", "GradePoint", "Credit", "CreditPoint", "ResultRemarks"
            };
        }

        #endregion Data Store
        
    }
}