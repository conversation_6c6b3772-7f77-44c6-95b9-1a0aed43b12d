using System;
using System.Data;
using DataTransformation.Common;


namespace DataTransformation.ITMClient
{
    public class MiscComputation : TransformationBase
    {
        // public DataTable Compute(DataTable dataTable)
        // {
        //     // Add miscellaneous computations here
        //     return dataTable;
        // }

        public DataTable Compute(DataTable dataTable)
        {
            RenameColumn(dataTable, "PassFail", "OverallPassFail");
            RenameColumn(dataTable, "ExamSeatNo", "SeatNo");
            AddNewColumns(dataTable);
            UpdateValues(dataTable);

            UpdateProgramColumn(dataTable);

            UpdateExamTypeColumn(dataTable);

            UpdateExamNameColumn(dataTable);

            Console.WriteLine("File processed successfully!");

            // string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            // FileHandler.WriteExcelFile(dataTable, $@"..\DataStore\ITM\5_MiscComputed\MiscComputed_{timestamp}.xlsx");

            return dataTable;
        }

        static void RenameColumn(DataTable table, string oldName, string newName)
        {
            if (table.Columns.Contains(oldName))
            {
                table.Columns[oldName].ColumnName = newName;
            }
        }

        static void AddNewColumns(DataTable table)
        {
            if (!table.Columns.Contains("PassFail"))
            {
                table.Columns.Add("PassFail");
            }
            if (!table.Columns.Contains("ExamLID"))
            {
                table.Columns.Add("ExamLID");
            }
            if (!table.Columns.Contains("ExamStudentLID"))
            {
                table.Columns.Add("ExamStudentLID");
            }
            if (!table.Columns.Contains("ExamStudentSubjectLID"))
            {
                table.Columns.Add("ExamStudentSubjectLID");
            }
        }

        static void UpdateValues(DataTable table)
        {
            foreach (DataRow row in table.Rows)
            {
                string gradeName = row["GradeName"]?.ToString();
                string overallPassFail = row["OverallPassFail"]?.ToString();

                // Update OverallPassFail
                if (string.IsNullOrEmpty(overallPassFail) || overallPassFail == "AB" || overallPassFail == "NULL")
                {
                    row["OverallPassFail"] = "Fail";
                }

                // Update Subject-wise PassFail
                if (string.IsNullOrEmpty(gradeName) || gradeName == "F" || gradeName == "AB" || gradeName == "NULL")
                {
                    row["PassFail"] = "Fail";
                }
                else
                {
                    row["PassFail"] = "Pass";
                }

                // Initialize new columns (default values can be set here)
                row["ExamLID"] = ""; // Assign default or calculated value here
                row["ExamStudentLID"] = ""; // Assign default or calculated value here
                row["ExamStudentSubjectLID"] = ""; // Assign default or calculated value here
            }
        }

        static void UpdateProgramColumn(DataTable table)
        {
            if (!table.Columns.Contains("Program"))
            {
                Console.WriteLine("The Program column does not exist.");
                return;
            }

            foreach (DataRow row in table.Rows)
            {
                string program = row["Program"]?.ToString();

                if (!string.IsNullOrEmpty(program))
                {
                    // Convert to uppercase
                    program = program.ToUpper();

                    // Replace "B.Tech." with "B.Tech"
                    program = program.Replace("B.TECH.", "B.TECH");

                    program = program.Replace("BBA", "BBA (HONS.)");

                    // Update the row
                    row["Program"] = program;
                }
            }
        }

        static void UpdateExamTypeColumn(DataTable table)
        {
            if (!table.Columns.Contains("ExamType"))
            {
                Console.WriteLine("The ExamType column does not exist.");
                return;
            }

            foreach (DataRow row in table.Rows)
            {
                string examType = row["ExamType"]?.ToString();

                if (!string.IsNullOrEmpty(examType))
                {
                    // Convert "Reg" to "Regular" and "Rem" to "Remedial"
                    if (examType.Equals("Reg", StringComparison.OrdinalIgnoreCase))
                    {
                        row["ExamType"] = "Regular";
                    }
                    else if (examType.Equals("Rem", StringComparison.OrdinalIgnoreCase))
                    {
                        row["ExamType"] = "Supplementary";
                    }
                }
            }
        }

        static void UpdateExamNameColumn(DataTable table)
        {
            // Add ExamName column if it doesn't exist
            if (!table.Columns.Contains("ExamName"))
            {
                table.Columns.Add("ExamName");
            }

            // Iterate through each row to update ExamName
            foreach (DataRow row in table.Rows)
            {
                string facultyShortName = row["FacultyShortName"]?.ToString();
                string program = row["Program"]?.ToString();
                string examType = row["ExamType"]?.ToString();
                string semester = row["Semester"]?.ToString();
                string monthYear = row["Month_Year"]?.ToString();

                // Ensure all components are not null or empty
                facultyShortName = string.IsNullOrEmpty(facultyShortName) ? "" : facultyShortName.Trim();
                program = string.IsNullOrEmpty(program) ? "" : program.Trim(); 
                examType = string.IsNullOrEmpty(examType) ? "" : examType.Trim(); 
                semester = string.IsNullOrEmpty(semester) ? "" : semester.Trim();
                monthYear = string.IsNullOrEmpty(monthYear) ? "" : monthYear.Trim();

                if(examType == "Supplementary") {
                    examType = "Remedial";
                }

                // Combine the columns to form ExamName
                row["ExamName"] = $"{facultyShortName} {program} {examType} Semester - {semester} {monthYear}".Trim();
            }
        }
    
    }
}