using System;
using System.Data;
using System.Collections.Generic;
using OfficeOpenXml;
using DataImport.Utility;
using DataImport.Config;

namespace DataImport.ImportPipeline
{
    public class LegacyImportBase
    {
        private static string connectionString;

        public static void ImportIntoLegacy(string inputFolderPath, string _connectionString)
        {
            connectionString = _connectionString;
            DataTable masterDataTable = ExatractionAndPreImportComputation(inputFolderPath);

            //Console.WriteLine("Total Rows : " + Convert.ToString(masterDataTable.Rows.Count));

            //DataTableViewer.ShowDataTableInExcel(masterDataTable, "Data To Import");

            // DataTableViewer.ShowDataTableInExcel(masterDataTable, "Master Data");

            ImportIntoDataBaseTable(ref masterDataTable, "EXL_ExamL");
            ImportIntoDataBaseTable(ref masterDataTable, "EXL_ExamStudentL");
            ImportIntoDataBaseTable(ref masterDataTable, "EXL_ExamStudentSubjectL");

        }

        public static void ImportIntoDataBaseTable(ref DataTable masterDataTable, string tableName)
        {
            DataTable DataToImport = TransformDataTableAsPerDataBase(ref masterDataTable, tableName);


            if (DataToImport.Rows.Count > 0)
            {
                DatabaseHandler.InsertDataIntoDatabase(DataToImport, connectionString, tableName);
                Console.WriteLine($"Successfully imported data into {tableName}");
            }
            else
            {
                Console.WriteLine($"No data to import in {tableName}");
            }


            if (tableName == "EXL_ExamL")
            {
                // Dictionary<string, int> ExamName_To_ExamLID_Mapping = DatabaseHandler.GetMappingFromDatabase(connectionString, "EXL_ExamL", "ExamLID", new List<string> { "ExamName" });
                Dictionary<string, int> ExamName_To_ExamLID_Mapping = DatabaseHandler.GetExamLIdMapping(connectionString);
                ImportUtility.FillExamLIDInMasterTable(ref masterDataTable, ExamName_To_ExamLID_Mapping);
            }
            else if (tableName == "EXL_ExamStudentL")
            {
                // Dictionary<string, int> EnrollmentNoExamLID_To_ExamStudentLId_Mapping = DatabaseHandler.GetMappingFromDatabase(connectionString, "EXL_ExamStudentL", "ExamStudentLID", new List<string> { "EnrollmentNo", "ExamLID" });
                Dictionary<string, int> EnrollmentNoExamLID_To_ExamStudentLId_Mapping = DatabaseHandler.GetExamStudentLIdMapping(connectionString);
                ImportUtility.FillExamStudentLIDInMasterTable(ref masterDataTable, EnrollmentNoExamLID_To_ExamStudentLId_Mapping);
            }
            else if (tableName == "EXL_ExamStudentSubjectL")
            {
                // Dictionary<string, int> ExamStudentLIDExamLIDSubjectCode_To_ExamStudentSubjectLID_Mapping = DatabaseHandler.GetMappingFromDatabase(connectionString, "EXL_ExamStudentSubjectL", "ExamStudentSubjectLID", new List<string> { "ExamStudentLID", "ExamLID", "SubjectCode" });
                Dictionary<string, int> ExamStudentLIDExamLIDSubjectCode_To_ExamStudentSubjectLID_Mapping = DatabaseHandler.GetExamStudentSubjectLIDMapping(connectionString);
                ImportUtility.FillExamStudentSubjectLIDInMasterTable(ref masterDataTable, ExamStudentLIDExamLIDSubjectCode_To_ExamStudentSubjectLID_Mapping);
            }
        }

        public static DataTable ExatractionAndPreImportComputation(string inputFolderPath)
        {
            // List<string> excelFiles = ImportUtility.GetListOfExcelFiles(inputFolderPath);
            // Get all supported file types
            var csvFiles = Directory.GetFiles(inputFolderPath, "*.csv", System.IO.SearchOption.AllDirectories);
            var xlsxFiles = Directory.GetFiles(inputFolderPath, "*.xlsx", System.IO.SearchOption.AllDirectories);
            var xlsFiles = Directory.GetFiles(inputFolderPath, "*.xls", System.IO.SearchOption.AllDirectories);

            // Combine all files
            var allFiles = csvFiles.Concat(xlsxFiles).Concat(xlsFiles);
            List<string> excelFiles = allFiles.ToList();

            DataTable masterDataTable = new DataTable(); // Master DataTable to collect all data

            Console.WriteLine("Total Files : " + excelFiles.Count);

            // Collect all rows from all Excel files
            foreach (string filePath in excelFiles)
            {
                Console.WriteLine("Processing file: " + filePath);
                DataTable dataTable = FileHandler.ReadExcelAndCsvFile(filePath);
                if (dataTable.Rows.Count > 0)
                {
                    // Merge data into masterDataTable
                    masterDataTable.Merge(dataTable);
                    Console.WriteLine($"Processed {filePath}");
                }
                else
                {
                    Console.WriteLine($"No data found in {filePath}. Skipping.");
                }
            }

            ImportUtility.UpdateOrAddColumnIntoDataTable(ref masterDataTable, "UserID", 1);
            ImportUtility.UpdateOrAddColumnIntoDataTable(ref masterDataTable, "Created", DateTime.Now);
            ImportUtility.UpdateOrAddColumnIntoDataTable(ref masterDataTable, "Modified", DateTime.Now);
            ImportUtility.UpdateOrAddColumnIntoDataTable(ref masterDataTable, "Remarks", "Imported");
            ImportUtility.UpdateOrAddColumnIntoDataTable(ref masterDataTable, "Description", "Imported");

            return masterDataTable;
        }

        public static DataTable TransformDataTableAsPerDataBase(ref DataTable masterDataTable, string tableName)
        {
            HashSet<string> tableColumns = DatabaseHandler.GetDatabaseTableColumns(connectionString, tableName);

            
            DataTable dt = ImportUtility.FilterDataTableByColumns(masterDataTable, tableColumns);


            // trim all values of datatable (remove extra spaces from left and right side, only if data type is string)
            ImportUtility.TrimStringValueOfDataTable(ref dt);

            DataTable uniqueDataTable = ImportUtility.GetUniqueRows(dt);

            Console.WriteLine("Unique Rows : " + Convert.ToString(uniqueDataTable.Rows.Count));

            List<string> notNullableColumns = ImportConfig.NotNullableColumns(tableName);

            DataTable validDataTable = ImportUtility.ValidateDataTableByCheckingNullablility(uniqueDataTable, notNullableColumns, tableName, masterDataTable);
        
            Console.WriteLine("Valid Rows : " + Convert.ToString(validDataTable.Rows.Count));
            Console.WriteLine("Invalid Rows : " + Convert.ToString(uniqueDataTable.Rows.Count - validDataTable.Rows.Count));

            List<string> checkUniquenessOn = ImportConfig.CheckUniquenessOn(tableName);

            DataTable filteredDataTable = ImportUtility.FilterDataTableByRemovingAlreadyInsertedData(tableName, validDataTable, connectionString, checkUniquenessOn);

            Console.WriteLine("Filtered Rows : " + Convert.ToString(filteredDataTable.Rows.Count));
            Console.WriteLine("Already Inserted Rows : " + Convert.ToString(validDataTable.Rows.Count - filteredDataTable.Rows.Count));

            var (dt_relativeDuplicateData, dt_absoluteDuplicateData, dt_validateData) = ImportUtility.SeparateDuplicates(filteredDataTable, checkUniquenessOn, masterDataTable);

            Console.WriteLine("Duplicate Rows : " + Convert.ToString(dt_absoluteDuplicateData.Rows.Count));
            Console.WriteLine("Data To Import Rows : " + Convert.ToString(dt_validateData.Rows.Count));

            // // Replace the existing DataTableViewer.ShowDataTablePopup call with:
            // DataTableViewer.ShowDataTableInExcel(dt_absoluteDuplicateData, $"{tableName} - Duplicate Data");
            // Console.WriteLine("Continuing execution after Excel was closed...");

            if (ImportConfig.isExtractDublicateByCell && dt_absoluteDuplicateData.Rows.Count > 0)
            {
                // Check if the folder for invalid data exists, if not, create it
                string duplicateDataFolderPath = ImportConfig.dublicateDataByCellFolderPath;
                if (!Directory.Exists(duplicateDataFolderPath))
                {
                    Directory.CreateDirectory(duplicateDataFolderPath);
                }
                string duplicateDataFilePath = Path.Combine(duplicateDataFolderPath, $"{tableName}_DuplicateData.xlsx");
                Console.WriteLine("Duplicate data detected. Storing in Excel file : " + duplicateDataFilePath);
                FileHandler.SaveDataTableToExcel(dt_absoluteDuplicateData, duplicateDataFilePath);
            }
            else if (!ImportConfig.isExtractDublicateByCell && dt_absoluteDuplicateData.Rows.Count > 0)
            {

                // Console.WriteLine("\nisExtractDublicateByCell is set to false. Duplicate data will not be extracted.");
                // Console.WriteLine("want to continue? (y/n)");

                // Confirm with popup dialog
                DialogResult result = MessageBox.Show(
                    $"isExtractDublicateByCell is set to false. Duplicate data will EXIST and will not be extracted. Want to continue?",
                    "Proceed",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);
                if (result != DialogResult.Yes)
                {
                    throw new Exception("isExtractDublicateByCell is set to false. Duplicate data will not be extracted.");
                }

                // string response = Console.ReadLine();
                // if (response.ToLower() != "y")
                // {
                //     throw new Exception("isExtractDublicateByCell is set to false. Duplicate data will not be extracted.");
                // }
            }

            return dt_validateData; // Return the filtered DataTable
        }
        

    }
}