DECLARE	@CurrentDateTime AS DATETIME = GETDATE()

DECLARE	@dtDistinctSubject AS TABLE
(
        ExamID		int,
        SubjectID	int
)
INSERT INTO @dtDistinctSubject
SELECT
        DISTINCT
        MappedExamID,
        SubjectID
FROM	EXL_ExamL

INNER JOIN [dbo].[EXL_ExamStudentSubjectL]
ON	EXL_ExamStudentSubjectL.ExamLID = EXL_ExamL.ExamLID
AND	[dbo].[EXL_ExamL].[MappedExamID] IS NOT NULL
AND	[dbo].[EXL_ExamStudentSubjectL].[SubjectID] IS NOT NULL
-- AND	[dbo].[EXL_ExamStudentSubjectL].[GradeName] <> 'Ex'

--*-------------------------------------------------
AND     EXL_ExamL.ExamLID IN (_listOfExamLIDs_) --|
--*-------------------------------------------------

DECLARE @dtExamWiseSubject AS TABLE (

     ExamID	                                                        int
    ,SubjectID	                                                        int
    ,BaseDepartmentID	                                                int
    ,SubjectCode	                                                nvarchar(20)
    ,SubjectName	                                                nvarchar(250)
    ,SubjectShortName	                                                nvarchar(20)
    ,Credit	                                                        decimal(10, 2)
    ,MarkCETheory	                                                int
    ,MarkCEPractical	                                                int
    ,MarkESETheory	                                                int
    ,MarkESEPractical	                                                int
    ,MarkTotal	                                                        int
    ,PassingMarkCETheory	                                        int
    ,PassingMarkCEPractical	                                        int
    ,PassingMarkESETheory	                                        int
    ,PassingMarkESEPractical	                                        int
    ,PassingMarkTotal	                                                int
    ,PaperMarkESETheory	                                                int
    ,PaperPassingMarkESETheory	                                        int
    ,ESETheoryEligibilityBy	                                        nvarchar(250)
    ,ESEPracticalEligibilityBy	                                        nvarchar(250)
    ,MarkTotalTheory	                                                int
    ,PassingMarkTheory	                                                int
    ,MarkTotalPractical	                                                int
    ,PassingMarkPractical	                                        int
    ,PracticalPassingPCT	                                        decimal(10, 2)
    ,SubjectGroupID	                                                int
    ,ExamDuration	                                                int
    ,ExamRequirements	                                                nvarchar(250)
    ,ResultMergeFromSubjectID	                                        int
    ,ResultMergeToSubjectID	                                        int
    ,MarkVivaVoce	                                                int
    ,PassingMarkVivaVoce	                                        int
    ,IsNonGradialSubject	                                        bit
    ,PrerequisiteSubjectID	                                        int
    ,Description	                                                nvarchar(250)
    ,UserID	                                                        int
    ,Created	                                                        datetime
    ,Modified	                                                        datetime
    ,SubjectGroupName	                                                nvarchar(250)
    ,TheoryPassingPCT	                                                decimal(10, 2)
    ,StudentsInternalTheory	                                        int
    ,StudentsInternalPractical	                                        int
    ,StudentsExternalTheory	                                        int
    ,StudentsExternalPractical	                                        int
    ,QuePaperFormatID	                                                int
    ,PaperMarkESEPractical	                                        int
    ,PassingPaperMarkESEPractical	                                int
    ,ExternalTheoryPassingPCT	                                        decimal(10, 2)
    ,ExternalPracticalPassingPCT	                                decimal(10, 2)
    ,MarkCEPracticalElective	                                        int
    ,PassingMarkCEPracticalElective	                                int
    ,PassingMarkCETheoryMercy	                                        int
    ,PassingMarkCEPracticalMercy	                                int
    ,PassingMarkESETheoryMercy	                                        int
    ,PassingMarkESEPracticalMercy	                                int
    ,PassingMarkTotalMercy	                                        int
    ,PassingMarkCEPracticalElectiveMercy	                        int
    ,PassingMarkVivaVoceMercy	                                        int
    ,NoOfSectionInPaper	                                                int
    ,TheoryFee	                                                        int
    ,PracticalFee	                                                int
    ,ResultProcessedDateTime	                                        datetime
    ,ResultProcessedByUserID	                                        int
    ,SubjectCodePrint	                                                varchar
    ,MainSubjectID	                                                int
    ,SimilarSubjectCodeCSV	                                        nvarchar(250)
    ,MarkESETheorySectionAMax	                                        int
    ,MarkESETheorySectionBMax	                                        int
    ,MarkESETheorySectionCMax	                                        int
    ,IsSystemLocked	                                                bit

)

INSERT INTO @dtExamWiseSubject

SELECT

             [dtDistinctSubject].[ExamID]                                                                                 AS [ExamID]
            ,[dbo].[SUB_Subject].[SubjectID]                                                                                    AS [SubjectID]
            ,[dbo].[SUB_Subject].[BaseDepartmentID]                                                                             AS [BaseDepartmentID]
            ,[dbo].[SUB_Subject].[SubjectCode]                                                                                  AS [SubjectCode]
            ,[dbo].[SUB_Subject].[SubjectName]                                                                                  AS [SubjectName]
            ,[dbo].[SUB_Subject].[SubjectShortName]                                                                             AS [SubjectShortName]
            ,[dbo].[SUB_Subject].[Credit]                                                                                       AS [Credit]
            ,[dbo].[SUB_Subject].[MarkCETheory]                                                                                 AS [MarkCETheory]
            ,[dbo].[SUB_Subject].[MarkCEPractical]                                                                              AS [MarkCEPractical]
            ,[dbo].[SUB_Subject].[MarkESETheory]                                                                                AS [MarkESETheory]
            ,[dbo].[SUB_Subject].[MarkESEPractical]                                                                             AS [MarkESEPractical]
            ,[dbo].[SUB_Subject].[MarkTotal]                                                                                    AS [MarkTotal]
            ,[dbo].[SUB_Subject].[PassingMarkCETheory]                                                                          AS [PassingMarkCETheory]
            ,[dbo].[SUB_Subject].[PassingMarkCEPractical]                                                                       AS [PassingMarkCEPractical]
            ,[dbo].[SUB_Subject].[PassingMarkESETheory]                                                                         AS [PassingMarkESETheory]
            ,[dbo].[SUB_Subject].[PassingMarkESEPractical]                                                                      AS [PassingMarkESEPractical]
            , NULL                                                                                                              AS [PassingMarkTotal]
            ,[dbo].[SUB_Subject].[PaperMarkESETheory]                                                                           AS [PaperMarkESETheory]
            ,[dbo].[SUB_Subject].[PaperPassingMarkESETheory]                                                                    AS [PaperPassingMarkESETheory]
            ,[dbo].[SUB_Subject].[ESETheoryEligibilityBy]                                                                       AS [ESETheoryEligibilityBy]
            ,[dbo].[SUB_Subject].[ESEPracticalEligibilityBy]                                                                    AS [ESEPracticalEligibilityBy]
            , ISNULL([dbo].[SUB_Subject].[MarkCETheory],0) + ISNULL([dbo].[SUB_Subject].[MarkESETheory],0)                      AS [MarkTotalTheory]
            , NULL                                                                                                              AS [PassingMarkTheory]
            , ISNULL([dbo].[SUB_Subject].[MarkCEPractical],0) + 
                    ISNULL([dbo].[SUB_Subject].[MarkESEPractical],0) +  
                        ISNULL([dbo].[SUB_Subject].[MarkCEPracticalElective],0)                                                 AS [MarkTotalPractical]
            , NULL                                                                                                              AS [PassingMarkPractical]
            ,[dbo].[SUB_Subject].[PracticalPassingPCT]                                                                          AS [PracticalPassingPCT]
            ,[dbo].[SUB_Subject].[SubjectGroupID]                                                                               AS [SubjectGroupID]
            ,[dbo].[SUB_Subject].[ExamDuration]                                                                                 AS [ExamDuration]
            ,[dbo].[SUB_Subject].[ExamRequirements]                                                                             AS [ExamRequirements]
            ,[dbo].[SUB_Subject].[ResultMergeFromSubjectID]                                                                     AS [ResultMergeFromSubjectID]
            ,[dbo].[SUB_Subject].[ResultMergeToSubjectID]                                                                       AS [ResultMergeToSubjectID]
            ,[dbo].[SUB_Subject].[MarkVivaVoce]                                                                                 AS [MarkVivaVoce]
            ,[dbo].[SUB_Subject].[PassingMarkVivaVoce]                                                                          AS [PassingMarkVivaVoce]
            ,[dbo].[SUB_Subject].[IsNonGradialSubject]                                                                          AS [IsNonGradialSubject]
            ,[dbo].[SUB_Subject].[PrerequisiteSubjectID]                                                                        AS [PrerequisiteSubjectID]
            , 'Imported'                                                                                                        AS [Description]
            , 1                                                                                                                 AS [UserID]
            , @CurrentDateTime                                                                                                  AS [Created]
            , @CurrentDateTime                                                                                                  AS [Modified]
            ,[dbo].[SUB_Subject].[SubjectGroupName]                                                                             AS [SubjectGroupName]
            ,[dbo].[SUB_Subject].[TheoryPassingPCT]                                                                             AS [TheoryPassingPCT]
            , NULL                                                                                                              AS [StudentsInternalTheory]
            , NULL                                                                                                              AS [StudentsInternalPractical]
            , NULL                                                                                                              AS [StudentsExternalTheory]
            , NULL                                                                                                              AS [StudentsExternalPractical]
            , NULL                                                                                                              AS [QuePaperFormatID]
            ,[dbo].[SUB_Subject].[PaperMarkESEPractical]                                                                        AS [PaperMarkESEPractical]
            ,[dbo].[SUB_Subject].[PassingPaperMarkESEPractical]                                                                 AS [PassingPaperMarkESEPractical]
            , NULL                                                                                                              AS [ExternalTheoryPassingPCT]
            , NULL                                                                                                              AS [ExternalPracticalPassingPCT]
            ,[dbo].[SUB_Subject].[MarkCEPracticalElective]                                                                      AS [MarkCEPracticalElective]
            ,[dbo].[SUB_Subject].[PassingMarkCEPracticalElective]                                                               AS [PassingMarkCEPracticalElective]
            , NULL                                                                                                              AS [PassingMarkCETheoryMercy]
            , NULL                                                                                                              AS [PassingMarkCEPracticalMercy]
            , NULL                                                                                                              AS [PassingMarkESETheoryMercy]
            , NULL                                                                                                              AS [PassingMarkESEPracticalMercy]
            , NULL                                                                                                              AS [PassingMarkTotalMercy]
            , NULL                                                                                                              AS [PassingMarkCEPracticalElectiveMercy]
            , NULL                                                                                                              AS [PassingMarkVivaVoceMercy]
            , NULL                                                                                                              AS [NoOfSectionInPaper]
            , NULL                                                                                                              AS [TheoryFee]
            , NULL                                                                                                              AS [PracticalFee]
            , NULL                                                                                                              AS [ResultProcessedDateTime]
            , NULL                                                                                                              AS [ResultProcessedByUserID]
            ,[dbo].[SUB_Subject].[SubjectCodePrint]                                                                                     AS [SubjectCodePrint]
            ,[dbo].[SUB_Subject].[MainSubjectID]                                                                                        AS [MainSubjectID]
            ,[dbo].[SUB_Subject].[SimilarSubjectCodeCSV]                                                                                AS [SimilarSubjectCodeCSV]
            ,[dbo].[SUB_Subject].[MarkESETheorySectionAMax]                                                                             AS [MarkESETheorySectionAMax]
            ,[dbo].[SUB_Subject].[MarkESETheorySectionBMax]                                                                             AS [MarkESETheorySectionBMax]
            ,[dbo].[SUB_Subject].[MarkESETheorySectionCMax]                                                                             AS [MarkESETheorySectionCMax]
, NULL                                                                                                                                  AS [IsSystemLocked]	                                                

FROM @dtDistinctSubject AS [dtDistinctSubject]

INNER JOIN [dbo].[SUB_Subject]
ON		[dbo].[SUB_Subject].[SubjectID] = [dtDistinctSubject].[SubjectID]

LEFT OUTER JOIN [dbo].[EXM_ExamWiseSubject]
ON		[dbo].[EXM_ExamWiseSubject].[SubjectID] = [dtDistinctSubject].[SubjectID]
AND		[dbo].[EXM_ExamWiseSubject].[ExamID] = [dtDistinctSubject].[ExamID]

WHERE	[dbo].[EXM_ExamWiseSubject].[ExamWiseSubjectID] IS NULL




SELECT * FROM @dtExamWiseSubject