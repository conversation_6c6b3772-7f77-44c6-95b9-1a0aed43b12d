using System;
using System.Data;
using System.Data.SqlClient;
using System.Collections.Generic;
using System.Linq;

namespace DataImport.Utility
{
    public class DatabaseHandler
    {
        public static DataTable FetchDataTable(
            string connectionString, string query
        )
        {
            // Create a DataTable to hold the result
            var dataTable = new DataTable();

            try
            {
                // Establish a connection to the database
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    connection.Open();

                    // Use SqlDataAdapter to execute the query and fill the DataTable
                    using (SqlDataAdapter adapter = new SqlDataAdapter(query, connection))
                    {
                        adapter.Fill(dataTable);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("Error fetching data: " + ex.Message);
                throw new Exception("Error fetching data: " + ex.Message);
            }

            return dataTable;
        }

        public static void InsertDataIntoDatabase(
            DataTable dataTable, string connectionString, string tableName
        )
        {
            // Get the column schema from the database for type mapping
            Dictionary<string, Type> columnSchema = GetColumnSchema(connectionString, tableName);

            // Preprocess the DataTable to ensure data types match the database schema
            DataTable convertedTable = ImportUtility.ConvertDataTableTypes(dataTable, columnSchema);

            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                connection.Open();

                using (SqlBulkCopy bulkCopy = new SqlBulkCopy(connection))
                {
                    bulkCopy.DestinationTableName = tableName;

                    foreach (DataColumn column in convertedTable.Columns)
                    {
                        bulkCopy.ColumnMappings.Add(column.ColumnName, column.ColumnName);
                    }

                    try
                    {
                        bulkCopy.WriteToServer(convertedTable);
                        Console.WriteLine($"Successfully imported data into table {tableName}");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine("Error during bulk insert: " + ex.Message);
                    }
                }
            }
        }

        public static HashSet<string> GetDatabaseTableColumns(
            string connectionString, string tableName
        )
        {
            HashSet<string> columns = new HashSet<string>();

            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                connection.Open();
                string query = $@"
                    SELECT COLUMN_NAME 
                    FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_NAME = '{tableName}'";

                using (SqlCommand command = new SqlCommand(query, connection))
                {
                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            columns.Add(reader["COLUMN_NAME"].ToString());
                        }
                    }
                }
            }

            return columns;
        }


        static Dictionary<string, Type> GetColumnSchema(
            string connectionString, string tableName
        )
        {
            var columnSchema = new Dictionary<string, Type>();

            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                connection.Open();

                // Query to get column information from the information schema
                string query = @"
                SELECT COLUMN_NAME, DATA_TYPE 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = @TableName";

                using (SqlCommand command = new SqlCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@TableName", tableName);

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            string columnName = reader["COLUMN_NAME"].ToString();
                            string dataType = reader["DATA_TYPE"].ToString();

                            // Map SQL data type to .NET type
                            Type netType = GetDotNetType(dataType);

                            if (netType != null)
                            {
                                columnSchema[columnName] = netType;
                            }
                        }
                    }
                }
            }

            return columnSchema;
        }

        static Type GetDotNetType(string sqlDataType)
        {
            // Map common SQL types to .NET types
            switch (sqlDataType.ToLower())
            {
                case "int": return typeof(int);
                case "bigint": return typeof(long);
                case "smallint": return typeof(short);
                case "tinyint": return typeof(byte);
                case "bit": return typeof(bool);
                case "decimal":
                case "numeric":
                case "money":
                case "smallmoney": return typeof(decimal);
                case "float": return typeof(double);
                case "real": return typeof(float);
                case "datetime":
                case "smalldatetime":
                case "date":
                case "time":
                case "datetime2": return typeof(DateTime);
                case "char":
                case "varchar":
                case "text":
                case "nchar":
                case "nvarchar":
                case "ntext": return typeof(string);
                case "uniqueidentifier": return typeof(Guid);
                case "binary":
                case "varbinary":
                case "image": return typeof(byte[]);
                default: return typeof(object); // Fallback for unsupported types
            }
        }


        public static void ExecuteQuery(
            string connectionString, string query
        )
        {
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                connection.Open();
                using (SqlCommand command = new SqlCommand(query, connection))
                {
                    command.ExecuteNonQuery();
                }
            }
        }

        public static Dictionary<string, int> GetMappingFromDatabase(
            string connectionString, 
            string tableName, 
            string idColumnName, 
            List<string> keyColumnNames
        )
        {
            Dictionary<string, int> mapping = new Dictionary<string, int>();

            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                connection.Open();

                // Build column list for the query
                string columnList = string.Join(", ", keyColumnNames) + $", {idColumnName}";
                
                // SQL query to fetch the mapping data
                string query = $"SELECT {columnList} FROM {tableName}";

                using (SqlCommand command = new SqlCommand(query, connection))
                {
                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            // Create composite key from all key columns
                            string compositeKey = string.Join("_", 
                                keyColumnNames.Select(col => reader[col].ToString()));
                            
                            int id = Convert.ToInt32(reader[idColumnName]);

                            // Add the mapping to the dictionary
                            if (!mapping.ContainsKey(compositeKey))
                            {
                                mapping.Add(compositeKey, id);
                            }
                        }
                    }
                }
            }

            return mapping; // Return the dictionary containing all the mappings
        }


        // Helper methods that use the generalized function
        public static Dictionary<string, int> GetExamLIdMapping(string connectionString)
        {
            return GetMappingFromDatabase(
                connectionString,
                "EXL_ExamL",
                "ExamLID",
                new List<string> { "ExamName" });
        }

        public static Dictionary<string, int> GetExamStudentLIdMapping(string connectionString)
        {
            return GetMappingFromDatabase(
                connectionString,
                "EXL_ExamStudentL",
                "ExamStudentLID",
                new List<string> { "EnrollmentNo", "ExamLID" });
        }

        public static Dictionary<string, int> GetExamStudentSubjectLIDMapping(string connectionString)
        {
            return GetMappingFromDatabase(
                connectionString,
                "EXL_ExamStudentSubjectL",
                "ExamStudentSubjectLID",
                new List<string> { "ExamStudentLID", "ExamLID", "SubjectCode" });
        }



    }
}
