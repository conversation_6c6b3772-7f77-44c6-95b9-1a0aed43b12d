DECLARE @CurrentDateTime datetime = GETDATE()

SELECT 
            'Imported' as Remarks,
            'No Check' as AttendanceCriteria,
            ExamName as ExamPrintName,
            @CurrentDateTime AS 'Created',
            @CurrentDateTime AS 'Modiefied',
            @CurrentDateTime AS 'ArchivalDate',

            EXL_ExamL.*, 

            EXM_ExamSeason.ExamSeasonID,
            MST_AcademicYear.FromDate,
            MST_AcademicYear.ToDate,
            INS_Faculty.UniversityID
FROM 
            EXL_ExamL
INNER JOIN	
            EXM_ExamSeason
ON 
            EXM_ExamSeason.AcademicYearID = EXL_ExamL.AcademicYearID
        AND EXM_ExamSeason.Term = EXL_ExamL.Term
        AND EXM_ExamSeason.FacultyID = EXL_ExamL.FacultyID
        --*--------------------------------------------
        AND EXL_ExamL.ExamLID IN (_listOfExamLIDs_) --|
        --*--------------------------------------------

INNER JOIN
            MST_AcademicYear
ON
            MST_AcademicYear.AcademicYearID = EXM_ExamSeason.AcademicYearID
INNER JOIN
            INS_Faculty
ON
            INS_Faculty.FacultyID = EXL_ExamL.FacultyID




