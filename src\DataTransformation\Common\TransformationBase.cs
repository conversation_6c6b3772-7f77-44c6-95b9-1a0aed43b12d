using System;
using System.Data;
using System.Windows.Forms;

namespace DataTransformation.Common
{
    public abstract class TransformationBase
    {
        protected FileHandler FileHandler { get; } = new FileHandler();

        protected bool ConfirmImport(string tableName)
        {
            // Console.WriteLine($"Do you want to import into {tableName}? (y/n)");
            // string response = Console.ReadLine();
            // return response?.ToLower() == "y";

            // Confirm with popup dialog
            DialogResult result = MessageBox.Show(
                $"Do you want to import into {tableName}?",
                "Confirm Import",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);
            return result == DialogResult.Yes;
        }
    }
}