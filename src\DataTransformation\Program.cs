using DataTransformation.ITMClient;
using DataTransformation.NADData;
using DataTransformation.PPSUData;
using System;

class Program
{
    static void Main(string[] args)
    {
        #region 1. Display College Options

        System.Console.WriteLine("Which College You Want To Transform Data For [Enter Respective Number] ?");
        System.Console.WriteLine("1. NAD");
        System.Console.WriteLine("2. ITM");
        System.Console.WriteLine("3. PPSU");
        System.Console.WriteLine("4. Roffel");
        System.Console.WriteLine("5. RAI");
        // ITM, (PPSU), Roffel, IAR, RAI, DSI, GU, (MEU, UPL)

        string? collegeIndex = System.Console.ReadLine();

        #endregion


        #region 2. Process Transformation Based on College Selection

        switch (collegeIndex)
        {
            case "1":
                ProcessNADTransformation();
                break;
            case "2":
                ProcessITMTransformation();
                break;
            case "3":
                ProcessPPSUTransformation();
                break;
            case "4":
            case "5":
                System.Console.WriteLine("In Development");
                break;
            default:
                System.Console.WriteLine("Invalid College Index");
                break;
        }

        #endregion
    
    }

    #region College-Specific Transformation Methods
    
    private static void ProcessNADTransformation()
    {
        // string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
        string inputFolderPath = NADConfig.inputFolderPath;
        string outputFolderPath = NADConfig.outputFolderPath;

        var transformationPipeline = new NADTransformationPipeline();
        transformationPipeline.Run(inputFolderPath, outputFolderPath);
    }

    private static void ProcessITMTransformation()
    {
        // string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
        // string inputFolderPath = ITMConfig.inputFolderPath;
        // string outputFolderPath = ITMConfig.outputFolderPath;

        // var transformationPipeline = new ITMTransformationPipeline();
        // transformationPipeline.Run(inputFolderPath, outputFolderPath);

        throw new Exception("ITM Transformation is not implemented yet");
    }

    private static void ProcessPPSUTransformation()
    {
        // string inputPath = PPSUConfig.inputFolderPath;
        // string outputFolderPath = PPSUConfig.outputFolderPath;

        // var transformationPipeline = new PPSUTransformationPipeline();
        // transformationPipeline.Run(inputPath, outputFolderPath);
        
        throw new Exception("PPSU Transformation is not implemented yet");
    }
    
    #endregion

}
