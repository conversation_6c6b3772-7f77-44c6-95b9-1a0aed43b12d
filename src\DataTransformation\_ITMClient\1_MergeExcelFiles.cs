using System;
using System.Data;
using System.IO;
using OfficeOpenXml;
using DataTransformation.Common;


namespace DataTransformation.ITMClient
{
    public class MergeExcelFiles : TransformationBase
    {
        // public DataTable Merge(string inputFolderPath)
        // {
        //     DataTable masterDataTable = new DataTable();
        //     foreach (var file in Directory.GetFiles(inputFolderPath, "*.xlsx", SearchOption.AllDirectories))
        //     {
        //         DataTable dataTable = FileHandler.ReadExcelFile(file);
        //         masterDataTable.Merge(dataTable);
        //         Console.WriteLine($"Processed {file}");
        //     }
        //     return masterDataTable;
        // }

        public DataTable Merge(string inputFolderPath)
        {
            DataTable masterDataTable = new DataTable();
            
            // Recursively merge all sheets from all Excel files
            ProcessFolder(inputFolderPath, masterDataTable);

            System.Console.WriteLine("Files Merged Successfully!");

            // List of column names in the desired order (as you provided)
            List<string> columnOrder = new List<string>
            {
                "FilePath", "SheetName", "Reg_Rem", "School", "Program", "Branch", "Semester", "Seat_No", "Enr_No", "Name", "Sub01", "Sub01_E", "Sub01_I", "Sub01_P", "Sub01_E_OG", "Sub01_E_CG", "Sub01_E_M", "Sub01_I_M", "Sub01_P_M", "Sub01_E_RM", "Sub01_I_RM", "Sub01_P_RM", "Sub01N", "Sub01_E_G", "Sub01_I_G", "Sub01_P_G", "Sub01_E_GP", "Sub01_I_GP", "Sub01_P_GP", "Sub_01_OGP", "Sub_01_OG", "Sub_01_GI", "Sub01_E_G_P", "Sub01_I_G_P", "Sub01_P_G_P", "Sub_01_OG_P", "Sub02", "Sub02_E", "Sub02_I", "Sub02_P", "Sub02_E_OG", "Sub02_E_CG", "Sub02_E_M", "Sub02_I_M", "Sub02_P_M", "Sub02_E_RM", "Sub02_I_RM", "Sub02_P_RM", "Sub02N", "Sub02_E_G", "Sub02_I_G", "Sub02_P_G", "Sub02_E_GP", "Sub02_I_GP", "Sub02_P_GP", "Sub_02_OGP", "Sub_02_OG", "Sub_02_GI", "Sub02_E_G_P", "Sub02_I_G_P", "Sub02_P_G_P", "Sub_02_OG_P", "Sub03", "Sub03_E", "Sub03_I", "Sub03_P", "Sub03_E_OG", "Sub03_E_CG", "Sub03_E_M", "Sub03_I_M", "Sub03_P_M", "Sub03_E_RM", "Sub03_I_RM", "Sub03_P_RM", "Sub03N", "Sub03_E_G", "Sub03_I_G", "Sub03_P_G", "Sub03_E_GP", "Sub03_I_GP", "Sub03_P_GP", "Sub_03_OGP", "Sub_03_OG", "Sub_03_GI", "Sub03_E_G_P", "Sub03_I_G_P", "Sub03_P_G_P", "Sub_03_OG_P", "Sub04", "Sub04_E", "Sub04_I", "Sub04_P", "Sub04_E_OG", "Sub04_E_CG", "Sub04_E_M", "Sub04_I_M", "Sub04_P_M", "Sub04_E_RM", "Sub04_I_RM", "Sub04_P_RM", "Sub04N", "Sub04_E_G", "Sub04_I_G", "Sub04_P_G", "Sub04_E_GP", "Sub04_I_GP", "Sub04_P_GP", "Sub_04_OGP", "Sub_04_OG", "Sub_04_GI", "Sub04_E_G_P", "Sub04_I_G_P", "Sub04_P_G_P", "Sub_04_OG_P", "Sub05", "Sub05_E", "Sub05_I", "Sub05_P", "Sub05_E_OG", "Sub05_E_CG", "Sub05_E_M", "Sub05_I_M", "Sub05_P_M", "Sub05_E_RM", "Sub05_I_RM", "Sub05_P_RM", "Sub05N", "Sub05_E_G", "Sub05_I_G", "Sub05_P_G", "Sub05_E_GP", "Sub05_I_GP", "Sub05_P_GP", "Sub_05_OGP", "Sub_05_OG", "Sub_05_GI", "Sub05_E_G_P", "Sub05_I_G_P", "Sub05_P_G_P", "Sub_05_OG_P", "Sub06", "Sub06_E", "Sub06_I", "Sub06_P", "Sub06_E_OG", "Sub06_E_CG", "Sub06_E_M", "Sub06_I_M", "Sub06_P_M", "Sub06_E_RM", "Sub06_I_RM", "Sub06_P_RM", "Sub06N", "Sub06_E_G", "Sub06_I_G", "Sub06_P_G", "Sub06_E_GP", "Sub06_I_GP", "Sub06_P_GP", "Sub_06_OGP", "Sub_06_OG", "Sub_06_GI", "Sub06_E_G_P", "Sub06_I_G_P", "Sub06_P_G_P", "Sub_06_OG_P", "Sub07", "Sub07_E", "Sub07_I", "Sub07_P", "Sub07_E_OG", "Sub07_E_CG", "Sub07_E_M", "Sub07_I_M", "Sub07_P_M", "Sub07_E_RM", "Sub07_I_RM", "Sub07_P_RM", "Sub07N", "Sub07_E_G", "Sub07_I_G", "Sub07_P_G", "Sub07_E_GP", "Sub07_I_GP", "Sub07_P_GP", "Sub_07_OGP", "Sub_07_OG", "Sub_07_GI", "Sub07_E_G_P", "Sub07_I_G_P", "Sub07_P_G_P", "Sub_07_OG_P", "Sub08", "Sub08_E", "Sub08_I", "Sub08_P", "Sub08_E_OG", "Sub08_E_CG", "Sub08_E_M", "Sub08_I_M", "Sub08_P_M", "Sub08_E_RM", "Sub08_I_RM", "Sub08_P_RM", "Sub08N", "Sub08_E_G", "Sub08_I_G", "Sub08_P_G", "Sub08_E_GP", "Sub08_I_GP", "Sub08_P_GP", "Sub_08_OGP", "Sub_08_OG", "Sub_08_GI", "Sub08_E_G_P", "Sub08_I_G_P", "Sub08_P_G_P", "Sub_08_OG_P", "Sub09", "Sub09_E", "Sub09_I", "Sub09_P", "Sub09_E_OG", "Sub09_E_CG", "Sub09_E_M", "Sub09_I_M", "Sub09_P_M", "Sub09_E_RM", "Sub09_I_RM", "Sub09_P_RM", "Sub09N", "Sub09_E_G", "Sub09_I_G", "Sub09_P_G", "Sub09_E_GP", "Sub09_I_GP", "Sub09_P_GP", "Sub_09_OGP", "Sub_09_OG", "Sub_09_GI", "Sub09_E_G_P", "Sub09_I_G_P", "Sub09_P_G_P", "Sub_09_OG_P", "Sub10", "Sub10_E", "Sub10_I", "Sub10_P", "Sub10_E_OG", "Sub10_E_CG", "Sub10_E_M", "Sub10_I_M", "Sub10_P_M", "Sub10_E_RM", "Sub10_I_RM", "Sub10_P_RM", "Sub10N", "Sub10_E_G", "Sub10_I_G", "Sub10_P_G", "Sub10_E_GP", "Sub10_I_GP", "Sub10_P_GP", "Sub_10_OGP", "Sub_10_OG", "Sub_10_GI", "Sub10_E_G_P", "Sub10_I_G_P", "Sub10_P_G_P", "Sub_10_OG_P", "Sub11", "Sub11_E", "Sub11_I", "Sub11_P", "Sub11_E_OG", "Sub11_E_CG", "Sub11_E_M", "Sub11_I_M", "Sub11_P_M", "Sub11_E_RM", "Sub11_I_RM", "Sub11_P_RM", "Sub11N", "Sub11_E_G", "Sub11_I_G", "Sub11_P_G", "Sub11_E_GP", "Sub11_I_GP", "Sub11_P_GP", "Sub_11_OGP", "Sub_11_OG", "Sub_11_GI", "Sub11_E_G_P", "Sub11_I_G_P", "Sub11_P_G_P", "Sub_11_OG_P", "Sub12", "Sub12_E", "Sub12_I", "Sub12_P", "Sub12_E_OG", "Sub12_E_CG", "Sub12_E_M", "Sub12_I_M", "Sub12_P_M", "Sub12_E_RM", "Sub12_I_RM", "Sub12_P_RM", "Sub12N", "Sub12_E_G", "Sub12_I_G", "Sub12_P_G", "Sub12_E_GP", "Sub12_I_GP", "Sub12_P_GP", "Sub_12_OGP", "Sub_12_OG", "Sub_12_GI", "Sub12_E_G_P", "Sub12_I_G_P", "Sub12_P_G_P", "Sub_12_OG_P", "Sub13", "Sub13_E", "Sub13_I", "Sub13_P", "Sub13_E_OG", "Sub13_E_CG", "Sub13_E_M", "Sub13_I_M", "Sub13_P_M", "Sub13_E_RM", "Sub13_I_RM", "Sub13_P_RM", "Sub13N", "Sub13_E_G", "Sub13_I_G", "Sub13_P_G", "Sub13_E_GP", "Sub13_I_GP", "Sub13_P_GP", "Sub_13_OGP", "Sub_13_OG", "Sub_13_GI", "Sub13_E_G_P", "Sub13_I_G_P", "Sub13_P_G_P", "Sub_13_OG_P", "Sub14", "Sub14_E", "Sub14_I", "Sub14_P", "Sub14_E_OG", "Sub14_E_CG", "Sub14_E_M", "Sub14_I_M", "Sub14_P_M", "Sub14_E_RM", "Sub14_I_RM", "Sub14_P_RM", "Sub14N", "Sub14_E_G", "Sub14_I_G", "Sub14_P_G", "Sub14_E_GP", "Sub14_I_GP", "Sub14_P_GP", "Sub_14_OGP", "Sub_14_OG", "Sub_14_GI", "Sub14_E_G_P", "Sub14_I_G_P", "Sub14_P_G_P", "Sub_14_OG_P", "Sub15", "Sub15_E", "Sub15_I", "Sub15_P", "Sub15_E_OG", "Sub15_E_CG", "Sub15_E_M", "Sub15_I_M", "Sub15_P_M", "Sub15_E_RM", "Sub15_I_RM", "Sub15_P_RM", "Sub15N", "Sub15_E_G", "Sub15_I_G", "Sub15_P_G", "Sub15_E_GP", "Sub15_I_GP", "Sub15_P_GP", "Sub_15_OGP", "Sub_15_OG", "Sub_15_GI", "Sub15_E_G_P", "Sub15_I_G_P", "Sub15_P_G_P", "Sub_15_OG_P", "SGPA", "GP_Sem_1", "GP_Sem_2", "GP_Sem_3", "GP_Sem_4", "GP_Sem_5", "GP_Sem_6", "GP_Sem_7", "GP_Sem_8", "GP_Sem_9", "GP_Sem_10", "Backlog Sem_1", "Backlog Sem_2", "Backlog Sem_3", "Backlog Sem_4", "Backlog Sem_5", "Backlog Sem_6", "Backlog Sem_7", "Backlog Sem_8", "Backlog Sem_9", "Backlog Sem_10", "Total Backlogs", "SGPA_01", "SGPA_02", "SGPA_03", "SGPA_04", "SGPA_05", "SGPA_06", "SGPA_07", "SGPA_08", "SGPA_09", "SGPA_10", "Overall CGPA", "RESULT", "Elective Total Credits", "Count P*", "Grade_Points"
            };

            masterDataTable = ReorderDataTableColumns(masterDataTable, columnOrder);

            System.Console.WriteLine("Files Reordered Successfully!");


            // string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            // FileHandler.WriteExcelFile(masterDataTable, $@"..\DataStore\ITM\1_MergedOutput\MergedData_{timestamp}.xlsx");

            return masterDataTable;
        }

        #region 1.0 Excel Processing
        // Processes a single Excel file and merges all worksheets into the master DataTable
        static void ProcessExcelFile(string filePath, DataTable masterDataTable)
        {
            using (var package = new ExcelPackage(new FileInfo(filePath)))
            {
                foreach (var worksheet in package.Workbook.Worksheets)
                {
                    DataTable sheetData = ReadSheetData(worksheet, filePath);  // No need to pass as ref
                    masterDataTable.Merge(sheetData);  // Merge the data into the master DataTable
                }
            }
        }
        #endregion

        #region 2.0 Folder Processing
        
        // Legacy folder processing implementation (commented)
        
        // static void ProcessFolder(string folderPath, DataTable masterDataTable)
        // {
        //     foreach (var file in Directory.GetFiles(folderPath, "*.xlsx", SearchOption.AllDirectories))
        //     {
        //         ProcessExcelFile(file, masterDataTable);  // No need for ref here either
        //     }
        // }
        
        
        // Processes either single Excel file or directory containing multiple Excel files
        
        static void ProcessFolder(string folderPath, DataTable masterDataTable)
        {
            if (File.Exists(folderPath) && Path.GetExtension(folderPath).Equals(".xlsx", StringComparison.OrdinalIgnoreCase))
            {
                // If folderPath is a single Excel file, process it directly
                ProcessExcelFile(folderPath, masterDataTable);
            }
            else if (Directory.Exists(folderPath))
            {
                // If folderPath is a directory, process all Excel files inside it recursively
                foreach (var file in Directory.GetFiles(folderPath, "*.xlsx", SearchOption.AllDirectories))
                {
                    ProcessExcelFile(file, masterDataTable);
                }
            }
            else
            {
                Console.WriteLine("Invalid file or directory path.");
            }
        }
        #endregion

        #region 3.0 Data Reading
        
        // Reads worksheet data and converts to DataTable format with additional metadata columns
        
        static DataTable ReadSheetData(ExcelWorksheet worksheet, string filePath)
        {
            DataTable dataTable = new DataTable();
            // bool headerRow = true;

            // Add columns for the sheet's headers
            foreach (var firstRowCell in worksheet.Cells[1, 1, 1, worksheet.Dimension.End.Column])
            {
                dataTable.Columns.Add(firstRowCell.Text);
            }

            // Add columns for the file path and sheet name
            dataTable.Columns.Add("FilePath");
            dataTable.Columns.Add("SheetName");

            // Read the remaining rows
            for (int rowNum = 2; rowNum <= worksheet.Dimension.End.Row; rowNum++)
            {
                var row = dataTable.NewRow();

                // Ensure that column indexes are within bounds
                for (int colNum = 1; colNum <= worksheet.Dimension.End.Column; colNum++)
                {
                    if (worksheet.Cells[rowNum, colNum].Value != null)
                    {
                        row[colNum - 1] = worksheet.Cells[rowNum, colNum].Text;
                    }
                    else
                    {
                        row[colNum - 1] = DBNull.Value;  // Handle empty cells gracefully
                    }
                }

                // Add the file path and sheet name to each row
                row["FilePath"] = filePath;
                row["SheetName"] = worksheet.Name;

                dataTable.Rows.Add(row);
            }

            return dataTable;
        }
        #endregion

        #region 4.0 Column Management
        
        // Reorders DataTable columns while preserving all data and handling missing columns
        
        public static DataTable ReorderDataTableColumns(DataTable dataTable, List<string> newColumnOrder)
        {
            DataTable reorderedTable = new DataTable();
            
            foreach (var columnName in newColumnOrder)
            {
                reorderedTable.Columns.Add(columnName);
            }

            foreach (DataColumn column in dataTable.Columns)
            {
                if (!reorderedTable.Columns.Contains(column.ColumnName))
                {
                    reorderedTable.Columns.Add(column.ColumnName);
                }
            }


            // Reorder the rows based on the new column order
            foreach (DataRow row in dataTable.Rows)
            {
                DataRow newRow = reorderedTable.NewRow();
                
                // Add values to the new row based on the column order
                foreach (var columnName in newColumnOrder)
                {
                    if (dataTable.Columns.Contains(columnName))
                    {
                        newRow[columnName] = row[columnName];
                    }
                    else
                    {
                        newRow[columnName] = DBNull.Value; // Handle missing columns by setting them as DBNull (empty)
                    }
                }

                // Add the new row to the reordered table
                reorderedTable.Rows.Add(newRow);
            }

            return reorderedTable;
        }
        #endregion
    }
}
