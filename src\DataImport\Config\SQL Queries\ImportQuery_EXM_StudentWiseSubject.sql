DECLARE	@CurrentDateTime AS DATETIME = GETDATE()

DECLARE @dtStudentWiseSubject AS TABLE (
     FirstExamID	                                                      int
    ,LastExamID	                                                          int
    ,ProgramID	                                                          int
    ,StudentID	                                                          int
    ,EnrollmentNo	                                                      nvarchar(50)
    ,LastExamSeatNo	                                                      nvarchar(20)
    ,<PERSON><PERSON><PERSON>	                                                          int
    ,SubjectID	                                                          int
    ,AttendancePCT	                                                      decimal(10, 2)
    ,AttendanceDetentionPCT	                                              decimal(10, 2)
    ,IsDetainedDueToLowAttendance	                                      bit                                          
    ,MarkInternalTheoryMAX	                                              int
    ,MarkInternalTheoryPassing	                                          int
    ,MarkInternalTheoryGracing	                                          int
    ,MarkInternalTheoryObtainedOriginal	                                  int
    ,MarkInternalTheoryObtainedGracing	                                  int
    ,MarkInternalPracticalMAX	                                          int
    ,MarkInternalPracticalPassing	                                      int
    ,MarkInternalPracticalGracing	                                      int
    ,MarkInternalPracticalObtainedOriginal	                              int
    ,MarkInternalPracticalObtainedGracing	                              int
    ,MarkESETheoryMAX	                                                  int
    ,MarkESETheoryPassing	                                              int
    ,MarkESETheoryGracing	                                              int
    ,MarkESETheoryGracingToAll	                                          int
    ,MarkESETheorySectionA	                                              int
    ,MarkESETheorySectionB	                                              int
    ,MarkESETheoryPaperMAX	                                              int
    ,MarkESETheoryPaperPassing	                                          int
    ,MarkESETheoryObtainedMapped	                                      int
    ,MarkESETheoryObtainedGracing	                                      int
    ,MarkESEPracticalMAX	                                              int
    ,MarkESEPracticalPassing	                                          int
    ,MarkESEPracticalGracing	                                          int
    ,MarkESEPracticalGracingToAll	                                      int
    ,MarkESEPracticalObtainedOriginal	                                  int
    ,MarkESEPracticalObtainedGracing	                                  int
    ,MarkTheoryObtainedTotal	                                          int
    ,MarkPracticalObtainedTotal	                                          int
    ,MarkTotalMAX	                                                      int
    ,MarkTotalObtained	                                                  int
    ,MarkTotalObtainedOutOf100	                                          int
    ,AttemptCount	                                                      int
    ,IsPass	                                                              bit                  
    ,GradeID	                                                          int
    ,GradeName	                                                          nvarchar(250)
    ,UFMPunishmentID	                                                  int
    ,InternalTheoryStatus	                                              nvarchar(10)
    ,InternalPracticalStatus	                                          nvarchar(10)
    ,ExternalTheoryStatus	                                              nvarchar(10)
    ,ExternalPracticalStatus	                                          nvarchar(10)
    ,Remarks	                                                          nvarchar(500)
    ,UserID	                                                              int
    ,Created	                                                          datetime                      
    ,Modified	                                                          datetime                      
    ,IsExemption	                                                      bit                          
    ,ExemptionByUserID	                                                  int
    ,ExemptionDateTime	                                                  datetime                              
    ,ExemptionRemarks	                                                  nvarchar(500)
    ,Credit	                                                              decimal(10, 2)
    ,GracingMark	                                                      int
    ,IsShowResult	                                                      bit                          
    ,IsShowExemptionInResult	                                          bit                                      
    ,IsShowResultInExemption	                                          bit                                      
    ,GradePoint	                                                          decimal(10, 2)
    ,CreditPoint	                                                      decimal(10, 2)
    ,ResultRemarks	                                                      nvarchar(500)
    ,MarkESEVivaVoceMax	                                                  int
    ,MarkESEVivaVoceObtained	                                          int
    ,IsDetainedDueToBackLog	                                              bit                                  
    ,MarkESETheorySectionC	                                              int
    ,MarkESETheorySectionD	                                              int
    ,MarkESETheorySectionE	                                              int
    ,CurriculumWiseSubjectID	                                          int
    ,GradeSystemID	                                                      int
    ,TheoryAttendancePCT	                                              decimal(10, 2)
    ,PracticalAttendancePCT	                                              decimal(10, 2)
    ,ExamStudentSubjectLID	                                              int
    ,StudentNotAppearingReasonID	                                      int
    ,StudentSubjectNotAppearingReasonID	                                  int
    ,ExamWiseFailReasonID	                                              int
    ,ExternalTheoryGradeID	                                              int
    ,ExternalPracticalGradeID	                                          int
    ,InternalTheoryGradeID	                                              int
    ,InternalPracticalGradeID	                                          int
    ,ExternalTheoryGradeName	                                          nvarchar(20)
    ,ExternalPracticalGradeName	                                          nvarchar(20)
    ,InternalTheoryGradeName	                                          nvarchar(20)
    ,InternalPracticalGradeName	                                          nvarchar(20)
    ,IsEligibleForTheoryExam	                                          bit                                      
    ,IsEligibleForPracticalExam	                                          bit                                      
    ,MarkTotalOutOf100	                                                  decimal(10, 2)
    ,MarkExternalTheoryObtainedOriginal	                                  int
    ,MarkExternalPracticalObtainedOriginal	                              int
    ,MarkInternalPracticalElectiveObtained	                              int
    ,MarkInternalPracticalElectiveObtainedGracing	                      int
    ,MarkInternalMAX	                                                  int
    ,ResultPCTExternalTheory	                                          decimal(10, 2)
    ,ResultPCTExternalPractical	                                          decimal(10, 2)
    ,ResultPCTInternalTheory	                                          decimal(10, 2)
    ,ResultPCTInternalPractical	                                          decimal(10, 2)
    ,InternalPracticalElectiveStatus	                                  nvarchar(10)
    ,ExternalVivaVoceStatus	                                              nvarchar(10)
    ,MarkInternalPracticalElectiveObtainedOriginal	                      int
    ,MarkInternalPracticalElectiveMAX	                                  int
    ,MarkInternalPracticalElectivePassing	                              int
    ,MarkTheoryMax	                                                      int
    ,MarkPracticalMax	                                                  int
    ,MarkTheoryPassing	                                                  int
    ,MarkPracticalPassing	                                              int
    ,PassedInMonthYear	                                                  nvarchar(50)
    ,MarkCETheoryAttendanceObtained	                                      int
    ,MarkGracingAllotedExternalTheory	                                  int
    ,MarkGracingAllotedInternalTheory	                                  int
    ,MarkGracingAllotedExternalPractical	                              int
    ,MarkGracingAllotedInternalPractical	                              int
    ,MarkGracingAllotedInternalPracticalElective	                      int
    ,MarkGracingAllotedVivaVoce	                                          int
    ,GNRemarks	                                                          nvarchar(500)
    ,oldCurriculumWiseSubject	                                          int
    ,PracticalGradeID	                                                  int
    ,PracticalGradeName	                                                  nvarchar(10)
    ,SubjectConvenerGradeID	                                              int
    ,SubjectConvenerGradeName	                                          nvarchar(10)
    ,Sequence	                                                          decimal(10, 2)
    ,GNPrevious_CurriculumWiseSubjectID	                                  int
    ,IsSystemLocked	                                                      bit                          
    ,TotalTheoryMarksAsPerCredit	                                      decimal(10, 2)
    ,TotalPracticalMarksAsPerCredit	                                      decimal(10, 2)
    ,TotalMarksAsPerCredit	                                              decimal(10, 2)
)





INSERT INTO @dtStudentWiseSubject
	SELECT

		 [dbo].[EXL_ExamL].[MappedExamID]                                                                                       AS [FirstExamID]                                            
        ,[dbo].[EXL_ExamL].[MappedExamID]                                                                                       AS [LastExamID]                                            
        ,[dbo].[STU_Student].[ProgramID]                                                                                        AS [ProgramID]                                        
        ,[dbo].[STU_Student].[StudentID]                                                                                        AS [StudentID]                                        
        ,[dbo].[STU_Student].[EnrollmentNo]                                                                                     AS [EnrollmentNo]                                            
        ,[dbo].[EXL_ExamStudentL].[SeatNo]                                                                                      AS [LastExamSeatNo]                                                
        ,[dbo].[EXL_ExamL].[Semester]                                                                                           AS [Semester]                                        
        ,[dbo].[EXL_ExamStudentSubjectL].[SubjectID]                                                                            AS [SubjectID]                                        
        , NULL                                                                                                                  AS [AttendancePCT]                                            
        , NULL                                                                                                                  AS [AttendanceDetentionPCT]                                                        
        , 0                                                                                                                     AS [IsDetainedDueToLowAttendance]                                                            
        ,[dbo].[EXM_ExamWiseSubject].[MarkCETheory]                                                                             AS [MarkInternalTheoryMAX]                                                    
        ,[dbo].[EXM_ExamWiseSubject].[PassingMarkCETheory]                                                                      AS [MarkInternalTheoryPassing]                                                        
        , NULL                                                                                                                  AS [MarkInternalTheoryGracing]                                                        
        ,[dbo].[EXL_ExamStudentSubjectL].[InternalTheoryObtainedMark]                                                           AS [MarkInternalTheoryObtainedOriginal]                                                                    
        ,[dbo].[EXL_ExamStudentSubjectL].[InternalTheoryObtainedMark]                                                           AS [MarkInternalTheoryObtainedGracing]                                                                
        ,[dbo].[EXM_ExamWiseSubject].[MarkCEPractical]                                                                          AS [MarkInternalPracticalMAX]                                                        
        ,[dbo].[EXM_ExamWiseSubject].[PassingMarkCEPractical]                                                                   AS [MarkInternalPracticalPassing]                                                            
        , NULL                                                                                                                  AS [MarkInternalPracticalGracing]                                                            
        ,[dbo].[EXL_ExamStudentSubjectL].[InternalPracticalObtainedMark]                                                                AS [MarkInternalPracticalObtainedOriginal]                                                                    
        ,[dbo].[EXL_ExamStudentSubjectL].[InternalPracticalObtainedMark]                                                                AS [MarkInternalPracticalObtainedGracing]                                                                    
        ,[dbo].[EXM_ExamWiseSubject].[MarkESETheory]                                                                            AS [MarkESETheoryMAX]                                                
        ,[dbo].[EXM_ExamWiseSubject].[PassingMarkESETheory]                                                                     AS [MarkESETheoryPassing]                                                    
        , NULL                                                                                                                  AS [MarkESETheoryGracing]                                                    
        , NULL                                                                                                                  AS [MarkESETheoryGracingToAll]                                                        
        , NULL                                                                                                                  AS [MarkESETheorySectionA]                                                    
        , NULL                                                                                                                  AS [MarkESETheorySectionB]                                                    
        ,[dbo].[EXM_ExamWiseSubject].[PaperMarkESETheory]                                                                       AS [MarkESETheoryPaperMAX]                                                    
        ,[dbo].[EXM_ExamWiseSubject].[PaperPassingMarkESETheory]                                                                AS [MarkESETheoryPaperPassing]                                                        
        ,[dbo].[EXL_ExamStudentSubjectL].[ExternalTheoryObtainedMark]                                                           AS [MarkESETheoryObtainedMapped]                                                            
        ,[dbo].[EXL_ExamStudentSubjectL].[ExternalTheoryObtainedMark]                                                           AS [MarkESETheoryObtainedGracing]                                                            
        ,[dbo].[EXM_ExamWiseSubject].[MarkESEPractical]                                                                         AS [MarkESEPracticalMAX]                                                    
        ,[dbo].[EXM_ExamWiseSubject].[PassingMarkESEPractical]                                                                  AS [MarkESEPracticalPassing]                                                        
        , NULL                                                                                                                  AS [MarkESEPracticalGracing]                                                        
        , NULL                                                                                                                  AS [MarkESEPracticalGracingToAll]                                                            
        ,[dbo].[EXL_ExamStudentSubjectL].[ExternalPracticalObtainedMark]                                                                AS [MarkESEPracticalObtainedOriginal]                                                                
        ,[dbo].[EXL_ExamStudentSubjectL].[ExternalPracticalObtainedMark]                                                                AS [MarkESEPracticalObtainedGracing]                                                                
       
        ,CASE WHEN ISNULL([EXL_ExamStudentSubjectL].[TheoryObtainedMark], 0) > 0
                THEN [EXL_ExamStudentSubjectL].[TheoryObtainedMark]
                ELSE ISNULL([EXL_ExamStudentSubjectL].[InternalTheoryObtainedMark], 0) + 
                     ISNULL([EXL_ExamStudentSubjectL].[ExternalTheoryObtainedMark], 0)
            END                                                                                                                  AS [MarkTheoryObtainedTotal]                                                        

        ,CASE WHEN ISNULL([EXL_ExamStudentSubjectL].[PracticalObtainedMark], 0) > 0
                THEN [EXL_ExamStudentSubjectL].[PracticalObtainedMark]
                ELSE ISNULL([EXL_ExamStudentSubjectL].[InternalPracticalObtainedMark], 0) + 
                     ISNULL([EXL_ExamStudentSubjectL].[ExternalPracticalObtainedMark], 0)
            END                                                                                                                  AS [MarkPracticalObtainedTotal]                                                            
        
        ,[dbo].[EXM_ExamWiseSubject].[MarkTotal]                                                                                AS [MarkTotalMAX]                                            
        ,[dbo].[EXL_ExamStudentSubjectL].[TotalObtainedMark]                                                                    AS [MarkTotalObtained]                                                
        , NULL                                                                                                                  AS [MarkTotalObtainedOutOf100]                                                        
        ,[dbo].[EXL_ExamStudentSubjectL].[AttemptNo]                                                                            AS [AttemptCount]                                            
        ,CASE WHEN ISNULL([dbo].[EXL_ExamStudentSubjectL].[PassFail], 'Fail') = 'Pass' THEN 1 ELSE 0 END                        AS [IsPass]                                        
        ,[dbo].[EXL_ExamStudentSubjectL].[GradeID]                                                                              AS [GradeID]                                        
        ,[dbo].[EXL_ExamStudentSubjectL].[GradeName]                                                                            AS [GradeName]                                        
        , NULL                                                                                                                  AS [UFMPunishmentID]                                                
        , NULL                                                                                                                  AS [InternalTheoryStatus]                                                    
        , NULL                                                                                                                  AS [InternalPracticalStatus]                                                        
        , NULL                                                                                                                  AS [ExternalTheoryStatus]                                                    
        , NULL                                                                                                                  AS [ExternalPracticalStatus]                                                        
        , 'Imported'                                                                                                            AS [Remarks]                                        
        , 1                                                                                                                     AS [UserID]                                        
        , @CurrentDateTime                                                                                                      AS [Created]                                        
        , @CurrentDateTime                                                                                                      AS [Modified]                                        
        , NULL                                                                                                                  AS [IsExemption]                                            
        , NULL                                                                                                                  AS [ExemptionByUserID]                                                
        , NULL                                                                                                                  AS [ExemptionDateTime]                                                
        , NULL                                                                                                                  AS [ExemptionRemarks]                                                
        ,[dbo].[EXM_ExamWiseSubject].[Credit]                                                                                   AS [Credit]                                        
        , NULL                                                                                                                  AS [GracingMark]                                            
        , NULL                                                                                                                  AS [IsShowResult]                                            
        , NULL                                                                                                                  AS [IsShowExemptionInResult]                                                        
        , NULL                                                                                                                  AS [IsShowResultInExemption]                                                        
        ,[dbo].[EXL_ExamStudentSubjectL].[GradePoint]                                                                           AS [GradePoint]                                            
        ,[dbo].[EXL_ExamStudentSubjectL].[CreditPoint]                                                                          AS [CreditPoint]                                            
        ,[dbo].[EXL_ExamStudentSubjectL].[ResultRemarks]                                                                        AS [ResultRemarks]                                            
        ,[dbo].[EXM_ExamWiseSubject].[MarkVivaVoce]                                                                             AS [MarkESEVivaVoceMax]                                                    
        ,[dbo].[EXL_ExamStudentSubjectL].[ExternalVivaVoceObtainedMark]                                                         AS [MarkESEVivaVoceObtained]                                                        
        , 0                                                                                                                     AS [IsDetainedDueToBackLog]                                                        
        , NULL                                                                                                                  AS [MarkESETheorySectionC]                                                    
        , NULL                                                                                                                  AS [MarkESETheorySectionD]                                                    
        , NULL                                                                                                                  AS [MarkESETheorySectionE]                                                    
        , NULL                                                                                                                  AS [CurriculumWiseSubjectID]                                                        
        , NULL                                                                                                                  AS [GradeSystemID]                                            
        , NULL                                                                                                                  AS [TheoryAttendancePCT]                                                    
        , NULL                                                                                                                  AS [PracticalAttendancePCT]                                                        
        , NULL                                                                                                                  AS [ExamStudentSubjectLID]                                                    
        , NULL                                                                                                                  AS [StudentNotAppearingReasonID]                                                            
        , NULL                                                                                                                  AS [StudentSubjectNotAppearingReasonID]                                                                    
        , NULL                                                                                                                  AS [ExamWiseFailReasonID]                                                    
        , [dbo].[EXL_ExamStudentSubjectL].[ExternalTheoryGradeID]                                                               AS [ExternalTheoryGradeID]                                                    
        , NULL                                                                                                                  AS [ExternalPracticalGradeID]                                                        
        ,[dbo].[EXL_ExamStudentSubjectL].[InternalTheoryGradeID]                                                                AS [InternalTheoryGradeID]                                                    
        , NULL                                                                                                                  AS [InternalPracticalGradeID]                                                        
        ,CASE WHEN ISNULL([dbo].[SUB_Subject].[MarkESETheory], 0) > 0
            THEN [dbo].[EXL_ExamStudentSubjectL].[ExternalTheoryGrade]
            ELSE NULL
        END                                                                                                                     AS [ExternalTheoryGradeName]                                                        
        ,CASE WHEN [dbo].[EXL_ExamStudentSubjectL].[PracticalGrade] IS NOT NULL
                AND  ISNULL([dbo].[SUB_Subject].[MarkESEPractical], 0) > 0
                THEN [dbo].[EXL_ExamStudentSubjectL].[PracticalGrade]
                ELSE NULL
        END                                                                                                                     AS [ExternalPracticalGradeName]                                                            
        ,CASE WHEN ISNULL([dbo].[SUB_Subject].[MarkCETheory], 0) > 0
            THEN [dbo].[EXL_ExamStudentSubjectL].[InternalTheoryGrade]
            ELSE NULL
        END                                                                                                                   AS [InternalTheoryGradeName]                                                        
        , CASE WHEN [dbo].[EXL_ExamStudentSubjectL].[PracticalGrade] IS NOT NULL
                    AND  ISNULL([dbo].[SUB_Subject].[MarkCEPractical], 0) > 0
                    THEN [dbo].[EXL_ExamStudentSubjectL].[PracticalGrade]
                    ELSE NULL
                END                                                                                                             AS [InternalPracticalGradeName]                                                            
        , NULL                                                                                                                  AS [IsEligibleForTheoryExam]                                                        
        , NULL                                                                                                                  AS [IsEligibleForPracticalExam]                                                            
        , NULL                                                                                                                  AS [MarkTotalOutOf100]                                                
        , NULL                                                                                                                  AS [MarkExternalTheoryObtainedOriginal]                                                                    
        , NULL                                                                                                                  AS [MarkExternalPracticalObtainedOriginal]                                                                    
        , NULL                                                                                                                  AS [MarkInternalPracticalElectiveObtained]                                                                    
        , NULL                                                                                                                  AS [MarkInternalPracticalElectiveObtainedGracing]                                                                            
        , NULL                                                                                                                  AS [MarkInternalMAX]                                                
        , NULL                                                                                                                  AS [ResultPCTExternalTheory]                                                        
        , NULL                                                                                                                  AS [ResultPCTExternalPractical]                                                            
        , NULL                                                                                                                  AS [ResultPCTInternalTheory]                                                        
        , NULL                                                                                                                  AS [ResultPCTInternalPractical]                                                            
        , NULL                                                                                                                  AS [InternalPracticalElectiveStatus]                                                                
        , NULL                                                                                                                  AS [ExternalVivaVoceStatus]                                                        
        , NULL                                                                                                                  AS [MarkInternalPracticalElectiveObtainedOriginal]                                                                            
        ,[dbo].[EXM_ExamWiseSubject].[MarkCEPracticalElective]                                                                    AS [MarkInternalPracticalElectiveMAX]                                                                
        ,[dbo].[EXM_ExamWiseSubject].[PassingMarkCEPracticalElective]                                                           AS [MarkInternalPracticalElectivePassing]                                                                    
        
        ,CASE WHEN ISNULL([EXL_ExamStudentSubjectL].[TheoryMaxMark],0) > 0
                THEN [EXL_ExamStudentSubjectL].[TheoryMaxMark]
                ELSE ISNULL([SUB_Subject].[MarkCETheory], 0) + ISNULL([SUB_Subject].[MarkESETheory], 0)
            END                                                                                                                 AS [MarkTheoryMax]                                            
        ,CASE WHEN ISNULL([EXL_ExamStudentSubjectL].[PracticalMaxMark],0) > 0
                THEN [EXL_ExamStudentSubjectL].[PracticalMaxMark]
                ELSE ISNULL([SUB_Subject].[MarkCEPractical], 0) + ISNULL([SUB_Subject].[MarkCEPractical], 0)
            END                                                                                                                 AS [MarkPracticalMax]                                                
        ,CASE WHEN ISNULL([EXL_ExamStudentSubjectL].[TheoryPassingMark], 0) > 0
                THEN [EXL_ExamStudentSubjectL].[TheoryPassingMark]
                ELSE ISNULL([SUB_Subject].[PassingMarkCETheory], 0) + ISNULL([SUB_Subject].[PassingMarkESETheory], 0)
            END                                                                                                                 AS [MarkTheoryPassing]                                                
        ,CASE WHEN ISNULL([EXL_ExamStudentSubjectL].[PracticalPassingMark], 0) > 0
                THEN [EXL_ExamStudentSubjectL].[PracticalPassingMark]
                ELSE ISNULL([SUB_Subject].[PassingMarkCEPractical], 0) + ISNULL([SUB_Subject].[PassingMarkESEPractical], 0)
            END                                                                                                                 AS [MarkPracticalPassing]                                                    
        
        ,NULL                                                                                        AS [PassedInMonthYear]                                                
        ,NULL                                                                           AS [MarkCETheoryAttendanceObtained]                                                                
        ,NULL                                                                         AS [MarkGracingAllotedExternalTheory]                                                                
        ,NULL                                                                         AS [MarkGracingAllotedInternalTheory]                                                                
        ,NULL                                                                      AS [MarkGracingAllotedExternalPractical]                                                                    
        ,NULL                                                                      AS [MarkGracingAllotedInternalPractical]                                                                    
        ,NULL                                                              AS [MarkGracingAllotedInternalPracticalElective]                                                                            
        ,NULL                                                                               AS [MarkGracingAllotedVivaVoce]                                                            
        ,NULL                                                                                                AS [GNRemarks]                                        
        ,NULL                                                                                 AS [oldCurriculumWiseSubject]                                                        
        ,[dbo].[EXL_ExamStudentSubjectL].[PracticalGradeID]                                                                                         AS [PracticalGradeID]                                                
        ,[dbo].[EXL_ExamStudentSubjectL].[PracticalGrade]                                                                       AS [PracticalGradeName]                                                    
        ,NULL                                                                                   AS [SubjectConvenerGradeID]                                                        
        ,NULL                                                                                 AS [SubjectConvenerGradeName]                                                        
        ,NULL                                                                                                 AS [Sequence]                                        
        ,NULL                                                                       AS [GNPrevious_CurriculumWiseSubjectID]                                                                    
        ,NULL                                                                                           AS [IsSystemLocked]                                                
        ,NULL                                                                                                 AS [TotalTheoryMarksAsPerCredit]                                                            
        ,NULL                                                                                              AS [TotalPracticalMarksAsPerCredit]                                                                
        ,NULL                                                                                                       AS [TotalMarksAsPerCredit]                                                    


	FROM [dbo].[EXL_ExamL]

    INNER JOIN [dbo].[EXL_ExamStudentL]
		ON [dbo].[EXL_ExamStudentL].[ExamLID] = [dbo].[EXL_ExamL].[ExamLID]

	INNER JOIN [dbo].[EXL_ExamStudentSubjectL]
		ON [dbo].[EXL_ExamStudentSubjectL].[ExamLID] = [dbo].[EXL_ExamL].[ExamLID]
		AND [dbo].[EXL_ExamStudentSubjectL].[ExamStudentLID] = [dbo].[EXL_ExamStudentL].[ExamStudentLID]
		AND [dbo].[EXL_ExamStudentSubjectL].[StudentID] = [dbo].[EXL_ExamStudentL].[StudentID]
        AND [dbo].[EXL_ExamStudentSubjectL].[StudentID] IS NOT NULL
        AND [dbo].[EXL_ExamStudentSubjectL].[SubjectID] IS NOT NULL
        -- AND [dbo].[EXL_ExamStudentSubjectL].[GradeName] <> 'Ex'

        --*-------------------------------------------------------
        AND [dbo].[EXL_ExamL].[ExamLID] IN (_listOfExamLIDs_) --|
        --*-------------------------------------------------------
	-------------------------------------------------------------- Added due to specific condition ------------------------------- 
		-- AND [dbo].[EXL_ExamStudentSubjectL].[GradeName] IS NOT NULL
	-------------------------------------------------------------- Added due to specific condition ------------------------------- 

	INNER JOIN [dbo].[EXM_Exam]
		ON [dbo].[EXM_Exam].[ExamID] = [dbo].[EXL_ExamL].[MappedExamID]

	INNER JOIN [dbo].[STU_Student]
		ON [dbo].[STU_Student].[StudentID] = [dbo].[EXL_ExamStudentSubjectL].[StudentID]


	INNER JOIN (SELECT
                    [dbo].[EXL_ExamStudentSubjectL].[ExamStudentSubjectLID]
                    ,[dbo].[EXL_ExamStudentSubjectL].[StudentID]
                    ,[dbo].[EXL_ExamStudentSubjectL].[SubjectID]
                    ,[dbo].[EXL_ExamL].[Semester]
                    ,[dbo].[EXL_ExamStudentSubjectL].[AttemptNo]
                    ,ROW_NUMBER() OVER (PARTITION BY [dbo].[EXL_ExamStudentSubjectL].[StudentID], [dbo].[EXL_ExamStudentSubjectL].[SubjectID], [dbo].[EXL_ExamL].[Semester] ORDER BY [dbo].[EXL_ExamStudentSubjectL].[AttemptNo] DESC) AS ANo
                    FROM [dbo].[EXL_ExamStudentSubjectL]

                    INNER JOIN EXL_ExamL
                        ON EXL_ExamL.ExamLID = [dbo].[EXL_ExamStudentSubjectL].[ExamLID]

                    WHERE [dbo].[EXL_ExamStudentSubjectL].[StudentID] IS NOT NULL
                    AND [dbo].[EXL_ExamStudentSubjectL].[SubjectID] IS NOT NULL
                    -- AND [dbo].[EXL_ExamStudentSubjectL].[GradeName] <> 'Ex'
            ) AS A
		ON A.[StudentID] = [dbo].[EXL_ExamStudentSubjectL].[StudentID]
			AND A.[SubjectID] = [dbo].[EXL_ExamStudentSubjectL].[SubjectID]
			AND A.[ExamStudentSubjectLID] = [dbo].[EXL_ExamStudentSubjectL].[ExamStudentSubjectLID]
			-- AND [dbo].[EXL_ExamStudentSubjectL].[GradeName] IS NOT NULL
			AND A.ANo = 1

	INNER JOIN [dbo].[EXM_ExamWiseSubject]
		ON [dbo].[EXM_ExamWiseSubject].[ExamID] = [dbo].[EXL_ExamL].[MappedExamID]
			AND [dbo].[EXM_ExamWiseSubject].[SubjectID] = [dbo].[EXL_ExamStudentSubjectL].[SubjectID]

	INNER JOIN [INS_Course]
		ON [INS_Course].[CourseID] = [EXL_ExamL].[CourseID]

    INNER JOIN [SUB_Subject]
        ON [SUB_Subject].[SubjectID] = [dbo].[EXL_ExamStudentSubjectL].[SubjectID]

	LEFT OUTER JOIN [dbo].[EXM_StudentWiseSubject]
		ON [dbo].[EXM_StudentWiseSubject].[StudentID] = [dbo].[EXL_ExamStudentSubjectL].[StudentID]
			AND [dbo].[EXM_StudentWiseSubject].[Semester] = [dbo].[EXL_ExamL].[Semester]
			AND [dbo].[EXM_StudentWiseSubject].[SubjectID] = [dbo].[EXL_ExamStudentSubjectL].[SubjectID]

	WHERE [dbo].[EXM_StudentWiseSubject].[StudentWiseSubjectID] IS NULL

	ORDER BY [dbo].[EXL_ExamStudentSubjectL].[StudentID],
	[dbo].[EXL_ExamL].[Semester],
	[dbo].[EXL_ExamStudentSubjectL].[SubjectID]







SELECT
	*
FROM @dtStudentWiseSubject