DECLARE	@CurrentDateTime AS DATETIME = GETDATE()

DECLARE @dtResultGrade AS TABLE (

     ExamID	                                                int
    ,ResultStudentID	                                    int
    ,StudentID	                                            int
    ,EnrollmentNo	                                        nvarchar(50)
    ,SubjectID	                                            int
    ,Mark	                                                int
    ,GradeID	                                            int
    ,GradeName	                                            nvarchar(250)
    ,IsPass	                                                bit
    ,<PERSON><PERSON>	                                            nvarchar(500)
    ,UserID	                                                int
    ,Created	                                            datetime
    ,Modified	                                            datetime
    ,IsFailInInternalTheory	                                bit
    ,IsFailInInternalPractical	                            bit
    ,IsFailInExternalTheory	                                bit
    ,IsFailInExternalPractical	                            bit
    ,OldMark	                                            int
    ,OldGradeID	                                            int
    ,OldGradeName	                                        nvarchar(250)
    ,GradeNameBeforeUFM	                                    nvarchar(250)
    ,GradeIDBeforeUFM	                                    int
    ,IsPassBeforeUFM	                                    bit
    ,MarkInternalTheoryMAX	                                int
    ,MarkInternalPracticalMAX	                            int
    ,MarkExternalTheoryMAX	                                int
    ,MarkExternalPracticalMAX	                            int
    ,MarkTotalTheoryMAX	                                    int
    ,MarkTotalPracticalMAX	                                int
    ,MarkInternalTheoryPassing	                            int
    ,MarkInternalPracticalPassing	                        int
    ,MarkExternalTheoryPassing	                            int
    ,MarkExternalPracticalPassing	                        int
    ,MarkTotalTheoryPassing	                                int
    ,MarkTotalPracticalPassing	                            int
    ,MarkInternalTheoryObtained	                            int
    ,MarkInternalPracticalObtained	                        int
    ,MarkExternalTheoryObtained	                            int
    ,MarkExternalPracticalObtained	                        int
    ,MarkTotalTheoryObtained	                            int
    ,MarkTotalPracticalObtained	                            int
    ,MarkTotalObtained	                                    int
    ,MarkTotalMAX	                                        int
    ,Credit	                                                decimal(10, 2)
    ,IsAbsentInESEPractical	                                bit
    ,IsFailInTheoryTotal	                                bit
    ,IsFailInPracticalTotal	                                bit
    ,IsFailDueToLowAttendance	                            bit
    ,GradePoint	                                            decimal(10, 2)
    ,CreditPoint	                                        decimal(10, 2)
    ,OldGradePoint	                                        decimal(10, 2)
    ,OldCreditPoint	                                        decimal(10, 2)
    ,ResultRemarks	                                        nvarchar(500)
    ,MarkExternalVivaVoceMAX	                            int
    ,MarkExternalVivaVoceObtained	                        int
    ,AttemptCount	                                        int
    ,PreviousInternalTheoryStatus	                        nvarchar(20)
    ,PreviousInternalPracticalStatus	                    nvarchar(20)
    ,PreviousExternalTheoryStatus	                        nvarchar(20)
    ,PreviousExternalPracticalStatus	                    nvarchar(20)
    ,PreviousMarkInternalTheoryObtained	                    int
    ,PreviousMarkInternalPracticalObtained	                int
    ,PreviousMarkExternalTheoryObtained	                    int
    ,PreviousMarkExternalPracticalObtained	                int
    ,PreviousMarkTotalTheoryObtained	                    int
    ,PreviousMarkTotalPracticalObtained	                    int
    ,PreviousMarkTotalObtained	                            int
    ,PreviousGradeID	                                    int
    ,PreviousGradeName	                                    nvarchar(50)
    ,PreviousGradePoint	                                    decimal(10, 2)
    ,PreviousCreditPoint	                                decimal(10, 2)
    ,ResultPCT	                                            decimal(10, 2)
    ,IsFailOverall	                                        bit
    ,ExamWiseFailReasonID	                                int
    ,MarkGracingExternalTheory	                            int
    ,MarkGracingExternalPractical	                        int
    ,MarkExternalPracticalObtainedOriginal	                int
    ,MarkExternalTheoryObtainedOriginal	                    int
    ,MarkTotalTheoryObtainedOriginal	                    int
    ,MarkTotalPracticalObtainedOriginal	                    int
    ,InternalPracticalElectiveMarkObtained	                int
    ,IsEligibleForTheoryExam	                            bit
    ,IsEligibleForPracticalExam	                            bit
    ,MarkESETheorySectionA	                                decimal(10, 2)
    ,MarkESETheorySectionB	                                decimal(10, 2)
    ,MarkESETheorySectionC	                                decimal(10, 2)
    ,MarkESETheorySectionD	                                decimal(10, 2)
    ,MarkESETheorySectionE	                                decimal(10, 2)
    ,MarkTotalOutOf100	                                    decimal(10, 2)
    ,MarkInternalTheoryObtainedOriginal	                    int
    ,MarkInternalPracticalObtainedOriginal	                int
    ,MarkInternalPracticalElectiveObtained	                int
    ,MarkInternalPracticalElectiveObtainedGracing	        int
    ,MarkInternalMAX	                                    int
    ,UFMPunishmentID	                                    int
    ,GracingMark	                                        int
    ,ResultPCTExternalTheory	                            decimal(10, 2)
    ,ResultPCTExternalPractical	                            decimal(10, 2)
    ,ResultPCTInternalTheory	                            decimal(10, 2)
    ,ResultPCTInternalPractical	                            decimal(10, 2)
    ,ExternalTheoryGradeID	                                int
    ,ExternalPracticalGradeID	                            int
    ,InternalTheoryGradeID	                                int
    ,InternalPracticalGradeID	                            int
    ,ExternalTheoryGradeName	                            nvarchar(20)
    ,ExternalPracticalGradeName	                            nvarchar(20)
    ,InternalTheoryGradeName	                            nvarchar(20)
    ,InternalPracticalGradeName	                            nvarchar(20)
    ,IsFailInInternalPracticalElective	                    bit
    ,IsFailInExternalVivaVoce	                            bit
    ,IsAbsentInExternalVivaVoce	                            bit
    ,MarkTotalInternalMAX	                                int
    ,ResultClassID	                                        int
    ,MarkGracingAllotedInternalTheory	                    int
    ,MarkGracingAllotedInternalPractical	                int
    ,MarkGracingAllotedInternalPracticalElective	        int
    ,MarkGracingAllotedExternalTheory	                    int
    ,MarkGracingAllotedExternalPractical	                int
    ,MarkGracingAllotedVivaVoce	                            int
    ,PracticalGradeID	                                    int
    ,PracticalGradeName	                                    nvarchar(50)
    ,SubjectConvenerGradeID	                                int
    ,SubjectConvenerGradeName	                            nvarchar(10)
    ,MarkTotalObtainedOriginal	                            int
    ,IsFailDueToGradeBoundary	                            bit
    ,IsGradable	                                            bit
    ,IsSystemLocked	                                        bit
    ,TotalTheoryMarksAsPerCredit	                        decimal(10, 2)
    ,TotalPracticalMarksAsPerCredit	                        decimal(10, 2)
    ,TotalMarksAsPerCredit	                                decimal(10, 2)

)

INSERT INTO @dtResultGrade

	SELECT

		 [EXL_ExamL].[MappedExamID]                                                                         AS [ExamID]
        ,[EXM_ResultStudent].[ResultStudentID]                                                              AS [ResultStudentID]
        ,[EXL_ExamStudentSubjectL].[StudentID]                                                              AS [StudentID]
        ,[EXL_ExamStudentSubjectL].[EnrollmentNo]                                                           AS [EnrollmentNo]
        ,[EXL_ExamStudentSubjectL].[SubjectID]                                                              AS [SubjectID]


        ,CASE WHEN ISNULL([dbo].[EXL_ExamStudentSubjectL].[TotalObtainedMark],0) > 0
            THEN [EXL_ExamStudentSubjectL].[TotalObtainedMark]
            ELSE ISNULL([dbo].[EXL_ExamStudentSubjectL].[InternalTheoryObtainedMark], 0) + 
                 ISNULL([dbo].[EXL_ExamStudentSubjectL].[ExternalTheoryObtainedMark], 0) + 
                 ISNULL([dbo].[EXL_ExamStudentSubjectL].[InternalPracticalObtainedMark], 0) +
                 ISNULL([dbo].[EXL_ExamStudentSubjectL].[ExternalPracticalObtainedMark], 0) +
                 ISNULL([dbo].[EXL_ExamStudentSubjectL].[ExternalVivaVoceObtainedMark], 0)
        END                                                                                             AS [Mark]

        ,[EXL_ExamStudentSubjectL].[GradeID]                                                                AS [GradeID]
        ,[EXL_ExamStudentSubjectL].[GradeName]                                                              AS [GradeName]
        ,CASE WHEN 
            ISNULL([dbo].[EXL_ExamStudentSubjectL].[PassFail], 'Fail') = 'Pass'
              THEN 1
              ELSE 0  
            END                                                                                             AS [IsPass]
          
        , 'Imported'                                                                                         AS [Remarks]
        , 1                                                                                                  AS [UserID]
        ,@CurrentDateTime                                                                                   AS [Created]
        ,@CurrentDateTime                                                                                   AS [Modified]
        ,CASE WHEN ISNULL([dbo].[SUB_Subject].[MarkCETheory],0) > 0
                AND  [dbo].[EXL_ExamStudentSubjectL].[InternalTheoryGrade] IS NOT NULL
                AND  [dbo].[EXL_ExamStudentSubjectL].[InternalTheoryGrade] = 'F'
                THEN 1
                ELSE 0
            END                                                                                             AS [IsFailInInternalTheory]
        ,CASE WHEN ISNULL([dbo].[SUB_Subject].[MarkCEPractical],0) > 0
                AND  [dbo].[EXL_ExamStudentSubjectL].[PracticalGrade] IS NOT NULL
                AND  [dbo].[EXL_ExamStudentSubjectL].[PracticalGrade] = 'F'
                THEN 1
                ELSE 0
            END                                                                                             AS [IsFailInInternalPractical]
        ,CASE WHEN ISNULL([dbo].[SUB_Subject].[MarkESETheory],0) > 0
                AND  [dbo].[EXL_ExamStudentSubjectL].[ExternalTheoryGrade] IS NOT NULL
                AND  [dbo].[EXL_ExamStudentSubjectL].[ExternalTheoryGrade] = 'F'
                THEN 1
                ELSE 0
            END                                                                                             AS [IsFailInExternalTheory]
        ,CASE WHEN ISNULL([dbo].[SUB_Subject].[MarkESEPractical],0) > 0
                AND  [dbo].[EXL_ExamStudentSubjectL].[PracticalGrade] IS NOT NULL
                AND  [dbo].[EXL_ExamStudentSubjectL].[PracticalGrade] = 'F'
                THEN 1
                ELSE 0
            END                                                                                             AS [IsFailInExternalPractical]
        ,NULL                                                                                    AS [OldMark]
        ,NULL                                                                                 AS [OldGradeID]
        ,NULL                                                                               AS [OldGradeName]
        ,NULL                                                                         AS [GradeNameBeforeUFM]
        ,NULL                                                                           AS [GradeIDBeforeUFM]
        ,NULL                                                                            AS [IsPassBeforeUFM]
        ,[SUB_Subject].[MarkCETheory]                                                                       AS [MarkInternalTheoryMAX]
        ,[SUB_Subject].[MarkCEPractical]                                                                    AS [MarkInternalPracticalMAX]
        ,[SUB_Subject].[MarkESETheory]                                                                      AS [MarkExternalTheoryMAX]
        ,[SUB_Subject].[MarkESEPractical]                                                                   AS [MarkExternalPracticalMAX]

        ,CASE WHEN ISNULL([EXL_ExamStudentSubjectL].[TheoryMaxMark],0) > 0
                THEN [EXL_ExamStudentSubjectL].[TheoryMaxMark]
                ELSE ISNULL([SUB_Subject].[MarkCETheory], 0) + ISNULL([SUB_Subject].[MarkESETheory], 0)
            END                                                                                             AS [MarkTotalTheoryMAX]
        ,CASE WHEN ISNULL([EXL_ExamStudentSubjectL].[PracticalMaxMark],0) > 0
                THEN [EXL_ExamStudentSubjectL].[PracticalMaxMark]
                ELSE ISNULL([SUB_Subject].[MarkCEPractical], 0) + ISNULL([SUB_Subject].[MarkCEPractical], 0)
            END                                                                                             AS [MarkTotalPracticalMAX]
        ,[SUB_Subject].[PassingMarkCETheory]                                                                AS [MarkInternalTheoryPassing]
        ,[SUB_Subject].[PassingMarkCEPractical]                                                             AS [MarkInternalPracticalPassing]
        ,[SUB_Subject].[PassingMarkESETheory]                                                               AS [MarkExternalTheoryPassing]
        ,[SUB_Subject].[PassingMarkESEPractical]                                                            AS [MarkExternalPracticalPassing]
        ,CASE WHEN ISNULL([EXL_ExamStudentSubjectL].[TheoryPassingMark], 0) > 0
                THEN [EXL_ExamStudentSubjectL].[TheoryPassingMark]
                ELSE ISNULL([SUB_Subject].[PassingMarkCETheory], 0) + ISNULL([SUB_Subject].[PassingMarkESETheory], 0)
            END                                                                                             AS [MarkTotalTheoryPassing]
        ,CASE WHEN ISNULL([EXL_ExamStudentSubjectL].[PracticalPassingMark], 0) > 0
                THEN [EXL_ExamStudentSubjectL].[PracticalPassingMark]
                ELSE ISNULL([SUB_Subject].[PassingMarkCEPractical], 0) + ISNULL([SUB_Subject].[PassingMarkESEPractical], 0)
            END                                                                                             AS [MarkTotalPracticalPassing]
        ,CASE WHEN ISNULL([dbo].[SUB_Subject].MarkCETheory, 0) > 0
                THEN [EXL_ExamStudentSubjectL].[InternalTheoryObtainedMark]
                ELSE NULL
            END                                                                                             AS [MarkInternalTheoryObtained]
        ,CASE WHEN ISNULL([dbo].[SUB_Subject].MarkCEPractical,0) > 0
                THEN [EXL_ExamStudentSubjectL].[InternalPracticalObtainedMark] 
                ELSE NULL
            END                                                                                             AS [MarkInternalPracticalObtained]
        ,CASE WHEN ISNULL([dbo].[SUB_Subject].MarkESETheory,0) > 0
                THEN [EXL_ExamStudentSubjectL].[ExternalTheoryObtainedMark] 
                ELSE NULL
            END                                                                                                 AS [MarkExternalTheoryObtained]
        ,CASE WHEN ISNULL([dbo].[SUB_Subject].MarkESEPractical,0) > 0
                THEN [EXL_ExamStudentSubjectL].[ExternalPracticalObtainedMark]
                ELSE NULL
            END                                                                                             AS [MarkExternalPracticalObtained]
        ,CASE WHEN ISNULL([EXL_ExamStudentSubjectL].[TheoryObtainedMark], 0) > 0
                THEN [EXL_ExamStudentSubjectL].[TheoryObtainedMark]
                ELSE ISNULL([EXL_ExamStudentSubjectL].[InternalTheoryObtainedMark], 0) + 
                     ISNULL([EXL_ExamStudentSubjectL].[ExternalTheoryObtainedMark], 0)
            END                                                                                             AS [MarkTotalTheoryObtained]
        ,CASE WHEN ISNULL([EXL_ExamStudentSubjectL].[PracticalObtainedMark], 0) > 0
                THEN [EXL_ExamStudentSubjectL].[PracticalObtainedMark]
                ELSE ISNULL([EXL_ExamStudentSubjectL].[InternalPracticalObtainedMark], 0) + 
                     ISNULL([EXL_ExamStudentSubjectL].[ExternalPracticalObtainedMark], 0)
            END                                                                                             AS [MarkTotalPracticalObtained]
        ,[EXL_ExamStudentSubjectL].[TotalObtainedMark]                                                     AS [MarkTotalObtained]
        ,[SUB_Subject].[MarkTotal]                                                                          AS [MarkTotalMAX]
        ,[SUB_Subject].[Credit]                                                                             AS [Credit]
        , 0                                                                                                 AS [IsAbsentInESEPractical]
        ,CASE WHEN ISNULL([dbo].[SUB_Subject].[MarkCETheory],0) > 0
                AND  [dbo].[EXL_ExamStudentSubjectL].[InternalTheoryGrade] IS NOT NULL
                AND  [dbo].[EXL_ExamStudentSubjectL].[InternalTheoryGrade] = 'F'
                THEN 1
                WHEN ISNULL([dbo].[SUB_Subject].[MarkESETheory],0) > 0
                AND  [dbo].[EXL_ExamStudentSubjectL].[ExternalTheoryGrade] IS NOT NULL
                AND  [dbo].[EXL_ExamStudentSubjectL].[ExternalTheoryGrade] = 'F'
                THEN 1
                ELSE 0
            END                                                                                             AS [IsFailInTheoryTotal]
        ,CASE WHEN (
                    ISNULL([dbo].[SUB_Subject].[MarkCEPractical],0) > 0 
                        OR 
                    ISNULL([dbo].[SUB_Subject].[MarkESEPractical],0) > 0
                  )
                AND  [dbo].[EXL_ExamStudentSubjectL].[PracticalGrade] IS NOT NULL
                AND  [dbo].[EXL_ExamStudentSubjectL].[PracticalGrade] = 'F'
                THEN 1
                ELSE 0
            END                                                                                             AS [IsFailInPracticalTotal]
        , 0                                                                                                 AS [IsFailDueToLowAttendance]
        ,[EXM_Grade].[GradePoint]                                                                           AS [GradePoint]
        ,CAST(([dbo].[EXM_Grade].[GradePoint] * [dbo].[SUB_Subject].[Credit]) AS decimal(5,2))              AS [CreditPoint]
        , NULL                                                                              AS [OldGradePoint]
        , NULL                                                                             AS [OldCreditPoint]
        ,[EXL_ExamStudentSubjectL].[ResultRemarks]                                                          AS [ResultRemarks]
        ,[SUB_Subject].[MarkVivaVoce]                                                                       AS [MarkExternalVivaVoceMAX]
        ,[EXL_ExamStudentSubjectL].[ExternalVivaVoceObtainedMark]                                                               AS [MarkExternalVivaVoceObtained]
        ,[EXL_ExamStudentSubjectL].[AttemptNo]                                                              AS [AttemptCount]
        ,NULL                                                               AS [PreviousInternalTheoryStatus]
        ,NULL                                                            AS [PreviousInternalPracticalStatus]
        ,NULL                                                               AS [PreviousExternalTheoryStatus]
        ,NULL                                                            AS [PreviousExternalPracticalStatus]
        ,NULL                                                         AS [PreviousMarkInternalTheoryObtained]
        ,NULL                                                      AS [PreviousMarkInternalPracticalObtained]
        ,NULL                                                         AS [PreviousMarkExternalTheoryObtained]
        ,NULL                                                      AS [PreviousMarkExternalPracticalObtained]
        ,NULL                                                            AS [PreviousMarkTotalTheoryObtained]
        ,NULL                                                         AS [PreviousMarkTotalPracticalObtained]
        ,NULL                                                                  AS [PreviousMarkTotalObtained]
        ,NULL                                                                            AS [PreviousGradeID]
        ,NULL                                                                          AS [PreviousGradeName]
        ,NULL                                                                         AS [PreviousGradePoint]
        ,NULL                                                                        AS [PreviousCreditPoint]
        ,NULL                                                                                  AS [ResultPCT]
        ,NULL                                                                                             AS [IsFailOverall]
        ,NULL                                                                       AS [ExamWiseFailReasonID]
        ,NULL                                                                  AS [MarkGracingExternalTheory]
        ,NULL                                                               AS [MarkGracingExternalPractical]
        ,[EXL_ExamStudentSubjectL].[ExternalPracticalObtainedMark]                                                      AS [MarkExternalPracticalObtainedOriginal]
        ,[EXL_ExamStudentSubjectL].[ExternalTheoryObtainedMark]                                                         AS [MarkExternalTheoryObtainedOriginal]
        ,[EXL_ExamStudentSubjectL].[TheoryObtainedMark]                                                            AS [MarkTotalTheoryObtainedOriginal]
        ,[EXL_ExamStudentSubjectL].[PracticalObtainedMark]                                                         AS [MarkTotalPracticalObtainedOriginal]
        ,NULL                                                      AS [InternalPracticalElectiveMarkObtained]
        ,NULL                                                                    AS [IsEligibleForTheoryExam]
        ,NULL                                                                 AS [IsEligibleForPracticalExam]
        ,[EXL_ExamStudentSubjectL].[ExternalTheorySectionAObtainedMark]                                                                      AS [MarkESETheorySectionA]
        ,[EXL_ExamStudentSubjectL].[ExternalTheorySectionBObtainedMark]                                                                      AS [MarkESETheorySectionB]
        ,[EXL_ExamStudentSubjectL].[ExternalTheorySectionCObtainedMark]                                                                      AS [MarkESETheorySectionC]
        ,NULL                                                                      AS [MarkESETheorySectionD]
        ,NULL                                                                      AS [MarkESETheorySectionE]
        ,NULL                                                                          AS [MarkTotalOutOf100]
        ,[EXL_ExamStudentSubjectL].[InternalTheoryObtainedMark]                                                         AS [MarkInternalTheoryObtainedOriginal]
        ,[EXL_ExamStudentSubjectL].[InternalPracticalObtainedMark]                                                      AS [MarkInternalPracticalObtainedOriginal]
        ,NULL                                                      AS [MarkInternalPracticalElectiveObtained]
        ,NULL                                               AS [MarkInternalPracticalElectiveObtainedGracing]
        ,[EXL_ExamStudentSubjectL].[InternalMaxMark]                                                                            AS [MarkInternalMAX]
        ,NULL                                                                            AS [UFMPunishmentID]
        ,NULL                                                                                AS [GracingMark]
        ,NULL                                                                    AS [ResultPCTExternalTheory]
        ,NULL                                                                 AS [ResultPCTExternalPractical]
        ,NULL                                                                    AS [ResultPCTInternalTheory]
        ,NULL                                                                 AS [ResultPCTInternalPractical]
        ,[EXL_ExamStudentSubjectL].[ExternalTheoryGradeID]                                                                      AS [ExternalTheoryGradeID]
        ,NULL                                                                   AS [ExternalPracticalGradeID]
        ,[EXL_ExamStudentSubjectL].[InternalTheoryGradeID]                                                                      AS [InternalTheoryGradeID]
        ,NULL                                                                   AS [InternalPracticalGradeID]
        ,[EXL_ExamStudentSubjectL].[ExternalTheoryGrade]                                                    AS [ExternalTheoryGradeName]
        ,NULL                                                                 AS [ExternalPracticalGradeName]
        ,[EXL_ExamStudentSubjectL].[InternalTheoryGrade]                                                    AS [InternalTheoryGradeName]
        ,NULL                                                                 AS [InternalPracticalGradeName]
        ,NULL                                                          AS [IsFailInInternalPracticalElective]
        ,NULL                                                                   AS [IsFailInExternalVivaVoce]
        ,NULL                                                                 AS [IsAbsentInExternalVivaVoce]
        ,[EXL_ExamStudentSubjectL].[InternalMaxMark]                                                                       AS [MarkTotalInternalMAX]
        ,NULL                                                                              AS [ResultClassID]
        ,NULL                                                           AS [MarkGracingAllotedInternalTheory]
        ,NULL                                                        AS [MarkGracingAllotedInternalPractical]
        ,NULL                                                AS [MarkGracingAllotedInternalPracticalElective]
        ,NULL                                                           AS [MarkGracingAllotedExternalTheory]
        ,NULL                                                        AS [MarkGracingAllotedExternalPractical]
        ,NULL                                                                 AS [MarkGracingAllotedVivaVoce]
        ,[EXL_ExamStudentSubjectL].[PracticalGradeID]                                                                           AS [PracticalGradeID]
        ,[EXL_ExamStudentSubjectL].[PracticalGrade]                                                         AS [PracticalGradeName]
        ,NULL                                                                     AS [SubjectConvenerGradeID]
        ,NULL                                                                   AS [SubjectConvenerGradeName]
        ,[EXL_ExamStudentSubjectL].[TotalObtainedMark]                                                                  AS [MarkTotalObtainedOriginal]
        ,NULL                                                                   AS [IsFailDueToGradeBoundary]
        ,NULL                                                                                 AS [IsGradable]
        , 0                                                                             AS [IsSystemLocked]
        ,NULL                                                                AS [TotalTheoryMarksAsPerCredit]
        ,NULL                                                             AS [TotalPracticalMarksAsPerCredit]
        ,NULL                                                                      AS [TotalMarksAsPerCredit]

	FROM [dbo].[EXL_ExamL]


	INNER JOIN [dbo].[EXL_ExamStudentSubjectL]
		ON [dbo].[EXL_ExamStudentSubjectL].[ExamLID] = [dbo].[EXL_ExamL].[ExamLID]

        --*-------------------------------------------------------
        AND [dbo].[EXL_ExamL].[ExamLID] IN (_listOfExamLIDs_) --|
        ---*------------------------------------------------------

    -------------------------------------------------------------- Added due to specific condition -------------------------------------------------------------- 
			-- AND [dbo].[EXL_ExamStudentSubjectL].[GradeName] IS NOT NULL
	-------------------------------------------------------------- Added due to specific condition -------------------------------------------------------------- 


	INNER JOIN [dbo].[EXM_ResultStudent]
		ON [dbo].[EXM_ResultStudent].[StudentID] = [dbo].[EXL_ExamStudentSubjectL].[StudentID]
			AND [dbo].[EXM_ResultStudent].[ExamID] = [dbo].[EXL_ExamL].[MappedExamID]
			AND [dbo].[EXL_ExamL].[MappedExamID] IS NOT NULL
			AND [dbo].[EXL_ExamStudentSubjectL].[StudentID] IS NOT NULL
			AND [dbo].[EXL_ExamStudentSubjectL].[SubjectID] IS NOT NULL
	--        AND		[dbo].[EXL_ExamStudentSubjectL].[GradeName] <> 'Ex'

	INNER JOIN [dbo].[SUB_Subject]
		ON [dbo].[SUB_Subject].[SubjectID] = [dbo].[EXL_ExamStudentSubjectL].[SubjectID]

	LEFT OUTER JOIN [dbo].[EXM_Grade]
		ON [dbo].[EXM_Grade].[GradeID] = [dbo].[EXL_ExamStudentSubjectL].[GradeID]
			AND [dbo].[EXM_Grade].[CourseID] = [dbo].[EXL_ExamL].[CourseID]
			AND [dbo].[EXM_Grade].[GradeSystemID] = [dbo].[EXL_ExamStudentSubjectL].[GradeSystemID]

	LEFT OUTER JOIN [dbo].[EXM_ResultGrade]
		ON [dbo].[EXM_ResultGrade].[ExamID] = [dbo].[EXL_ExamL].[MappedExamID]
			AND [dbo].[EXM_ResultGrade].[ResultStudentID] = [dbo].[EXM_ResultStudent].[ResultStudentID]
			AND [dbo].[EXM_ResultGrade].[StudentID] = [dbo].[EXL_ExamStudentSubjectL].[StudentID]
			AND [dbo].[EXM_ResultGrade].[SubjectID] = [dbo].[EXL_ExamStudentSubjectL].[SubjectID]

	WHERE [dbo].[EXM_ResultGrade].[ResultGradeID] IS NULL







SELECT
	*
FROM @dtResultGrade