using System.Data;
using ClosedXML.Excel;
using DataTransformation.Common;

namespace DataTransformation.ITMClient
{
    public class HelperFunctions
    {
        public static readonly string logFilePath = ITMConfig.LogFilePath;

		public static void ProcessDataInDirectory(string inputFolderPath, string outputFolderPath)
		{
            // Get all supported file types
            var csvFiles = Directory.GetFiles(inputFolderPath, "*.csv", System.IO.SearchOption.AllDirectories);
            var xlsxFiles = Directory.GetFiles(inputFolderPath, "*.xlsx", System.IO.SearchOption.AllDirectories);
            var xlsFiles = Directory.GetFiles(inputFolderPath, "*.xls", System.IO.SearchOption.AllDirectories);

            // Combine all files
            var allFiles = csvFiles.Concat(xlsxFiles).Concat(xlsFiles);

            // Process each file
            // Output folder hierarchy must be maintained as input given folder structure

            foreach (var file in allFiles)
            {
                ProcessFile(file, inputFolderPath, outputFolderPath);
            }
        }

        public static void ProcessFile(string inputFilePath, string inputFolderPath, string outputFolderPath)
        {
            
        }
    }
}
