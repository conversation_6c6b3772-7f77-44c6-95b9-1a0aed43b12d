using DataTransformation.Common;
using System;
using System.Data;
using System.IO;
using Microsoft.VisualBasic.FileIO;
using ClosedXML.Excel;
using OfficeOpenXml;
using System.Text;
using DataTransformation.ITMClient;

namespace DataTransformation.ITMClient
{
    public class ITMTransformationPipeline
    {
        public void Run(string inputFolderPath, string outputFolderPath)
        {
            try
            {
                HelperFunctions.ProcessDataInDirectory(inputFolderPath, outputFolderPath); 
            }
            catch (Exception ex)
            {
                throw new Exception($"ITM transformation failed: {ex.Message}", ex);
            }
        }

        
    }
}
