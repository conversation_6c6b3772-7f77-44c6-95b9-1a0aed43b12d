DECLARE @CurrentDateTime datetime = GETDATE()

SELECT
        'Entry' AS InternalMarkSubmissionMethod,
        'Imported' AS Description,
        @CurrentDateTime AS Created,
        @CurrentDateTime AS Modified,
        EXM_Exam.*
FROM EXM_Exam

LEFT OUTER JOIN  EXM_ExamConfig
ON		EXM_ExamConfig.ExamID = EXM_Exam.ExamID

WHERE EXM_ExamConfig.ExamID IS NULL

--*-----------------------------------------
AND EXM_Exam.ExamID IN (_listOfExamIDs_) --|
--*-----------------------------------------