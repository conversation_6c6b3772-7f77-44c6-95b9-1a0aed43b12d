// using System;

// namespace DataImport.ITMClient
// {
//     public class ITMImportPipeline
//     {
//         private readonly LegacyTablesImporter _legacyImporter;
//         private readonly MainTablesImporter _mainImporter;
//         private readonly FileHandler _fileHandler;

//         public ITMImportPipeline(DatabaseHandler dbHandler, string intermediateFilePath)
//         {
//             _legacyImporter = new LegacyTablesImporter(dbHandler);
//             _mainImporter = new MainTablesImporter(dbHandler);
//             _fileHandler = new FileHandler();
//         }

//         public void Run()
//         {
//             // Load intermediate data
//             var data = _fileHandler.LoadExcelFile(intermediateFilePath);

//             // Import into legacy tables
//             _legacyImporter.Import(data);

//             // Ask user if they want to proceed to main tables import
//             Console.WriteLine("Do you want to proceed to import into main tables? (y/n)");
//             string response = Console.ReadLine();

//             if (response?.ToLower() == "y")
//             {
//                 // Import into main tables
//                 _mainImporter.Import(data);
//             }

//             Console.WriteLine("ITM Data Import Complete!");
//         }
//     }
// }