DECLARE @CurrentDateTime datetime = GETDATE()

SELECT		
                DISTINCT
                'Imported' AS Remarks,
                INS_Faculty.FacultyShortName + ' - ' + EXL_ExamL.Term + ' - ' + MST_AcademicYear.AcademicYearName AS ExamSeasonName,
                EXL_ExamL.AcademicYearID,
                EXL_ExamL.FacultyID,
                EXL_ExamL.Term,
                INS_Faculty.UniversityID,
                EXL_ExamL.UserID,
                @CurrentDateTime AS Created,
                @CurrentDateTime AS Modified,
                @CurrentDateTime AS ArchivalDate
                
FROM        EXL_ExamL
INNER JOIN  INS_Faculty
ON          EXL_ExamL.FacultyID = INS_Faculty.FacultyID
--*----------------------------------------------------
AND         EXL_ExamL.ExamLID IN (0000) --|
--*----------------------------------------------------

INNER JOIN MST_AcademicYear 
ON EXL_ExamL.AcademicYearID = MST_AcademicYear.AcademicYearID
