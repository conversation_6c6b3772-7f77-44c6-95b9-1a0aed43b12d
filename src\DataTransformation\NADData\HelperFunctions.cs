using System.Data;
using ClosedXML.Excel;
using DataTransformation.Common;

namespace DataTransformation.NADData
{
    public class HelperFunctions
    {
        public static readonly string logFilePath = NADConfig.LogFilePath;

		public static void ProcessDataInDirectory(string inputFolderPath, string outputFolderPath)
		{
            // Get all supported file types
            var csvFiles = Directory.GetFiles(inputFolderPath, "*.csv", System.IO.SearchOption.AllDirectories);
            var xlsxFiles = Directory.GetFiles(inputFolderPath, "*.xlsx", System.IO.SearchOption.AllDirectories);
            var xlsFiles = Directory.GetFiles(inputFolderPath, "*.xls", System.IO.SearchOption.AllDirectories);

            // Combine all files
            var allFiles = csvFiles.Concat(xlsxFiles).Concat(xlsFiles);

            // Process each file
            // Output folder hierarchy must be maintained as input given folder structure

            foreach (var file in allFiles)
            {
                ProcessFile(file, inputFolderPath, outputFolderPath);
            }
        }

        public static void ProcessFile(string inputFilePath, string inputFolderPath, string outputFolderPath)
        {
            DataTransformation.Common.HelperFunctions.LogMessage("1. Processing file: " + inputFilePath.Substring(32), logFilePath);

            // Load the Excel file into a DataTable
            DataTable dataTable = FileHandler.ReadExcelAndCsvFile(inputFilePath);
            DataTransformation.Common.HelperFunctions.LogMessage("2. Loaded Excel File", logFilePath);

            // Get header mappings from configuration
            Dictionary<string, string> headerMapping = NADConfig.GetHeaderMappings();

            // Get columns to keep from configuration,
            List<string> columnsToKeep = NADConfig.GetColumnsToKeep();

            // filter data table by column name
            FilterDataTableByColumn(ref dataTable, columnsToKeep);
            DataTransformation.Common.HelperFunctions.LogMessage("3. Filtered Columns", logFilePath);

            // renaming header name as per our requirement for our database, [ COURSE_NAME -> CourseName ]
            RenameHeaderName(ref dataTable, headerMapping);
            DataTransformation.Common.HelperFunctions.LogMessage("4. Renamed Headers", logFilePath);

            if (NADConfig.IsColumnToRowTransformationRequired)
            {
                // Get primary headers from configuration
                List<string> primaryHeaders = NADConfig.GetPrimaryHeaders();

                // Get common secondary headers from configuration
                List<string> commonSecondaryHeaders = NADConfig.GetCommonSecondaryHeaders();

                // count maximum number of subject from datatable
                int maxSubjectCount = CountMaximumSubject(dataTable, commonSecondaryHeaders.Count);
                DataTransformation.Common.HelperFunctions.LogMessage("5. Counted Maximum Subject: " + maxSubjectCount, logFilePath);

                // Get secondary header groups from configuration
                List<List<string>> secondaryHeaderGroups = NADConfig.GetSecondaryHeaderGroups(maxSubjectCount);

                // Transform columns to rows
                dataTable = TransformColumnsToRows(dataTable, primaryHeaders, secondaryHeaderGroups, commonSecondaryHeaders);
                DataTransformation.Common.HelperFunctions.LogMessage("6. Transformed Columns", logFilePath);
            }
            else
            {
                Common.HelperFunctions.LogMessage("6. Column to Row Transformation is not required", logFilePath);
            }

            // Miscellaneous Computation :
            //      - Update Semester from Roman to Integer, 
            //      - Update ExamType to Regular (with config), 
            //      - Add Empty Column : ExamLID, ExamStudentLID and ExamStudentSubjectLID
            DataTransformation.Common.HelperFunctions.LogMessage("7. Miscellaneous Computation", logFilePath);
            try
            {
                MiscellaneousComputation(ref dataTable);
            }
            // catch invalid semester thrown error
            catch (ArgumentException ex)
            {
                Console.WriteLine("Error: " + ex.Message + " in file: " + inputFilePath);
                return;
            }

            // Append Exam Name in the beginning of datatable (ExamName = {FileName} + " Semester - " + {Semester})
            NADConfig.AppendExamNameWithManulaConfiguration(ref dataTable, inputFilePath);
            DataTransformation.Common.HelperFunctions.LogMessage("8. Appended Exam Name", logFilePath);

            DataTransformation.Common.HelperFunctions.SaveDataTableToExcelFileAsRelative(dataTable, inputFilePath, inputFolderPath, outputFolderPath);
            DataTransformation.Common.HelperFunctions.LogMessage("9. Processed file", logFilePath);
            DataTransformation.Common.HelperFunctions.LogMessage("------------------------------------------------------------------------------\n", logFilePath);
            }

        public static void RenameHeaderName(ref DataTable dataTable, Dictionary<string, string> headerMapping)
        {
            foreach (DataColumn column in dataTable.Columns)
            {
                if (headerMapping.ContainsKey(column.ColumnName))
                {
                    column.ColumnName = headerMapping[column.ColumnName];
                }
            }
        }

        public static void FilterDataTableByColumn(ref DataTable dataTable, List<string> columnsToKeep) {
            // Create a list to store columns that need to be removed
            List<DataColumn> columnsToRemove = new List<DataColumn>();
            
            // First identify all columns to remove
            foreach (DataColumn column in dataTable.Columns)
            {
                if (!columnsToKeep.Contains(column.ColumnName))
                {
                    columnsToRemove.Add(column);
                }
            }
            
            // Then remove them after the enumeration is complete
            foreach (DataColumn column in columnsToRemove)
            {
                dataTable.Columns.Remove(column);
            }
        }

        public static DataTable TransformColumnsToRows(DataTable inputTable, List<string> primaryHeaders, List<List<string>> secondaryHeaderGroups, List<string> commonSecondaryHeader)
        {
            DataTable resultTable = new DataTable();

            // Add primary columns to result table
            foreach (string primary in primaryHeaders)
            {
                resultTable.Columns.Add(primary, inputTable.Columns[primary].DataType);
            }

            // Add columns from the first secondary group (assuming all groups have the same size)
            foreach (string secondary in commonSecondaryHeader)
            {
                resultTable.Columns.Add(secondary, typeof(string));
            }

            // Process each row in the input table
            foreach (DataRow row in inputTable.Rows)
            {
                foreach (List<string> secondaryGroup in secondaryHeaderGroups)
                {
                    DataRow newRow = resultTable.NewRow();

                    // Copy primary column values
                    foreach (string primary in primaryHeaders)
                    {
                        newRow[primary] = row[primary];
                    }

                    bool isSecondaryRowContainsData = false;
                    
                    for (int i = 0; i < commonSecondaryHeader.Count; i++)
                    {
                        newRow[commonSecondaryHeader[i]] = row[secondaryGroup[i]];
                        if (!string.IsNullOrEmpty(Convert.ToString(row[secondaryGroup[i]]))) {
                            isSecondaryRowContainsData = true;
                        }
                    }

                    if (isSecondaryRowContainsData) {
                        resultTable.Rows.Add(newRow);
                    }
                }
            }

            return resultTable;
        }

        public static int CountMaximumSubject(DataTable dataTable, int commonSecondaryHeadersCount)
        {
            int subjectCount = 0;

            for (int i = 0; i < dataTable.Columns.Count; i++)
            {
                if (dataTable.Columns[i].ColumnName.StartsWith("SUB"))
                {
                    subjectCount++;
                }
            }
            return subjectCount/commonSecondaryHeadersCount;
        }

        public static void MiscellaneousComputation(ref DataTable dataTable)
        {

            // DataTableViewer.ShowDataTableInExcel(dataTable, "Misc Computation Starts");
            // Console.WriteLine("Continuing execution after Excel was closed...");

            #region Update Semester from Roman or Integer to Integer
            // update semester from roman to integer
            foreach (DataRow row in dataTable.Rows)
            {
                row["Semester"] = DataTransformation.Common.HelperFunctions.FormatSemesterValue(row["Semester"].ToString());
            }
            DataTransformation.Common.HelperFunctions.LogMessage("\t7.1 Updated Semester from Roman or Integer to Integer", logFilePath);
            #endregion Update Semester from Roman or Integer to Integer

            #region Update ExamType to Regular (with config)
            bool isExamTypeDefaultRegular = NADConfig.IsExamTypeDefaultRegular;


            if (isExamTypeDefaultRegular)
            {
                // if datatable doesn't contains exam type column, add it
                if (!dataTable.Columns.Contains("ExamType"))
                {
                    dataTable.Columns.Add("ExamType", typeof(string));
                }
                // update exam type to regular
                foreach (DataRow row in dataTable.Rows)
                {
                    row["ExamType"] = "Regular";
                }
            }
            DataTransformation.Common.HelperFunctions.LogMessage("\t7.2 Updated ExamType to Regular", logFilePath);
            #endregion Update ExamType to Regular (with config)

            #region Add Empty Column : ExamLID, ExamStudentLID, ExamStudentSubjectLID

            if (!dataTable.Columns.Contains("ExamLID"))
            {
                dataTable.Columns.Add("ExamLID");
            }
            if (!dataTable.Columns.Contains("ExamStudentLID"))
            {
                dataTable.Columns.Add("ExamStudentLID");
            }
            if (!dataTable.Columns.Contains("ExamStudentSubjectLID"))
            {
                dataTable.Columns.Add("ExamStudentSubjectLID");
            }
            DataTransformation.Common.HelperFunctions.LogMessage("\t7.3 Added Empty Column : ExamLID, ExamStudentLID, ExamStudentSubjectLID", logFilePath);

            #endregion Add Empty Column : ExamLID, ExamStudentLID, ExamStudentSubjectLID
        }

    }
}
