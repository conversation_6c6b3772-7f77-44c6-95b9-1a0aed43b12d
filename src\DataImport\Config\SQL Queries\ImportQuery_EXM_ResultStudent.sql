DECLARE @CurrentDateTime datetime = GETDATE()


DECLARE @dtResultStudent AS TABLE (
    ExamID								         int       
    ,CourseID							         int   	    
    ,InstituteID							     int   	    
    ,ProgramID							         int   	    
    ,StudentID							         int   	    
    ,EnrollmentNo						         nvarchar(50)   		    
    ,SeatNo								         nvarchar(20)       
    ,TotalMark							         int   	    
    ,OutOfMark							         int   	    
    ,PCT								         decimal(10, 2)
    ,SPI								         decimal(10, 2)
    ,RankInBranch						         int   		    
    ,FailInSubject						         int   		    
    ,Remarks								     nvarchar(500)       
    ,UserID								         int       
    ,Created								     datetime       
    ,Modified							         datetime   	    
    ,CGPA								         decimal(10, 2)       
    ,ResultClassID						         int   		    
    ,ResultDeclarationDate				         datetime   		    		
    ,IsDetainedBecauseOfAttendance		         bit   		    				
    ,ResultEffect						         nvarchar(250)   		    
    ,TotalBackLog						         int   		    
    ,OldSPI								         decimal(10, 2)       
    ,OldCGPA								     decimal(10, 2)       
    ,OldTotalMark						         int   		    
    ,OldPCT								         decimal(10, 2)       
    ,SemesterPromotionStatus				     nvarchar(50)   		    		
    ,CumulativeResultClassID				     int   		    		
    ,CumulativePCT						         decimal(10, 2)   		    
    ,BatchNo								     int       
    ,SemesterPromotionStatusDateTime		     datetime   		    				
    ,SemesterPromotionStatusByUserID		     int   		    				
    ,AttemptCount						         int   		    
    ,TotalMarkObtainedSemester			         int   		    			
    ,TotalMarkOutOfSemester				         int   		    		
    ,PCTSemester							     decimal(10, 2)   	    
    ,PrintedMarksheetNo					         nvarchar(50)   		    	
    ,IsResultHold						         bit   		    
    ,ResultHoldDateTime					         datetime   		    	
    ,ResultHoldByUserID					         int   		    	
    ,ResultHoldDescription				         nvarchar(250)   		    		
    -- ,DSIPreviousResultClassID			         int   		    			
    -- ,DSIPreviousCumulativeResultClassID          int		    					
    ,StudentLCName						         nvarchar(250)   		    
    ,CGPADisplay							     decimal(10, 2)   	    
    ,CGPAUptoExamSemester				         decimal(10, 2)   		    		
    ,StudentResultRemarks				         nvarchar(100)   		    		
    ,OldCGPAUptoExamSemester				     decimal(10, 2)   		    		
    ,IsResultAnalysis					         bit   		    	
    ,OldCGPADisplay						         decimal(10, 2)   		    
    ,MarksheetNo							     nvarchar(50)   	    
    -- ,ResultClassID20240830				         int   		    		
    -- ,CumulativeResultClassID20240830		     int   		    				
    ,TotalMarkObtainedOriginal			         int   					
    ,TotalMarkObtainedSemesterOriginal	         int   							
    ,ResultDeclarationDateSystem			     datetime   					
    -- ,GNRemarks							         nvarchar(500)   	
    ,IsSystemLocked						         bit   		
    ,IsFinalResult						         bit   		
    ,ApplicationType						     nvarchar(50)   		
)


INSERT INTO @dtResultStudent
	SELECT

		 [EXL_ExamL].[MappedExamID]                                       AS [ExamID]
        ,[EXL_ExamL].[CourseID]                                           AS [CourseID]
        ,[STU_Student].[InstituteID]                                      AS [InstituteID]    
        ,[STU_Student].[ProgramID]                                        AS [ProgramID]    
        ,[STU_Student].[StudentID]                                        AS [StudentID]    
        ,[STU_Student].[EnrollmentNo]                                     AS [EnrollmentNo]    
        ,[EXL_ExamStudentL].[SeatNo]                                      AS [SeatNo]
        ,SUM(ISNULL([dbo].[EXL_ExamStudentSubjectL].[TotalObtainedMark], 0)) AS [TotalMark]    
        ,SUM(ISNULL([dbo].[EXL_ExamStudentSubjectL].[TotalMaxMark], 0))   AS [OutOfMark]    
        ,[EXL_ExamStudentL].[PCT]                                         AS [PCT]
        ,[EXL_ExamStudentL].[SGPA]                                        AS [SPI]
        ,NULL                                                             AS [RankInBranch]    
        ,[EXL_ExamStudentL].[CurrentBacklog]                              AS [FailInSubject]        
        ,'Imported'                                                       AS [Remarks]
        , 1                                                               AS [UserID]
        ,@CurrentDateTime                                                 AS [Created]
        ,@CurrentDateTime                                                 AS [Modified]
        ,[EXL_ExamStudentL].[CGPA]                                        AS [CGPA]
        ,NULL                                                             AS [ResultClassID]        
        ,[EXL_ExamL].[ResultDeclarationDate]                              AS [ResultDeclarationDate]                
        ,0                                                                AS [IsDetainedBecauseOfAttendance]                        
        ,NULL                                                             AS [ResultEffect]    
        ,[EXL_ExamStudentL].[TotalBackLog]                                AS [TotalBackLog]    
        ,NULL                                                   AS [OldSPI]
        ,NULL                                                  AS [OldCGPA]
        ,NULL                                             AS [OldTotalMark]    
        ,NULL                                                   AS [OldPCT]
        ,NULL                                  AS [SemesterPromotionStatus]                
        ,NULL                                  AS [CumulativeResultClassID]                
        ,NULL                                            AS [CumulativePCT]        
        ,NULL                                                  AS [BatchNo]
        ,NULL                         AS [SemesterPromotionStatusDateTime]                        
        ,NULL                          AS [SemesterPromotionStatusByUserID]                        
        ,[EXL_ExamStudentSubjectL].[AttemptNo]                            AS [AttemptCount]    
        ,SUM(ISNULL([dbo].[EXL_ExamStudentSubjectL].[TotalObtainedMark], 0))                                AS [TotalMarkObtainedSemester]                    
        ,SUM(ISNULL([dbo].[EXL_ExamStudentSubjectL].[TotalMaxMark], 0))                                   AS [TotalMarkOutOfSemester]                
        ,[EXL_ExamStudentL].[PCT]                                              AS [PCTSemester]    
        ,NULL                                       AS [PrintedMarksheetNo]            
        ,NULL                                             AS [IsResultHold]    
        ,NULL                                       AS [ResultHoldDateTime]            
        ,NULL                                       AS [ResultHoldByUserID]            
        ,NULL                                    AS [ResultHoldDescription]                
       -- ,[aaa].[DSIPreviousResultClassID]                                 AS [DSIPreviousResultClassID]                
       -- ,[aaa].[DSIPreviousCumulativeResultClassID]                       AS [DSIPreviousCumulativeResultClassID]                            
        ,[STU_Student].[StudentLCName]                                    AS [StudentLCName]        
        ,[EXL_ExamStudentL].[CGPA]                                        AS [CGPADisplay]    
        ,NULL                                     AS [CGPAUptoExamSemester]            
        ,CASE WHEN ISNULL([dbo].[EXL_ExamStudentL].[CurrentBacklog],0) = 0
                THEN 'Pass'
                ELSE 'Fail'
            END                                                           AS [StudentResultRemarks]            
        ,NULL                                 AS [OldCGPAUptoExamSemester]                
        ,1                                         AS [IsResultAnalysis]        
        ,NULL                                           AS [OldCGPADisplay]        
        ,NULL                                              AS [MarksheetNo]    
        --,[aaa].[ResultClassID20240830]                                    AS [ResultClassID20240830]                
        --,[aaa].[CumulativeResultClassID20240830]                          AS [CumulativeResultClassID20240830]                        
        ,SUM(ISNULL([dbo].[EXL_ExamStudentSubjectL].[TotalObtainedMark], 0))                                AS [TotalMarkObtainedOriginal]                    
        ,SUM(ISNULL([dbo].[EXL_ExamStudentSubjectL].[TotalMaxMark], 0))                        AS [TotalMarkObtainedSemesterOriginal]                            
        ,[EXL_ExamL].[ResultDeclarationDate]                              AS [ResultDeclarationDateSystem]                    
        --,[aaa].[GNRemarks]                                                AS [GNRemarks]    
        ,0                                           AS [IsSystemLocked]        
        ,NULL                                            AS [IsFinalResult]        
        ,NULL                                          AS [ApplicationType]        

	FROM [EXL_ExamL]

	INNER JOIN [EXL_ExamStudentL]
		ON [EXL_ExamStudentL].[ExamLID] = [EXL_ExamL].[ExamLID]
        
        --*------------------------------------------------
        AND [EXL_ExamL].[ExamLID] IN (_listOfExamLIDs_) --|
        --*------------------------------------------------

	INNER JOIN [dbo].[EXL_ExamStudentSubjectL]
		ON [EXL_ExamStudentSubjectL].[ExamLID] = [EXL_ExamL].[ExamLID]
		AND [EXL_ExamStudentSubjectL].[StudentID] = [EXL_ExamStudentL].[StudentID]


	INNER JOIN [dbo].[STU_Student]
		ON [dbo].[STU_Student].[StudentID] = [dbo].[EXL_ExamStudentL].[StudentID]
			AND [dbo].[EXL_ExamL].[MappedExamID] IS NOT NULL
			AND [dbo].[EXL_ExamStudentL].[StudentID] IS NOT NULL
			-- AND [dbo].[EXL_ExamStudentSubjectL].[GradeName] <> 'Ex'

	GROUP BY [dbo].[EXL_ExamL].[MappedExamID]
			,[dbo].[EXL_ExamL].[CourseID]
            ,[dbo].[EXL_ExamL].[ResultDeclarationDate]
			,[dbo].[STU_Student].[InstituteID]
			,[dbo].[STU_Student].[ProgramID]
			,[dbo].[STU_Student].[StudentID]
			,[dbo].[STU_Student].[EnrollmentNo]
			,[dbo].[EXL_ExamStudentL].[SeatNo]
			,[dbo].[EXL_ExamStudentL].[SGPA]
			,[dbo].[EXL_ExamStudentL].[CurrentBacklog]
			,[dbo].[EXL_ExamStudentL].[CGPA]
			,[dbo].[EXL_ExamStudentL].[TotalBacklog]
            ,[dbo].[EXL_ExamStudentL].[PCT]
			,[dbo].[EXL_ExamStudentSubjectL].[AttemptNo]
			,[dbo].[STU_Student].[StudentLCName]
		--  ,[dbo].EXL_ExamStudentL.OverallPassFail


SELECT
	*
FROM @dtResultStudent