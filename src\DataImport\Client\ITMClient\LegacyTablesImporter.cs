// using System;
// using System.Data;

// namespace DataImport.ITMClient
// {
//     public class LegacyTablesImporter : ImportBase
//     {
//         public LegacyTablesImporter(DatabaseHandler dbHandler) : base(dbHandler) { }

//         public void Import(DataTable data)
//         {
//             Console.WriteLine("Starting import into legacy tables...");

//             // Example: Import into EXL_ExamL
//             if (ConfirmImport("EXL_ExamL"))
//             {
//                 ImportIntoTable(data, "EXL_ExamL");
//                 Console.WriteLine("Data imported into EXL_ExamL.");
//             }

//             // Example: Import into EXL_ExamStudentL
//             if (ConfirmImport("EXL_ExamStudentL"))
//             {
//                 ImportIntoTable(data, "EXL_ExamStudentL");
//                 Console.WriteLine("Data imported into EXL_ExamStudentL.");
//             }

//             // Example: Import into EXL_ExamStudentSubjectL
//             if (ConfirmImport("EXL_ExamStudentSubjectL"))
//             {
//                 ImportIntoTable(data, "EXL_ExamStudentSubjectL");
//                 Console.WriteLine("Data imported into EXL_ExamStudentSubjectL.");
//             }
//         }

//         private void ImportIntoTable(DataTable data, string tableName)
//         {
//             // Logic to import data into the specified table
//         }
//     }
// }