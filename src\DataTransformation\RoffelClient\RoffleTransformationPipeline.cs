// using System;
// using DataTransformation.Common;

// using System.Data;
// using System.Diagnostics;
// using System.IO;
// using System.Text.RegularExpressions;
// using ClosedXML.Excel;
// using DocumentFormat.OpenXml.Drawing.Diagrams;
// using DocumentFormat.OpenXml.Drawing.Spreadsheet;
// using DocumentFormat.OpenXml.Office2010.PowerPoint;
// using DocumentFormat.OpenXml.Office2016.Excel;
// using DocumentFormat.OpenXml.Spreadsheet;
// using DocumentFormat.OpenXml.Wordprocessing;


// namespace DataTransformation.RoffleClient
// {
//     public class RoffleTransformationPipeline
//     {

//         public void Run(string inputFolderPath, string outputFolderPath)
//         {
//             // Check if the input folder exists
//             if (!Directory.Exists(inputFolderPath))
//             {
//                 Console.WriteLine("The input folder does not exist at the specified location: " + inputFolderPath);
//                 return;
//             }

//             // Ensure the output folder exists
//             EnsureDirectoryExists(outputFolderPath);

//             // Process the Excel files in the input folder and its subdirectories
//             ProcessExcelFilesInDirectory(inputFolderPath, outputFolderPath);

//             // WriteErrorFilesToLog(errorFiles, errorMessages);

//             Console.WriteLine("Roffle Data Transformation Complete!");
//         }

//         public static List<string> errorFiles = new List<string>();
//         public static List<string> errorMessages = new List<string>();

//         // SubjectClass
//         #region SubjectClass
//         public class Subject
//         {
//             public string SubjectCode { get; set; }
//             public string SubjectName { get; set; }

//             public int HasPractical { get; set; }
//             public Double? SubjectTheoryCredit { get; set; }

//             public Double? SubjectPracticalCredit { get; set; }

//             public int NumberOfColumn { get; set; }

//             public int Index { get; set; }

//             public double? InternalMaxMark { get; set; }
//             public double? InternalPassingMark { get; set; }

//             public double? ExternalMaxMark { get; set; }
//             public double? ExternalPassingMark { get; set; }

//             public double? TotalMaxMark { get; set; }
//             public double? TotalPassingMark { get; set; }

//             public double OutOfMark { get; set; }

//             //public int HasInternal

//             public Subject(string subjectCode, string subjectName, int hasPractical, Double subjectTheoryCredit,
//                     Double subjectPracticalCredit, int numberOfColumn, int index)
//             {
//                 this.SubjectCode = subjectCode;
//                 this.SubjectName = subjectName;
//                 this.HasPractical = hasPractical;
//                 this.SubjectTheoryCredit = subjectTheoryCredit;
//                 this.SubjectPracticalCredit = subjectPracticalCredit;
//                 this.NumberOfColumn = numberOfColumn;
//                 this.Index = index;

//             }


//             public override string ToString()
//             {
//                 return $"\n\nSubject Code: {this.SubjectCode}, " +
//                     $"Subject Name: {this.SubjectName}, " +
//                     $"Has Practical: {this.HasPractical}, " +
//                     $"Theory Credit: {this.SubjectTheoryCredit}, " +
//                     $"\nPractical Credit: {this.SubjectPracticalCredit}, " +
//                     $"Number of Column: {this.NumberOfColumn}, " +
//                     $"Index: {this.Index}, " +
//                     $"Internal Max Mark: {this.InternalMaxMark}, " +
//                     $"Internal Passing Mark: {this.InternalPassingMark}, " +
//                     $"\nExternal Max Mark: {this.ExternalMaxMark}, " +
//                     $"External Passing Mark: {this.ExternalPassingMark}, " +
//                     $"Total Max Mark: {this.TotalMaxMark}, " +
//                     $"Total Passing Mark: {this.TotalPassingMark}, " +
//                     $"Out of Mark: {this.OutOfMark}";
//             }

//         }
//         #endregion

//         // SubjectWiseDataClass
//         #region SubjectWiseDataClass
//         public class SubjectWiseData
//         {
//             public string SubjectCode { get; set; }

//             public int SubjectType { get; set; }

//             public int PassFail { get; set; }

//             public SubjectWiseData(string subjectCode, int SubjectType, int passFail)
//             {
//                 this.SubjectCode = subjectCode;
//                 this.SubjectType = SubjectType;
//                 this.PassFail = passFail;
//             }


//             public override string ToString()
//             {
//                 return $"SubjectCode: {SubjectCode}, SubjectType: {SubjectType}, PassFail: {PassFail}";
//             }
//         }
//         #endregion

//         // FileNameDetails
//         #region FileNameDetailsClass
//         public class FileNameDetails
//         {
//             public string AcademicYear { get; set; }

//             public string CourseName { get; set; }

//             public int Semester { get; set; }

//             public string MonthYear { get; set; }

//             public string? ExamType { get; set; }

//         }
//         #endregion

//         // StudentRemSubjectData
//         #region StudentRemSubjectData
//         public class StudentRemSubjectData
//         {

//             public int TheoryRemRequired { get; set; }   // 0= rem not required, 1= rem required
//             public int PracticalRemRequired { get; set; } // 0= rem not required, 1= rem required

//             public StudentRemSubjectData(int theoryRemRequired, int practicalRemRequired)
//             {
//                 this.TheoryRemRequired = theoryRemRequired;
//                 this.PracticalRemRequired = practicalRemRequired;
//             }

//             public override string ToString()
//             {
//                 return $"Theory Rem Required: {this.TheoryRemRequired}, Practical Rem Required: {this.PracticalRemRequired}";
//             }
//         }
//         #endregion

//         // ErrorFiles Log
//         #region ErrorFilesLog
//         static void WriteErrorFilesToLog(List<string> errorFiles, List<String> errorMessages)
//         {
//             string logPath = @"C:\Users\<USER>\OneDrive\Documents\OUTPUT FOR REVISED DATA\error_log.xlsx";

//             // Create a new Excel file or overwrite the existing one
//             using (var workbook = new XLWorkbook())
//             {
//                 var worksheet = workbook.Worksheets.Add("Error Log");
//                 worksheet.Cell(1, 1).Value = "ErrorFileNames"; // Header
//                 worksheet.Cell(1, 2).Value = "ErrorMessages"; // Header

//                 // Add error files to the worksheet
//                 for (int i = 0; i < errorFiles.Count; i++)
//                 {
//                     worksheet.Cell(i + 2, 1).Value = errorFiles[i]; // Start adding from row 2
//                     worksheet.Cell(i + 2, 2).Value = errorMessages[i]; // Start adding from row 2
//                 }
//                 workbook.SaveAs(logPath);
//             }

//             Console.WriteLine($"Error files logged to {logPath}");



//         }
//         #endregion

//         // Method to ensure the directory exists
//         #region DirectoryExists
//         static void EnsureDirectoryExists(string directoryPath)
//         {
//             if (!Directory.Exists(directoryPath))
//             {
//                 Directory.CreateDirectory(directoryPath);
//             }
//         }
//         #endregion

//         // Method to process Excel files in the directory and subdirectories
//         #region Process Excel Files in directory
//         static void ProcessExcelFilesInDirectory(string inputDir, string outputDir)
//         {
//             // Process each Excel file in the current directory
//             foreach (var file in Directory.GetFiles(inputDir, "*.xlsx"))
//             {
//                 string relativePath = Path.GetRelativePath(inputDir, file);
//                 string outputFilePath = Path.Combine(outputDir, Path.ChangeExtension(relativePath, ".processed.xlsx"));

//                 // Ensure the directory for the output file exists
//                 string outputFolder = Path.GetDirectoryName(outputFilePath);
//                 EnsureDirectoryExists(outputFolder);

//                 // Process and generate the corresponding processed Excel file
//                 ProcessExcelFile(file, outputFilePath);
//             }

//             // Recursively process each subdirectory
//             foreach (var subDir in Directory.GetDirectories(inputDir))
//             {
//                 string subOutputDir = Path.Combine(outputDir, Path.GetFileName(subDir));
//                 ProcessExcelFilesInDirectory(subDir, subOutputDir);
//             }
//         }
//         #endregion

//         // Method to process the Excel file and generate the output Excel file
//         #region ProcessExcelFile
//         static void ProcessExcelFile(string inputPath, string outputFilePath)
//         {
//             try
//             {
//                 // Create output DataTable for the Excel file
//                 var file2Table = new DataTable();
//                 var NewExcelFileTable = new DataTable();
//                 var NewMarkHeadExcelFileTable= new DataTable();
                


//                 int BacklogFlag = 0;

//                 string fileName = Path.GetFileNameWithoutExtension(inputPath);
//                 Console.WriteLine(fileName);
//                 FileNameDetails details = ExtractDetailsFromFilename(fileName);
//                 //string ExamName = details.CourseName + "-" + "Semester-" + details.Semester + "-" + details.AcademicYear + "-" + details.MonthYear;


//                 // Set up columns for the processed Excel file

//                 NewExcelFileTable.Columns.AddRange(
//                     new[]
//                     {
//                         //new DataColumn("TotalBacklog"),
//                     new DataColumn("ExamStudentSubjectLID"),
//                     new DataColumn("ExamLID"),
//                     new DataColumn("ExamStudentLID"),
//                     new DataColumn("Credit"),     //null
//                     new DataColumn("SubjectCode"),
//                     new DataColumn("SubjectName"),
//                     new DataColumn("EnrollmentNo"),
//                     new DataColumn("StudentName"),

//                     new DataColumn("InternalTheoryMaxMark"),
//                     new DataColumn("InternalTheoryPassingMark"),
//                     new DataColumn("InternalTheoryObtainedMark"),
//                     new DataColumn("InternalPracticalMaxMark"),
//                     new DataColumn("InternalPracticalPassingMark"),
//                     new DataColumn("InternalPracticalObtainedMark"),
//                     new DataColumn("InternalMaxMark"),
//                     new DataColumn("InternalPassingMark"),
//                     new DataColumn("InternalObtainedMark"),


//                     new DataColumn("ExternalTheoryMaxMark"),
//                     new DataColumn("ExternalTheoryPassingMark"),
//                     new DataColumn("ExternalTheoryObtainedMark"),
//                     new DataColumn("ExternalPracticalMaxMark"),
//                     new DataColumn("ExternalPracticalPassingMark"),
//                     new DataColumn("ExternalPracticalObtainedMark"),
//                     new DataColumn("ExternalMaxMark"),
//                     new DataColumn("ExternalPassingMark"),
//                     new DataColumn("ExternalObtainedMark"),

//                     new DataColumn("TheoryMaxMark"),
//                     new DataColumn("TheoryPassingMark"),
//                     new DataColumn("TheoryObtainedMark"),
//                     new DataColumn("TheoryOutOf100Mark"),  //extra   ExamStudentSubjectL  done
//                     new DataColumn("GradePoint"),
//                     new DataColumn("GradeName"),   //extra    ExamStudentSubjectL  done
        


//                     new DataColumn("PracticalMaxMark"),
//                     new DataColumn("PracticalPassingMark"),
//                     new DataColumn("PracticalObtainedMark"),
//                     new DataColumn("PracticalOut100OfMark"),   //extra   ExamStudentSubjectL  done
            
            
//                     new DataColumn("TotalMaxMark"),
//                     new DataColumn("TotalPassingMark"),
//                     new DataColumn("TotalObtainedMark"),

//                     new DataColumn("CreditPoint"),
//                     new DataColumn("TotalCreditPoint"),
//                     new DataColumn("SemesterTotalMark"),  //extra  ExamStudentL  done
//                     new DataColumn("TotalCreditOffered"),   //extra ExamStudentL  done
//                     new DataColumn("SGPA"),
//                     new DataColumn("IsFailInTheory"),
//                     new DataColumn("IsFailInPractical"),
//                     new DataColumn("PassFail"),


//                     new DataColumn("IsFailInExternalTheory"),
//                     new DataColumn("IsFailInExternalPractical"),
//                     new DataColumn("IsFailInInternalTheory"),
//                     new DataColumn("IsFailInInternalPractical"),
//                     new DataColumn("IsFailInInternal"),
//                     new DataColumn("IsFailInExternal"),

//                     new DataColumn("CurrentBacklog"),
//                     new DataColumn("TotalBacklog"),
//                     new DataColumn("EarnCredit"),    //extra ExamstudentL   done
//                     new DataColumn("ExamType"),
//                     new DataColumn("AcademicYear"),  //extra ExamL   done
//                     new DataColumn("CourseName"),    //extra ExamL   done
//                     new DataColumn("Semester"),
//                     new DataColumn("Month_Year"),     //extra  ExamL  done 
//                     new DataColumn("ExamName")
//                     }
//                     );


//                 NewMarkHeadExcelFileTable.Columns.AddRange(
//                 new[]
//                 {

//                     new DataColumn("ExamStudentSubjectLID"),
//                     new DataColumn("ExamLID"),
//                     new DataColumn("ExamStudentLID"),
//                     new DataColumn("ExamStudentSubjectMarkHeadLID"), 

//                     new DataColumn("SubjectCode"),
//                     new DataColumn("SubjectName"),
//                     new DataColumn("EnrollmentNo"),
//                     new DataColumn("StudentName"),

//                     new DataColumn("HeadName"),
//                     new DataColumn("MarkMax"),
//                     new DataColumn("MarkPassing"),
//                     new DataColumn("MarkObtainedOriginal"),
//                     new DataColumn("MarkGracingRequired"),
//                     new DataColumn("MarkGracingAlloted"),
//                     new DataColumn("MarkObtainedAfterGracing"),
//                     new DataColumn("IsPass"),


//                     //composite head
//                     new DataColumn("GradeName"),
                    
//                     new DataColumn("GradePoint"),
//                     new DataColumn("CreditPoint"),
//                     new DataColumn("Credit"), //composite head


                
//                     new DataColumn("TotalCreditPoint"),
//                     new DataColumn("SemesterTotalMark"),  //extra  ExamStudentL  done
//                     new DataColumn("TotalCreditOffered"),   //extra ExamStudentL  done
//                     new DataColumn("SGPA"),


//                     new DataColumn("CurrentBacklog"),
//                     new DataColumn("TotalBacklog"),
//                     new DataColumn("EarnCredit"),  //extra ExamstudentL   done


//                     new DataColumn("ExamType"),
//                     new DataColumn("AcademicYear"),  //extra ExamL   done
//                     new DataColumn("CourseName"),    //extra ExamL   done
//                     new DataColumn("Semester"),
//                     new DataColumn("Month_Year"),     //extra  ExamL  done 
//                     new DataColumn("ExamName")
//                 }
//                 );

//                 // Read Excel data using ClosedXML
//                 using (var workbook = new XLWorkbook(inputPath))
//                 {
//                     Dictionary<string, Dictionary<string, StudentRemSubjectData>> studentRemData = new Dictionary<string, Dictionary<string, StudentRemSubjectData>>();

//                     int atktSheetCount = workbook.Worksheets
//                     .Count(ws => ws.Name.Contains("ATKT", StringComparison.OrdinalIgnoreCase) || ws.Name.Contains("Remedial", StringComparison.OrdinalIgnoreCase));
//                     Console.WriteLine("ATKT Sheet Count: " + atktSheetCount);

//                     //var worksheet = workbook.Worksheet(1);
//                     var worksheet = workbook.Worksheets.FirstOrDefault(ws => ws.Name.Contains("ATKT", StringComparison.OrdinalIgnoreCase) || ws.Name.Contains("Remedial", StringComparison.OrdinalIgnoreCase));

//                     if (worksheet != null)
//                     {
//                         BacklogFlag = 1;
//                         Console.WriteLine("backlog flag set");
//                         var regworksheet = workbook.Worksheets.FirstOrDefault(ws =>
//                         ws.Name.Contains("master", StringComparison.OrdinalIgnoreCase) &&
//                         ws.Name.Contains("Reassessment", StringComparison.OrdinalIgnoreCase));

//                         if (atktSheetCount > 1)
//                         {
//                             var atktSheets = workbook.Worksheets
//                         .Where(ws => ws.Name.Contains("ATKT", StringComparison.OrdinalIgnoreCase) || ws.Name.Contains("Remedial", StringComparison.OrdinalIgnoreCase))
//                         .ToList();
//                             regworksheet = atktSheets[1];
//                         }

//                         if (regworksheet == null)
//                         {
//                             regworksheet = workbook.Worksheets.FirstOrDefault(ws =>
//                             ws.Name.Contains("master", StringComparison.OrdinalIgnoreCase) &&
//                             ws.Name.Contains("grace", StringComparison.OrdinalIgnoreCase));
//                         }

//                         if (regworksheet == null)
//                         {

//                             regworksheet = workbook.Worksheets.FirstOrDefault(ws =>
//                             ws.Name.Contains("master", StringComparison.OrdinalIgnoreCase) &&
//                             ws.Name.Contains("(R)", StringComparison.OrdinalIgnoreCase));

//                         }

//                         if (regworksheet == null)
//                         {
//                             regworksheet = workbook.Worksheets.FirstOrDefault(ws =>
//                             ws.Name.Contains("master", StringComparison.OrdinalIgnoreCase) &&
//                             ws.Name.Contains("regular", StringComparison.OrdinalIgnoreCase) &&
//                             !ws.Name.Contains("Remedial", StringComparison.OrdinalIgnoreCase) &&
//                             !ws.Name.Contains("ATKT", StringComparison.OrdinalIgnoreCase)
//                             );    
//                         }
//                         if (regworksheet == null)
//                         {
//                             regworksheet = workbook.Worksheets.FirstOrDefault(ws =>
//                             ws.Name.Contains("master", StringComparison.OrdinalIgnoreCase)
//                             );
//                         }

//                         Console.WriteLine("Sheet Selected " + regworksheet.Name);
//                         //print number of rows
//                         Console.WriteLine(regworksheet.RowsUsed().Count());

                        
//                         var temprows = regworksheet.RowsUsed();
//                         var tempheader1Row = temprows.First(); // Get the first row
//                         int tempSubjectEndingIndex = 0;
//                         int tempflag = 0;
//                         foreach (var cell in tempheader1Row.Cells())
//                         {
//                             if ((cell.GetValue<string>() == "Result" || cell.GetValue<string>() == "Total GP" || cell.GetValue<string>() == "Total Credit Points" || cell.GetValue<string>() == "Total Credit Point"
//                                 || cell.GetValue<string>() == "Sum CGP" || cell.GetValue<string>() == "Toal Credit Point" || cell.GetValue<string>() == "Total Credit Point ") && tempflag == 0)
//                             {
//                                 tempflag = 1;
//                                 tempSubjectEndingIndex = cell.Address.ColumnNumber;
//                             }

//                             Console.WriteLine($"Column {cell.Address.ColumnNumber}: {cell.GetValue<string>()}");
//                         }
//                         tempSubjectEndingIndex -= 2;
//                         int tempSubjectStartingIndex = 4;


//                         var tempheaders1 = tempheader1Row.Cells().Select(cell => cell.Value.ToString()).ToArray();

//                         List<Subject> tempSubjects = new List<Subject>();

//                         int temp1 = 0;
//                         int previous1 = tempSubjectStartingIndex;
//                         int tempPrevious1 = 0;
//                         int tempi1 = tempSubjectStartingIndex;
//                         string preName1 = "";
//                         string pattern1 = @"(?i)Credit:\s*(\d+)";

//                         for (int i = tempSubjectStartingIndex; i <= tempSubjectEndingIndex; i++)
//                         {
//                             if (tempheaders1[i] != "")
//                             {

//                                 string Code = ExtractSubjectCode(tempheaders1[i]);

//                                 string Name = "";
//                                 try
//                                 {
//                                     Name = ExtractSubjectName(tempheaders1[i]);
//                                 }
//                                 catch (Exception ex)
//                                 {
//                                     Console.WriteLine(ex.Message);
//                                 }
//                                 Console.WriteLine(Name);
//                                 double SubjectTheoryCredit = 0;
//                                 double SubjectPracticalCredit = 0;

//                                 Match match = Regex.Match(tempheaders1[i], pattern1);
//                                 double credit = 0;
//                                 if (match.Success)
//                                 {
//                                     credit = Convert.ToDouble(match.Groups[1].Value);
//                                 }


//                                 if (tempheaders1[i].Contains("THEORY", StringComparison.OrdinalIgnoreCase) ||
//                                         tempheaders1[i].Contains("THRORY", StringComparison.OrdinalIgnoreCase))
//                                 {
//                                     SubjectTheoryCredit = credit;
//                                 }
//                                 if (tempheaders1[i].Contains("PRACTICAL", StringComparison.OrdinalIgnoreCase))
//                                 {
//                                     SubjectPracticalCredit = credit;
//                                 }

//                                 Subject subject = new Subject(Code, Name, 0, SubjectTheoryCredit, SubjectPracticalCredit, 0, i);
//                                 tempSubjects.Add(subject);
//                                 if (i != tempSubjectStartingIndex)
//                                 {

//                                     int x = temp1 - 1;

//                                     string CommonPart = CommonPrefix(preName1, Name);
//                                     if (CommonPart.Length > preName1.Length * 4 / 5)
//                                     {
//                                         tempSubjects[x].HasPractical = 1;
//                                     }

//                                     tempSubjects[x].NumberOfColumn = i - previous1;

//                                     previous1 = i;
//                                 }

//                                 temp1++;
//                                 preName1 = Name;
//                             }
//                             tempi1++;
//                         }
//                         tempSubjects[temp1 - 1].NumberOfColumn = tempi1 - previous1;

//                         //foreach (Subject subject in tempSubjects)
//                         //{
//                         //    Console.WriteLine(subject);
//                         //}

//                         var tempheader2Row = temprows.Skip(1).First();

//                         var tempheaders2 = tempheader2Row.Cells().Select(cell => cell.Value.ToString()).ToArray();


//                         foreach (var row in temprows.Skip(2))
//                         {
//                             var fields = row.Cells().Select(cell => cell.Value.ToString()).ToArray();

//                             //Ensure we handle rows with insufficient columns
//                             if (fields.Length < tempheaders2.Length / 2)
//                             {
//                                 Console.WriteLine("Skipping row due to insufficient columns.");
//                                 continue;
//                             }

//                             var EnrollmentNo = fields[2];  
//                             var StudentName = fields[3];

//                             try
//                             {

//                                 for (int i = 0; i < tempSubjects.Count(); i++)
//                                 {
//                                     Subject subject = tempSubjects[i];
//                                     var SubjectCode = "";
//                                     if (subject.SubjectCode != "")
//                                     {
//                                         SubjectCode = subject.SubjectCode;
//                                     }

//                                     var SubjectName = subject.SubjectName;
//                                     //var TheoryCredit = "";

//                                     var Credit = "";

//                                     int Index = subject.Index;

//                                     var GradePoint = "";
//                                     var GradeName = "";
//                                     var TheoryCreditGradePoint = "";



//                                     var PracticalGradeName = "";
//                                     var PracticalCreditGradePoint = "";


//                                     if (subject.SubjectTheoryCredit != 0)
//                                     {

//                                         if (subject.NumberOfColumn == 7)
//                                         {
//                                             GradePoint = fields[Index + 4];
//                                             GradeName = fields[Index + 5];
//                                             TheoryCreditGradePoint = fields[Index + 6];

//                                             if ((GradePoint.Trim() == "-" || GradePoint.Trim() == "0" || GradePoint.Trim() == "" || string.IsNullOrWhiteSpace(GradePoint)) &&
//                                                 (GradeName.Trim() == "-" || GradeName.Trim() == "0" || GradeName.Trim() == "" || string.IsNullOrWhiteSpace(GradeName)) &&
//                                                 (TheoryCreditGradePoint.Trim() == "-" || TheoryCreditGradePoint.Trim() == "0" || TheoryCreditGradePoint.Trim() == "" || string.IsNullOrWhiteSpace(TheoryCreditGradePoint))
//                                                 )
//                                             {
//                                                 continue;
//                                             }

//                                             if (GradeName.Trim().Equals("AB", StringComparison.OrdinalIgnoreCase) || GradeName.Trim().Equals("F", StringComparison.OrdinalIgnoreCase))
//                                             {
//                                                 Console.WriteLine("Rem of Student :" + EnrollmentNo + " ----> " + SubjectCode);
//                                                 if (studentRemData.ContainsKey(EnrollmentNo))
//                                                 {
                                                    
//                                                     studentRemData[EnrollmentNo][SubjectCode] = new StudentRemSubjectData(1, 0);


//                                                 }
//                                                 else
//                                                 {
//                                                     studentRemData[EnrollmentNo] = new Dictionary<string, StudentRemSubjectData>();
//                                                     studentRemData[EnrollmentNo][SubjectCode] = new StudentRemSubjectData(1, 0);
//                                                 }

//                                             }

//                                         }
//                                         else if (subject.NumberOfColumn == 6)
//                                         {

//                                             GradePoint = fields[Index + 3];
//                                             GradeName = fields[Index + 4];
//                                             TheoryCreditGradePoint = fields[Index + 5];

//                                             if ((GradePoint.Trim() == "-" || GradePoint.Trim() == "0" || GradePoint.Trim() == "" || string.IsNullOrWhiteSpace(GradePoint)) &&
//                                                 (GradeName.Trim() == "-" || GradeName.Trim() == "0" || GradeName.Trim() == "" || string.IsNullOrWhiteSpace(GradeName)) &&
//                                                 (TheoryCreditGradePoint.Trim() == "-" || TheoryCreditGradePoint.Trim() == "0" || TheoryCreditGradePoint.Trim() == "" || string.IsNullOrWhiteSpace(TheoryCreditGradePoint))
//                                                 )
//                                             {
//                                                 continue;
//                                             }
//                                             if (GradeName.Trim().Equals("AB", StringComparison.OrdinalIgnoreCase) || GradeName.Trim().Equals("F", StringComparison.OrdinalIgnoreCase))
//                                             {
//                                                 Console.WriteLine("Rem of Student :" + EnrollmentNo + " ----> " + SubjectCode);
//                                                 if (studentRemData.ContainsKey(EnrollmentNo))
//                                                 {
//                                                     studentRemData[EnrollmentNo][SubjectCode] = new StudentRemSubjectData(1, 0);
//                                                 }
//                                                 else
//                                                 {
//                                                     studentRemData[EnrollmentNo] = new Dictionary<string, StudentRemSubjectData>();
//                                                     studentRemData[EnrollmentNo][SubjectCode] = new StudentRemSubjectData(1, 0);
//                                                 }

//                                             }

//                                         }
//                                         else if (subject.NumberOfColumn == 5)
//                                         {

//                                             GradePoint = fields[Index + 2];
//                                             GradeName = fields[Index + 3];

//                                             TheoryCreditGradePoint = fields[Index + 4];

//                                             if ((GradePoint.Trim() == "-" || GradePoint.Trim() == "0" || GradePoint.Trim() == "" || string.IsNullOrWhiteSpace(GradePoint)) &&
//                                                 (GradeName.Trim() == "-" || GradeName.Trim() == "0" || GradeName.Trim() == "" || string.IsNullOrWhiteSpace(GradeName)) &&
//                                                 (TheoryCreditGradePoint.Trim() == "-" || TheoryCreditGradePoint.Trim() == "0" || TheoryCreditGradePoint.Trim() == "" || string.IsNullOrWhiteSpace(TheoryCreditGradePoint))
//                                                 )
//                                             {
//                                                 continue;
//                                             }
//                                             if (GradeName.Trim().Equals("AB", StringComparison.OrdinalIgnoreCase) || GradeName.Trim().Equals("F", StringComparison.OrdinalIgnoreCase))
//                                             {
//                                                 Console.WriteLine("Rem of Student :" + EnrollmentNo + " ----> " + SubjectCode);
//                                                 if (studentRemData.ContainsKey(EnrollmentNo))
//                                                 {
//                                                     studentRemData[EnrollmentNo][SubjectCode] = new StudentRemSubjectData(1, 0);
//                                                 }
//                                                 else
//                                                 {
//                                                     studentRemData[EnrollmentNo] = new Dictionary<string, StudentRemSubjectData>();
//                                                     studentRemData[EnrollmentNo][SubjectCode] = new StudentRemSubjectData(1, 0);
//                                                 }

//                                             }
//                                         }
//                                         else if (subject.NumberOfColumn == 4)
//                                         {
//                                             GradePoint = fields[Index + 1];
//                                             GradeName = fields[Index + 2];
//                                             TheoryCreditGradePoint = fields[Index + 3];

//                                             if ((GradePoint.Trim() == "-" || GradePoint.Trim() == "0" || GradePoint.Trim() == "" || string.IsNullOrWhiteSpace(GradePoint)) &&
//                                                 (GradeName.Trim() == "-" || GradeName.Trim() == "0" || GradeName.Trim() == "" || string.IsNullOrWhiteSpace(GradeName)) &&
//                                                 (TheoryCreditGradePoint.Trim() == "-" || TheoryCreditGradePoint.Trim() == "0" || TheoryCreditGradePoint.Trim() == "" || string.IsNullOrWhiteSpace(TheoryCreditGradePoint))
//                                                 )
//                                             {
//                                                 continue;
//                                             }
//                                             if (GradeName.Trim().Equals("AB", StringComparison.OrdinalIgnoreCase) || GradeName.Trim().Equals("F", StringComparison.OrdinalIgnoreCase))
//                                             {
//                                                 Console.WriteLine("Rem of Student :" + EnrollmentNo + " ----> " + SubjectCode);
//                                                 if (studentRemData.ContainsKey(EnrollmentNo))
//                                                 {
//                                                     studentRemData[EnrollmentNo][SubjectCode] = new StudentRemSubjectData(1, 0);
//                                                 }
//                                                 else
//                                                 {
//                                                     studentRemData[EnrollmentNo] = new Dictionary<string, StudentRemSubjectData>();
//                                                     studentRemData[EnrollmentNo][SubjectCode] = new StudentRemSubjectData(1, 0);
//                                                 }

//                                             }
//                                         }

//                                     }
//                                     else if (subject.SubjectPracticalCredit != 0)
//                                     {

//                                         int PracIndex = subject.Index;

//                                         if (subject.NumberOfColumn == 7)
//                                         {
//                                             GradePoint = fields[PracIndex + 4];
//                                             GradeName = fields[PracIndex + 5];
//                                             PracticalCreditGradePoint = fields[PracIndex + 6];



//                                             if ((GradePoint.Trim() == "-" || GradePoint.Trim() == "0" || GradePoint.Trim() == "" || string.IsNullOrWhiteSpace(GradePoint)) &&
//                                             (GradeName.Trim() == "-" || GradeName.Trim() == "0" || GradeName.Trim() == "" || string.IsNullOrWhiteSpace(GradeName)) &&
//                                             (PracticalCreditGradePoint.Trim() == "-" || PracticalCreditGradePoint.Trim() == "0" || PracticalCreditGradePoint.Trim() == "" || string.IsNullOrWhiteSpace(PracticalCreditGradePoint))
//                                                 )
//                                             {
//                                                 continue;
//                                             }

//                                             if (GradeName.Equals("AB", StringComparison.OrdinalIgnoreCase) || GradeName.Equals("F", StringComparison.OrdinalIgnoreCase))
//                                             {
//                                                 if (studentRemData.ContainsKey(EnrollmentNo))
//                                                 {

//                                                     var SubjectDict = studentRemData[EnrollmentNo];
//                                                     if (SubjectDict.ContainsKey(SubjectCode))
//                                                     {
//                                                         var SubjectRemData = studentRemData[EnrollmentNo][SubjectCode];
//                                                         studentRemData[EnrollmentNo][SubjectCode] = new StudentRemSubjectData(SubjectRemData.TheoryRemRequired, 1);
//                                                     }
//                                                     else
//                                                     {
//                                                         studentRemData[EnrollmentNo][SubjectCode] = new StudentRemSubjectData(0, 1);
//                                                     }
//                                                 }
//                                                 else
//                                                 {
//                                                     studentRemData[EnrollmentNo] = new Dictionary<string, StudentRemSubjectData>();
//                                                     studentRemData[EnrollmentNo][SubjectCode] = new StudentRemSubjectData(0, 1);
//                                                 }
//                                             }

//                                         }
//                                         else if (subject.NumberOfColumn == 6)
//                                         {

//                                             GradePoint = fields[PracIndex + 3];
//                                             GradeName = fields[PracIndex + 4];
//                                             PracticalCreditGradePoint = fields[PracIndex + 5];


//                                             if ((GradePoint.Trim() == "-" || GradePoint.Trim() == "0" || GradePoint.Trim() == "" || string.IsNullOrWhiteSpace(GradePoint)) &&
//                                             (GradeName.Trim() == "-" || GradeName.Trim() == "0" || GradeName.Trim() == "" || string.IsNullOrWhiteSpace(GradeName)) &&
//                                             (PracticalCreditGradePoint.Trim() == "-" || PracticalCreditGradePoint.Trim() == "0" || PracticalCreditGradePoint.Trim() == "" || string.IsNullOrWhiteSpace(PracticalCreditGradePoint))
//                                                 )
//                                             {
//                                                 continue;
//                                             }



//                                             if (GradeName.Equals("AB", StringComparison.OrdinalIgnoreCase) || GradeName.Equals("F", StringComparison.OrdinalIgnoreCase))
//                                             {

//                                                 if (studentRemData.ContainsKey(EnrollmentNo))
//                                                 {

//                                                     var SubjectDict = studentRemData[EnrollmentNo];
//                                                     if (SubjectDict.ContainsKey(SubjectCode))
//                                                     {
//                                                         var SubjectRemData = studentRemData[EnrollmentNo][SubjectCode];
//                                                         studentRemData[EnrollmentNo][SubjectCode] = new StudentRemSubjectData(SubjectRemData.TheoryRemRequired, 1);
//                                                     }
//                                                     else
//                                                     {
//                                                         studentRemData[EnrollmentNo][SubjectCode] = new StudentRemSubjectData(0, 1);
//                                                     }
//                                                 }
//                                                 else
//                                                 {
//                                                     studentRemData[EnrollmentNo] = new Dictionary<string, StudentRemSubjectData>();
//                                                     studentRemData[EnrollmentNo][SubjectCode] = new StudentRemSubjectData(0, 1);
//                                                 }
//                                             }

//                                         }
//                                         else if (subject.NumberOfColumn == 5)
//                                         {

//                                             GradePoint = fields[PracIndex + 2];
//                                             GradeName = fields[PracIndex + 3];

//                                             PracticalCreditGradePoint = fields[PracIndex + 4];

//                                             if ((GradePoint.Trim() == "-" || GradePoint.Trim() == "0" || GradePoint.Trim() == "" || string.IsNullOrWhiteSpace(GradePoint)) &&
//                                             (GradeName.Trim() == "-" || GradeName.Trim() == "0" || GradeName.Trim() == "" || string.IsNullOrWhiteSpace(GradeName)) &&
//                                             (PracticalCreditGradePoint.Trim() == "-" || PracticalCreditGradePoint.Trim() == "0" || PracticalCreditGradePoint.Trim() == "" || string.IsNullOrWhiteSpace(PracticalCreditGradePoint))
//                                                 )
//                                             {
//                                                 continue;
//                                             }
//                                             if (GradeName.Equals("AB", StringComparison.OrdinalIgnoreCase) || GradeName.Equals("F", StringComparison.OrdinalIgnoreCase))
//                                             {
//                                                 if (studentRemData.ContainsKey(EnrollmentNo))
//                                                 {

//                                                     var SubjectDict = studentRemData[EnrollmentNo];
//                                                     if (SubjectDict.ContainsKey(SubjectCode))
//                                                     {
//                                                         var SubjectRemData = studentRemData[EnrollmentNo][SubjectCode];
//                                                         studentRemData[EnrollmentNo][SubjectCode] = new StudentRemSubjectData(SubjectRemData.TheoryRemRequired, 1);
//                                                     }
//                                                     else
//                                                     {
//                                                         studentRemData[EnrollmentNo][SubjectCode] = new StudentRemSubjectData(0, 1);
//                                                     }
//                                                 }
//                                                 else
//                                                 {
//                                                     studentRemData[EnrollmentNo] = new Dictionary<string, StudentRemSubjectData>();
//                                                     studentRemData[EnrollmentNo][SubjectCode] = new StudentRemSubjectData(0, 1);
//                                                 }
//                                             }

//                                         }
//                                         else if (subject.NumberOfColumn == 4)
//                                         {

//                                             GradePoint = fields[PracIndex + 1];
//                                             GradeName = fields[PracIndex + 2];
//                                             PracticalCreditGradePoint = fields[PracIndex + 3];

//                                             if ((GradePoint.Trim() == "-" || GradePoint.Trim() == "0" || GradePoint.Trim() == "" || string.IsNullOrWhiteSpace(GradePoint)) &&
//                                             (GradeName.Trim() == "-" || GradeName.Trim() == "0" || GradeName.Trim() == "" || string.IsNullOrWhiteSpace(GradeName)) &&
//                                             (PracticalCreditGradePoint.Trim() == "-" || PracticalCreditGradePoint.Trim() == "0" || PracticalCreditGradePoint.Trim() == "" || string.IsNullOrWhiteSpace(PracticalCreditGradePoint))
//                                                 )
//                                             {
//                                                 continue;
//                                             }

//                                             if (GradeName.Equals("AB", StringComparison.OrdinalIgnoreCase) || GradeName.Equals("F", StringComparison.OrdinalIgnoreCase))
//                                             {

//                                                 if (studentRemData.ContainsKey(EnrollmentNo))
//                                                 {

//                                                     var SubjectDict = studentRemData[EnrollmentNo];
//                                                     if (SubjectDict.ContainsKey(SubjectCode))
//                                                     {
//                                                         var SubjectRemData = studentRemData[EnrollmentNo][SubjectCode];
//                                                         studentRemData[EnrollmentNo][SubjectCode] = new StudentRemSubjectData(SubjectRemData.TheoryRemRequired, 1);
//                                                     }
//                                                     else
//                                                     {
//                                                         studentRemData[EnrollmentNo][SubjectCode] = new StudentRemSubjectData(0, 1);
//                                                     }
//                                                 }
//                                                 else
//                                                 {
//                                                     studentRemData[EnrollmentNo] = new Dictionary<string, StudentRemSubjectData>();
//                                                     studentRemData[EnrollmentNo][SubjectCode] = new StudentRemSubjectData(0, 1);
//                                                 }
//                                             }

//                                         }
//                                     }

//                                 }
//                             }
//                             catch (Exception ex)
//                             {
//                                 Console.WriteLine(ex.Message + "Fucked From ATKT");
//                             }

//                         }

//                     }


//                     if (worksheet == null)
//                     {   
//                         Console.WriteLine("Reassessment sheet selected");
//                         worksheet = workbook.Worksheets.FirstOrDefault(ws =>
//                         ws.Name.Contains("master", StringComparison.OrdinalIgnoreCase) &&
//                         ws.Name.Contains("Reassessment", StringComparison.OrdinalIgnoreCase));
//                     }
//                     if (worksheet == null)
//                     {

//                         worksheet = workbook.Worksheets.FirstOrDefault(ws =>
//                         ws.Name.Contains("master", StringComparison.OrdinalIgnoreCase) &&
//                         ws.Name.Contains("grace", StringComparison.OrdinalIgnoreCase));
//                     }

//                     if (worksheet == null)
//                     {

//                         worksheet = workbook.Worksheets.FirstOrDefault(ws =>
//                         ws.Name.Contains("master", StringComparison.OrdinalIgnoreCase) &&
//                         ws.Name.Contains("(R)", StringComparison.OrdinalIgnoreCase));

//                     }


//                     if (worksheet == null)
//                     {
//                         worksheet = workbook.Worksheets.FirstOrDefault(ws =>
//                         ws.Name.Contains("master", StringComparison.OrdinalIgnoreCase) &&
//                         ws.Name.Contains("regular", StringComparison.OrdinalIgnoreCase) &&
//                         !ws.Name.Contains("Remedial", StringComparison.OrdinalIgnoreCase) &&
//                         !ws.Name.Contains("ATKT", StringComparison.OrdinalIgnoreCase)
//                         );
//                     }
//                     if (worksheet == null)
//                     {
//                         worksheet = workbook.Worksheets.FirstOrDefault(ws =>
//                         ws.Name.Contains("master", StringComparison.OrdinalIgnoreCase)
//                         );
//                     }


//                     Console.WriteLine("Sheet Selected : " + worksheet.Name);
//                     var Declaresheet = workbook.Worksheets
//                 .FirstOrDefault(ws => string.Equals(ws.Name, "DECLARE Sheet", StringComparison.OrdinalIgnoreCase));

//                     //            var Gracesheet = workbook.Worksheets
//                     //.FirstOrDefault(ws => ws.Name.Contains("grace", StringComparison.OrdinalIgnoreCase));

//                     //            if (Gracesheet != null)
//                     //            {
//                     //                Console.WriteLine("Has Grace sheet");
//                     //            }


//                     if (Declaresheet != null)
//                     {
//                         // Worksheet found, proceed with your operations
//                         Console.WriteLine($"Worksheet '{Declaresheet.Name}' selected.");
//                     }
//                     else
//                     {
//                         // Worksheet not found
//                         Console.WriteLine("Worksheet 'Declare Sheet' not found.");
//                     }


//                     var rows = worksheet.RowsUsed();
//                     var declaresheetrows = Declaresheet.RowsUsed();
//                     var declaresheetheaderrow = declaresheetrows.Skip(1).First();

//                     int TotalBackLogIndex = 0;
//                     int EarnCreditIndex = 0;
//                     int TotalCreditIndex = 0;
//                     int TotalCreditFromDeclareSheet = 0;
//                     foreach (var cell in declaresheetrows.Skip(4).First().Cells())
//                     {
//                         if (cell.GetValue<string>().Contains("Total", StringComparison.OrdinalIgnoreCase))
//                         {
//                             TotalCreditIndex = cell.Address.ColumnNumber;
//                             TotalCreditFromDeclareSheet = Convert.ToInt32(declaresheetrows.Skip(4).First().Cells().ElementAt(TotalCreditIndex - 2).Value.ToString());
//                             break;
//                         }
//                     }

//                     foreach (var cell in declaresheetrows.Skip(5).First().Cells())
//                     {
//                         //Console.WriteLine($"Column {cell.Address.ColumnNumber}: {cell.GetValue<string>()}");
//                         if (cell.GetValue<string>().IndexOf("Total Backlog", StringComparison.OrdinalIgnoreCase) >= 0)
//                         {
//                             TotalBackLogIndex = cell.Address.ColumnNumber;
//                             Console.WriteLine(TotalBackLogIndex);
//                         }

//                         if (cell.GetValue<string>().Contains("Earn", StringComparison.OrdinalIgnoreCase))
//                         {
//                             EarnCreditIndex = cell.Address.ColumnNumber;
//                             Console.WriteLine(EarnCreditIndex);
//                         }

//                     }

//                     string UniversityName = declaresheetrows.Skip(1).First().Cell(1).Value.ToString().Trim();
//                     string CourseSemData = declaresheetrows.Skip(2).First().Cell(1).Value.ToString().Trim();
//                     string AcademicYearSeasonData = declaresheetrows.Skip(3).First().Cell(1).Value.ToString().Trim();

//                     Console.WriteLine(UniversityName);
//                     string courseName = string.Empty;
//                     string semester = string.Empty;
//                     string ExamType = string.Empty;
//                     string ExamType1 = string.Empty;
//                     string academicYear = string.Empty;
//                     string monthYear = string.Empty;

//                     //var CourseSemDataMatch = System.Text.RegularExpressions.Regex.Match(CourseSemData, @"\((.+?)-\s*Sem\s*(\d+)\)\s+(.+)");
//                     var CourseSemDataMatch = System.Text.RegularExpressions.Regex.Match(
//                         CourseSemData,
//                         @"\((.*?)\s*Sem\s*(\d+)\)\s+(\w+\s*\d*)"
//                     );


//                     if (CourseSemDataMatch.Success)
//                     {
//                         courseName = CourseSemDataMatch.Groups[1].Value.Trim().Trim('-').Trim(); // Extracts "BBA"
//                         semester = CourseSemDataMatch.Groups[2].Value.Trim();    // Extracts "1"
//                         ExamType = CourseSemDataMatch.Groups[3].Value.Trim();    // Extracts "ATKT 2"
//                         if (ExamType.Contains("ATKT", StringComparison.OrdinalIgnoreCase))
//                         {
//                             ExamType1 = ExamType.Replace("ATKT", "Supplementary",StringComparison.OrdinalIgnoreCase);
//                             ExamType =  "Supplementary";
//                         }
//                         else if (ExamType.Contains("Remedial", StringComparison.OrdinalIgnoreCase))
//                         {
//                             ExamType1 = ExamType.Replace("Remedial", "Supplementary", StringComparison.OrdinalIgnoreCase);
//                             ExamType = "Supplementary";

//                         }
//                         else if (ExamType.Contains("Regular", StringComparison.OrdinalIgnoreCase) && BacklogFlag == 1)
//                         {
//                             ExamType = details.ExamType;
//                             ExamType1 = "Regular";
//                         }
//                     }


//                     Console.WriteLine(CourseSemData);
//                     Console.WriteLine(courseName);
//                     Console.WriteLine(semester);
//                     Console.WriteLine(ExamType);

//                     var AcademicYearSeasonDataMatch = System.Text.RegularExpressions.Regex.Match(AcademicYearSeasonData, @"Academic Year:(\d{4}-\d{2})\s*\|\s*Month:\s*([A-Za-z]+\s+\d{4})");

//                     if (AcademicYearSeasonDataMatch.Success)
//                     {
//                         academicYear = AcademicYearSeasonDataMatch.Groups[1].Value.Trim();  // Extracts "2023-24"
//                         monthYear = AcademicYearSeasonDataMatch.Groups[2].Value.Trim();    // Extracts "January 2024"
//                     }
//                     Console.WriteLine(AcademicYearSeasonData);
//                     Console.WriteLine(academicYear);
//                     Console.WriteLine(monthYear);

//                     if(ExamType1 == String.Empty)
//                     {
//                         ExamType1 = ExamType;
//                     }
//                     string ExamName = courseName + " " + ExamType1 + " Semester " + semester + " " + monthYear + " " + academicYear;


//                     string[] DeclareSheetHeaders = declaresheetrows.Skip(5).First().Cells().Select(cell => cell.Value.ToString()).ToArray();
//                     string[] DeclareSheetHeaders1 = declaresheetrows.Skip(6).First().Cells().Select(cell => cell.Value.ToString()).ToArray();

//                     //int SubjectCodeThPracIndex = 4;
//                     //Console.WriteLine(DeclareSheetHeaders[4]);
//                     //Console.WriteLine(DeclareSheetHeaders[5]);


//                     //Read header row
//                     var header1Row = rows.First(); // Get the first row
//                     int SubjectEndingIndex = 0;
//                     int flag = 0;
//                     foreach (var cell in header1Row.Cells())
//                     {
//                         if ((cell.GetValue<string>() == "Result" || cell.GetValue<string>() == "Total GP" || cell.GetValue<string>() == "Total Credit Points" || cell.GetValue<string>() == "Total Credit Point"
//                             || cell.GetValue<string>() == "Sum CGP" || cell.GetValue<string>() == "Toal Credit Point" || cell.GetValue<string>() == "Total Credit Point ") && flag == 0)
//                         {
//                             flag = 1;
//                             SubjectEndingIndex = cell.Address.ColumnNumber;
//                         }

//                         Console.WriteLine($"Column {cell.Address.ColumnNumber}: {cell.GetValue<string>()}");
//                     }
//                     SubjectEndingIndex -= 2;
//                     int SubjectStartingIndex = 4;


//                     var headers1 = header1Row.Cells().Select(cell => cell.Value.ToString()).ToArray();

//                     //Print the headers to the console to see the content
//                     //Console.WriteLine("Headers:"); //foreach (var header in headers1)
//                     //{
//                     //    Console.WriteLine(header);
//                     //}


//                     List<string> lastheaders = new List<string>();

//                     Console.WriteLine("Last Headers");
//                     Console.WriteLine(SubjectStartingIndex);
//                     Console.WriteLine(SubjectEndingIndex);
//                     for (int i = SubjectEndingIndex + 1; i < headers1.Length;)
//                     {
//                         if (headers1[i] != "")
//                         {
//                             lastheaders.Add(headers1[i]);
//                             Console.WriteLine(headers1[i]);
//                             i++;
//                         }
//                         if (headers1[i].ToString() == "" || headers1[i] == null)
//                         {
//                             //Console.WriteLine(i);
//                             break;
//                         };
//                     }

//                     var header2Row = rows.Skip(1).First();
//                     //foreach (var cell in header2Row.Cells())
//                     //{
//                     //    Console.WriteLine($"Column {cell.Address.ColumnNumber}: {cell.GetValue<string>()}");
//                     //}


//                     var headers2 = header2Row.Cells().Select(cell => cell.Value.ToString()).ToArray();

//                     // Print the headers to the console to see the content
//                     //Console.WriteLine("Headers:");
//                     //foreach (var header in headers2)
//                     //{
//                     //    Console.WriteLine(header);
//                     //}

//                     // Process each row after the header


//                     List<Subject> Subjects = new List<Subject>();


//                     int temp = 0;
//                     int previous = SubjectStartingIndex;
//                     int tempPrevious = 0;
//                     int tempi = SubjectStartingIndex;
//                     string preName = "";
//                     string pattern = @"(?i)Credit:\s*(\d+)";


//                     for (int i = SubjectStartingIndex; i <= SubjectEndingIndex; i++)
//                     {
//                         if (headers1[i] != "")
//                         {

//                             string Code = ExtractSubjectCode(headers1[i]);

//                             string Name = "";
//                             try
//                             {
//                                 Name = ExtractSubjectName(headers1[i]);
//                             }
//                             catch (Exception ex)
//                             {
//                                 Console.WriteLine(ex.Message);
//                             }
//                             Console.WriteLine(Name);
//                             double SubjectTheoryCredit = 0;
//                             double SubjectPracticalCredit = 0;

//                             Match match = Regex.Match(headers1[i], pattern);
//                             double credit = 0;
//                             if (match.Success)
//                             {
//                                 credit = Convert.ToDouble(match.Groups[1].Value);
//                             }


//                             if (headers1[i].Contains("THEORY", StringComparison.OrdinalIgnoreCase) ||
//                                     headers1[i].Contains("THRORY", StringComparison.OrdinalIgnoreCase))
//                             {
//                                 SubjectTheoryCredit = credit;
//                             }
//                             if (headers1[i].Contains("PRACTICAL", StringComparison.OrdinalIgnoreCase))
//                             {
//                                 SubjectPracticalCredit = credit;
//                             }

//                             Subject subject = new Subject(Code, Name, 0, SubjectTheoryCredit, SubjectPracticalCredit, 0, i);
//                             Subjects.Add(subject);
//                             if (i != SubjectStartingIndex)
//                             {

//                                 int x = temp - 1;

//                                 string CommonPart = CommonPrefix(preName, Name);
//                                 if (CommonPart.Length > preName.Length * 4 / 5)
//                                 {
//                                     Subjects[x].HasPractical = 1;
//                                 }

//                                 Subjects[x].NumberOfColumn = i - previous;

//                                 previous = i;
//                             }

//                             temp++;
//                             preName = Name;
//                         }
//                         tempi++;
//                     }
//                     Subjects[temp - 1].NumberOfColumn = tempi - previous;

//                     foreach (Subject subject in Subjects)
//                     {
//                         Console.WriteLine(subject);
//                     }

//                     try
//                     {
//                         foreach (Subject subject in Subjects)
//                         {

//                             if (subject != null)
//                             {
//                                 Console.WriteLine(subject);
//                                 int Index = subject.Index;
//                                 int NumberOfColumns = subject.NumberOfColumn;

//                                 string internalData = "";
//                                 string externalData = "";
//                                 string totalData = "";
//                                 string outof100mark = "";
//                                 if (headers2[Index].Contains("CCE"))
//                                 {
//                                     internalData = headers2[Index++];
//                                 }

//                                 if (headers2[Index].Contains("SEE"))
//                                 {
//                                     externalData = headers2[Index++];
//                                 }

//                                 if (headers2[Index].Contains("Total"))
//                                 {
//                                     totalData = headers2[Index++];
//                                 }
//                                 if (headers2[Index].Contains("out"))
//                                 {
//                                     outof100mark = headers2[Index];
//                                 }

//                                 Console.WriteLine(internalData);
//                                 Console.WriteLine(externalData);
//                                 Console.WriteLine(totalData);
//                                 Console.WriteLine(outof100mark);
//                                 // Extract Internal data
//                                 if (!string.IsNullOrEmpty(internalData))
//                                 {
//                                     var internalParts = internalData.Split('\n');
//                                     if (internalParts.Length > 1)
//                                     {
//                                         var internalMarks = internalParts[1].Split('/');
//                                         if (internalMarks.Length == 2)
//                                         {
//                                             subject.InternalMaxMark = Convert.ToDouble(internalMarks[0]);
//                                             subject.InternalPassingMark = Convert.ToDouble(internalMarks[1]);
//                                         }
//                                     }
//                                 }

//                                 // Extract External data
//                                 if (!string.IsNullOrEmpty(externalData))
//                                 {
//                                     var externalParts = externalData.Split('\n');
//                                     if (externalParts.Length > 1)
//                                     {
//                                         var externalMarks = externalParts[1].Split('/');
//                                         if (externalMarks.Length == 2)
//                                         {
//                                             subject.ExternalMaxMark = Convert.ToDouble(externalMarks[0]);
//                                             subject.ExternalPassingMark = Convert.ToDouble(externalMarks[1]);

//                                         }
//                                     }
//                                 }

//                                 // Extract Total data
//                                 if (!string.IsNullOrEmpty(totalData))
//                                 {   
//                                     var totalParts = totalData.Split('\n');
//                                     if (totalParts.Length > 1)
//                                     {
//                                         var totalMarks = totalParts[1].Split('/');
//                                         if (totalMarks.Length == 2)
//                                         {
//                                             subject.TotalMaxMark = Convert.ToDouble(totalMarks[0]);
//                                             subject.TotalPassingMark = Convert.ToDouble(totalMarks[1]);
//                                         }
//                                     }
//                                 }
//                                 else
//                                 {
//                                     if (subject.InternalMaxMark != null)
//                                     {
//                                         subject.TotalMaxMark = Convert.ToDouble(subject.InternalMaxMark);
//                                         subject.TotalPassingMark = Convert.ToDouble(subject.InternalPassingMark);
//                                     }
//                                     if (subject.ExternalMaxMark != null)
//                                     {
//                                         subject.TotalMaxMark += Convert.ToDouble(subject.ExternalMaxMark);
//                                         subject.TotalMaxMark += Convert.ToDouble(subject.ExternalPassingMark);
//                                     }
//                                 }
//                                 // Check if "Grade Point" exists
//                                 //if (headers2[Index + 3].ToString() != "Grade Point")
//                                 //{
//                                 //    var parts = headers2[Index + 3].Split(' ');

//                                 //    if (parts.Length > 1)
//                                 //    {
//                                 //        subject.OutOfMark = Convert.ToDouble(parts[parts.Length - 1]);
//                                 //    }
//                                 //}
//                             }

//                         }
//                     }
//                     catch (Exception ex)
//                     {
//                         Console.WriteLine(ex.Message);

//                     }

//                     int DeclareSheetHeaderIndex = 4;

//                     for (int i = 0; i < Subjects.Count(); i++)
//                     {
//                         Subjects[i].SubjectCode = DeclareSheetHeaders[DeclareSheetHeaderIndex++];
//                     }



//                     foreach (Subject s in Subjects)
//                     {
//                         Console.WriteLine(s);
//                     }

//                     var studentData = new Dictionary<string, (List<SubjectWiseData> SubjectWiseStudentData, int Backlog, int EarnCredit)>();

//                     // Backlog and EarnCredits data from declare sheet

//                     int targetRowNumber = 9;
//                     int DeclareSheetDataRowNumber = 0;

//                     foreach (var row in declaresheetrows)
//                     {
//                         var firstCell = row.Cells().FirstOrDefault();
//                         Console.WriteLine(firstCell.Value);
//                         if (firstCell.GetValue<string>() == "1")
//                         {
//                             break;
//                         }
//                         DeclareSheetDataRowNumber++;
//                     }
//                     Console.WriteLine("Data Starting From Row Number : " + DeclareSheetDataRowNumber);


//                     foreach (var row in declaresheetrows.Skip(DeclareSheetDataRowNumber))
//                     {
//                         //SubjectCodeThPracIndex = 4;

//                         var fields = row.Cells().Select(cell => cell.Value.ToString()).ToArray();
//                         //Console.WriteLine(fields[0]);
//                         if (string.Equals(fields[0], "fail", StringComparison.OrdinalIgnoreCase) ||
//                             string.Equals(fields[0], "#REF!", StringComparison.OrdinalIgnoreCase) ||
//                             string.Equals(fields[0], "", StringComparison.OrdinalIgnoreCase)
//                             )
//                         {
//                             break;
//                         }

//                         List<SubjectWiseData> SubjectWiseStudentData = new List<SubjectWiseData>();
//                         for (int j = 0; j < Subjects.Count(); j++)
//                         {
//                             string SubjectCode = DeclareSheetHeaders[4 + j];
//                             int SubjectType = 0;
//                             //Console.WriteLine(j + " : " + SubjectCode);
//                             if (DeclareSheetHeaders1[4 + j].Contains("THEORY", StringComparison.OrdinalIgnoreCase))
//                             {
//                                 SubjectType = 0;
//                             }
//                             else if (DeclareSheetHeaders1[4 + j].Contains("PRACTICAL", StringComparison.OrdinalIgnoreCase))
//                             {
//                                 SubjectType = 1;
//                             }

//                             if (fields[4 + j] == "F" || fields[4 + j] == "f")
//                             {
//                                 SubjectWiseStudentData.Add(new SubjectWiseData(SubjectCode, SubjectType, 1));
//                             }
//                             else
//                             {
//                                 SubjectWiseStudentData.Add(new SubjectWiseData(SubjectCode, SubjectType, 0));
//                             }
//                         }
//                         try
//                         {
//                             studentData[fields[1]] = (SubjectWiseStudentData, Convert.ToInt32(fields[TotalBackLogIndex - 1]), Convert.ToInt32(fields[EarnCreditIndex - 1]));
//                         }
//                         catch
//                         {

//                             //EarnCreditProblem
//                             studentData[fields[1]] = (SubjectWiseStudentData, Convert.ToInt32(fields[TotalBackLogIndex - 1]), 0);
//                         }
//                     }


//                     foreach (var row in rows.Skip(2))
//                     {
//                         var fields = row.Cells().Select(cell => cell.Value.ToString()).ToArray();

//                         //Ensure we handle rows with insufficient columns
//                         if (fields.Length < headers2.Length / 2)
//                         {
//                             Console.WriteLine("Skipping row due to insufficient columns.");
//                             continue;
//                         }

//                         //try { /

//                         var EnrollmentNo = fields[2];
//                         var StudentName = fields[3];

//                         if (BacklogFlag == 1)
//                         {
//                             if (!studentRemData.ContainsKey(EnrollmentNo))
//                             {
//                                 continue;
//                             }
//                         }
//                         //}
//                         //            catch (Exception ex)
//                         //            {
//                         //                Console.WriteLine(ex.Message + "fucked");
//                         //}
//                         int CurrentBacklog = 0;
//                         int BacklogCount = 0;
//                         int EarnCredit = 0;
//                         double EarnCreditCount = 0;
//                         int backlog = 0;

//                         Dictionary<string, (int, int)> dict = new Dictionary<string, (int TheoryPassFail, int PracticalPassFail)>();

//                         if (studentData.ContainsKey(EnrollmentNo))
//                         {
//                             var SubjectWiseStudentData = studentData[EnrollmentNo];
//                             List<SubjectWiseData> list = studentData[EnrollmentNo].SubjectWiseStudentData;
//                             foreach (SubjectWiseData s in list)
//                             {
//                                 if (dict.ContainsKey(s.SubjectCode))
//                                 {
//                                     int ThPass = dict[s.SubjectCode].Item1;
//                                     int PrPass = dict[s.SubjectCode].Item2;

//                                     if (s.SubjectType == 0)
//                                     {
//                                         if (s.PassFail == 0)
//                                         {
//                                             dict[s.SubjectCode] = (0, PrPass);
//                                         }
//                                         else if (s.PassFail == 1)
//                                         {
//                                             dict[s.SubjectCode] = (1, PrPass);
//                                         }
//                                     }
//                                     else if (s.SubjectType == 1)
//                                     {
//                                         if (s.PassFail == 0)
//                                         {
//                                             dict[s.SubjectCode] = (ThPass, 0);
//                                         }
//                                         else if (s.PassFail == 1)
//                                         {
//                                             dict[s.SubjectCode] = (ThPass, 1);
//                                         }
//                                     }
//                                 }
//                                 else
//                                 {
//                                     if (s.SubjectType == 0)
//                                     {
//                                         if (s.PassFail == 0)
//                                         {
//                                             dict[s.SubjectCode] = (0, 0);
//                                         }
//                                         else if (s.PassFail == 1)
//                                         {

//                                             dict[s.SubjectCode] = (1, 0);
//                                         }
//                                     }
//                                     else if (s.SubjectType == 1)
//                                     {
//                                         if (s.PassFail == 0)
//                                         {
//                                             dict[s.SubjectCode] = (0, 0);
//                                         }
//                                         else if (s.PassFail == 1)
//                                         {
//                                             dict[s.SubjectCode] = (0, 1);
//                                         }
//                                     }
//                                 }
//                             }

//                             foreach (var subdata in dict)
//                             {
//                                 if (subdata.Value.Item1 == 1)
//                                 {
//                                     BacklogCount++;
//                                 }
//                                 if (subdata.Value.Item2 == 1) BacklogCount++;

//                                 //NEW
//                                 //if (subdata.Value.Item1 == 1 || subdata.Value.Item2 == 1)
//                                 //{
//                                 //    BacklogCount++;
//                                 //}
                            
//                             }

//                             CurrentBacklog = SubjectWiseStudentData.Backlog;
//                             EarnCredit = SubjectWiseStudentData.EarnCredit;
//                         }




//                         var TotalCreditPoint = "";
//                         var SemesterTotalMark = "";
//                         //var BacklogFromMasterSheet = "";
//                         var TotalCreditOffered = "";
//                         var SGPA = "";
//                         int TempIndex = SubjectEndingIndex + 1;
//                         int LastHeaderIndex = 0;


//                         while (LastHeaderIndex < lastheaders.Count() && TempIndex < fields.Length)
//                         {
//                             if (lastheaders[LastHeaderIndex] == "Total GP" ||
//                                 lastheaders[LastHeaderIndex] == "Total Credit Points" ||
//                                 lastheaders[LastHeaderIndex] == "Total Credit Point" ||
//                                 lastheaders[LastHeaderIndex] == "Sum CGP" ||
//                                 lastheaders[LastHeaderIndex] == "Toal Credit Point")
//                             {
//                                 if (TotalCreditPoint == "")
//                                 {
//                                     TotalCreditPoint = fields[TempIndex];
//                                 }

//                                 LastHeaderIndex++;
//                                 TempIndex++;
//                             }
//                             //else if (BacklogFlag==1 && lastheaders[LastHeaderIndex].Contains("Total Backlog",StringComparison.OrdinalIgnoreCase))
//                             //{
//                             //    BacklogFromMasterSheet = fields[TempIndex];
//                             //    LastHeaderIndex++;
//                             //    TempIndex++;
//                             //}
//                             else if (lastheaders[LastHeaderIndex].Contains("Total Marks") || lastheaders[LastHeaderIndex].Contains("Total Mark"))
//                             {

//                                 SemesterTotalMark = fields[TempIndex];
//                                 TempIndex++;
//                                 LastHeaderIndex++;
//                             }
//                             else if (lastheaders[LastHeaderIndex] == "Registered Credit" ||
//                                     lastheaders[LastHeaderIndex] == "Total Credit")
//                             {

//                                 TotalCreditOffered = fields[TempIndex];
//                                 TempIndex++;
//                                 LastHeaderIndex++;
//                             }
//                             else if (lastheaders[LastHeaderIndex] == "SGPA")
//                             {
//                                 SGPA = Math.Round(Convert.ToDouble(fields[TempIndex]), 2).ToString();
//                                 TempIndex++;
//                                 LastHeaderIndex++;
//                             }
//                             else
//                             {
//                                 LastHeaderIndex++;
//                                 TempIndex++;
//                             }
//                         }

//                         TotalCreditOffered = TotalCreditFromDeclareSheet.ToString();

//                         try
//                         {

//                             for (int i = 0; i < Subjects.Count(); i++)
//                             {
//                                 Subject subject = Subjects[i];

//                                 var SubjectCode = "";
//                                 if (subject.SubjectCode != "")
//                                 {
//                                     SubjectCode = subject.SubjectCode;
//                                 }

//                                 var SubjectName = subject.SubjectName;


//                                 //Double CreditPoint = 0;

//                                 if (BacklogFlag == 1)
//                                 {
//                                     var studentRemSubjectData = studentRemData[EnrollmentNo];
//                                     if (!studentRemSubjectData.ContainsKey(SubjectCode))
//                                     {
//                                         continue;
//                                     }

//                                 }

//                                 var IsPass = 1;   //new
//                                 var HeadName = "";
//                                 var MarkMax = "";
//                                 var MarkPassing = "";
//                                 var MarkObtainedOriginal = "";


//                                 var MarkGracingRequired = "";
//                                 var MarkGracingAlloted = "";
//                                 var MarkObtainedAfterGracing = "";
                                



//                                 var PassFail = "PASS";
//                                 var IsFailInTheory = 0;
//                                 var IsFailInPractical = 0;
//                                 var IsFailInInternalTheory = 0;
//                                 var IsFailInExternalTheory = 0;
//                                 var IsFailInInternal = 0;
//                                 var IsFailInExternal = 0;
//                                 var IsFailInInternalPractical = 0;
//                                 var IsFailInExternalPractical = 0;

//                                 var Credit = "";
//                                 int Index = subject.Index;
//                                 var InternalTheoryObtainedMark = "";


//                                 var ExternalTheoryObtainedMark = "";
//                                 var TheoryObtainedMark = "";

//                                 var TheoryOutOfMark = "";   // *

//                                 var GradePoint = "";  //Th Grade Point
//                                 var GradeName = "";   // Th Grade Name

//                                 var TheoryCreditGradePoint = "";  //  *

//                                 var InternalTheoryMaxMark = "";
//                                 var InternalTheoryPassingMark = "";
//                                 var ExternalTheoryMaxMark = "";
//                                 var ExternalTheoryPassingMark = "";
//                                 var TotalTheoryMaxMark = "";  //TheoryMaxMark
//                                 var TotalTheoryPassingMark = "";  //TheoryPassingMark


//                                 Double? InternalObtainedMark = null;
//                                 Double? ExternalObtainedMark = null;
//                                 Double? TotalObtainedMark = null;

//                                 var InternalPracticalObtainedMark = "";
//                                 var ExternalPracticalObtainedMark = "";
//                                 var PracticalObtainedMark = "";

//                                 var PracticalOutOfMark = "";  // *



//                                 //var PracticalGradeName = "";
//                                 var PracticalCreditGradePoint = "";  // *
//                                 var InternalPracticalPassingMark = "";
//                                 var ExternalPracticalMaxMark = "";


//                                 var InternalPracticalMaxMark = "";
//                                 var ExternalPracticalPassingMark = "";
//                                 var PracticalMaxMark = "";
//                                 var PracticalPassingMark = "";

//                                 var InternalMaxMark = subject.InternalMaxMark;
//                                 var InternalPassingMark = subject.InternalPassingMark;
//                                 var ExternalMaxMark = subject.ExternalMaxMark;
//                                 var ExternalPassingMark = subject.ExternalPassingMark;

//                                 var CreditPoint = "";

//                                 double? TotalMaxMark = null;
//                                 double? TotalPassingMark = null;


//                                 int IsCompositeRequired = 0;

//                                 if (subject.SubjectTheoryCredit != 0)
//                                 {   

//                                     if (BacklogFlag == 1)
//                                     {
//                                         var studentRemSubjectData = studentRemData[EnrollmentNo];
//                                         var data = studentRemSubjectData[SubjectCode];
//                                         if (data.TheoryRemRequired == 0)
//                                         {
//                                             continue;
//                                         }

//                                     }

//                                     Credit = subject.SubjectTheoryCredit.ToString();
//                                     IsFailInTheory = dict.ContainsKey(SubjectCode) ? dict[SubjectCode].Item1 : 1;
//                                     PassFail = IsFailInTheory == 0 ? "PASS" : "FAIL";

//                                     InternalTheoryMaxMark = subject.InternalMaxMark.ToString();
//                                     InternalTheoryPassingMark = subject.InternalPassingMark.ToString();
//                                     ExternalTheoryMaxMark = subject.ExternalMaxMark.ToString();
//                                     ExternalTheoryPassingMark = subject.ExternalPassingMark.ToString();
//                                     TotalTheoryMaxMark = subject.TotalMaxMark.ToString();
//                                     TotalTheoryPassingMark = subject.TotalPassingMark.ToString();

//                                     TotalMaxMark = Convert.ToDouble(TotalTheoryMaxMark);
//                                     TotalPassingMark = Convert.ToDouble(TotalTheoryPassingMark);

//                                     if (subject.NumberOfColumn == 7)
//                                     {
//                                         IsCompositeRequired = 1;
//                                         InternalTheoryObtainedMark = (fields[Index].Equals("AB", StringComparison.OrdinalIgnoreCase) ? null : fields[Index]);
//                                         ExternalTheoryObtainedMark = fields[Index + 1].Equals("AB", StringComparison.OrdinalIgnoreCase) ? null : fields[Index + 1];
//                                         TheoryObtainedMark = fields[Index + 2];


//                                         if (TheoryObtainedMark.Equals("AB", StringComparison.OrdinalIgnoreCase))
//                                         {
//                                             TheoryObtainedMark = (Convert.ToDouble(InternalTheoryObtainedMark) + Convert.ToDouble(ExternalTheoryObtainedMark)).ToString();
//                                         }

//                                         string TheoryMark = fields[Index + 3].ToString();
//                                         double roundedValue;

//                                         if (double.TryParse(TheoryMark, out roundedValue))
//                                         {
//                                             // If the value is numeric, round it to 2 decimal places
//                                             TheoryMark = Math.Round(roundedValue, 2).ToString();
//                                         }
//                                         else
//                                         {
//                                             // If the value is not numeric (e.g., "AB"), leave it as is
//                                             if (TheoryMark.Trim() == "-" || TheoryMark.Trim() == "0" || TheoryMark.Trim() == "" || string.IsNullOrWhiteSpace(TheoryMark))
//                                             {
//                                                 TheoryMark = TheoryMark.Trim();
//                                             }
//                                             else
//                                             {
//                                                 double mulfactor = 1;
//                                                 if (Convert.ToDouble(TotalTheoryMaxMark) == 100)
//                                                 {
//                                                     mulfactor = 1;
//                                                 }
//                                                 else if (Convert.ToDouble(TotalTheoryMaxMark) == 50)
//                                                 {
//                                                     mulfactor = 2;
//                                                 }
//                                                 else if (Convert.ToDouble(TotalTheoryMaxMark) == 25)
//                                                 {
//                                                     mulfactor = 4;
//                                                 }
//                                                 else if (Convert.ToDouble(TotalTheoryMaxMark) == 150)
//                                                 {
//                                                     mulfactor = 2 / 3;
//                                                 }
//                                                 TheoryMark = (Convert.ToDouble(TheoryObtainedMark) * mulfactor).ToString();
//                                             }
//                                         }

//                                         TheoryOutOfMark = TheoryMark;
//                                         GradePoint = fields[Index + 4].Equals("AB", StringComparison.OrdinalIgnoreCase) ? "0" : fields[Index + 4];
//                                         GradeName = fields[Index + 5];

//                                         TheoryCreditGradePoint = fields[Index + 6].Equals("AB", StringComparison.OrdinalIgnoreCase) ? "0" : fields[Index + 6];

//                                         if ((InternalTheoryObtainedMark?.Trim() == "-" || InternalTheoryObtainedMark?.Trim() == "0" || InternalTheoryObtainedMark?.Trim() == "" || string.IsNullOrWhiteSpace(InternalTheoryObtainedMark)) &&
//                                             (ExternalTheoryObtainedMark?.Trim() == "-" || ExternalTheoryObtainedMark?.Trim() == "0" || ExternalTheoryObtainedMark?.Trim() == "" || string.IsNullOrWhiteSpace(ExternalTheoryObtainedMark)) &&
//                                             (TheoryObtainedMark.Trim() == "-" || TheoryObtainedMark.Trim() == "0" || TheoryObtainedMark.Trim() == "" || string.IsNullOrWhiteSpace(TheoryObtainedMark)) &&
//                                             (TheoryOutOfMark.Trim() == "-" || TheoryOutOfMark.Trim() == "0" || TheoryOutOfMark.Trim() == "" || string.IsNullOrWhiteSpace(TheoryOutOfMark)) &&
//                                             (GradePoint.Trim() == "-" || GradePoint.Trim() == "0" || GradePoint.Trim() == "" || string.IsNullOrWhiteSpace(GradePoint)) &&
//                                             (GradeName.Trim() == "-" || GradeName.Trim() == "0" || GradeName.Trim() == "" || string.IsNullOrWhiteSpace(GradeName)) &&
//                                             (TheoryCreditGradePoint.Trim() == "-" || TheoryCreditGradePoint.Trim() == "0" || TheoryCreditGradePoint.Trim() == "" || string.IsNullOrWhiteSpace(TheoryCreditGradePoint))
//                                             )
//                                         {
//                                             continue;
//                                         }

//                                         if (Convert.ToDouble(InternalTheoryObtainedMark) < Convert.ToDouble(InternalTheoryPassingMark))
//                                         {
//                                             PassFail = "FAIL";
//                                             IsFailInTheory = 1;
//                                             IsFailInInternalTheory = 1;

//                                             IsFailInInternal = 1;
//                                         }

//                                         if (Convert.ToDouble(ExternalTheoryObtainedMark) < Convert.ToDouble(ExternalTheoryPassingMark))
//                                         {
//                                             PassFail = "FAIL";
//                                             IsFailInTheory = 1;
//                                             IsFailInExternalTheory = 1;
//                                             IsFailInExternal = 1;
//                                         }


//                                         //IT
//                                         NewMarkHeadExcelFileTable.Rows.Add(
//                                             null,//ExamStudentSubjectLID
//                                             null,//ExamLID
//                                             null,//ExamStudentLID
//                                             null,//ExamStudentSubjectMarkHeadLID
//                                             SubjectCode,//SubjectCode
//                                             SubjectName,//SubjectName
//                                             EnrollmentNo,//EnrollmentNo
//                                             StudentName,//StudentName
//                                             "IT",
//                                             InternalTheoryMaxMark,//MaxMark
//                                             InternalTheoryPassingMark,//PassingMark
//                                             InternalTheoryObtainedMark,//ObtainedMark   
//                                             null,
//                                             null,
//                                             null,
//                                             IsFailInInternalTheory == 1 ? 0 : 1,//IsPass
//                                             null,//GradeName
//                                             null,//GradePoint
//                                             null,//CreditPoint
//                                             null,//Credit
//                                             TotalCreditPoint,//TotalCreditPoint
//                                             SemesterTotalMark,//SemesterTotalMark
//                                             TotalCreditOffered,//TotalCreditOffered
//                                             SGPA,//SGPA
//                                             null,//CurrentBacklog
//                                             BacklogCount,//TotalBacklog
//                                             EarnCredit,//EarnCredit
//                                             ExamType,
//                                             academicYear,
//                                             courseName,
//                                             semester,
//                                             monthYear,
//                                             ExamName
//                                             );


//                                         //ET
                                        
//                                         NewMarkHeadExcelFileTable.Rows.Add(
//                                             null,//ExamStudentSubjectLID
//                                             null,//ExamLID
//                                             null,//ExamStudentLID
//                                             null,//ExamStudentSubjectMarkHeadLID
//                                             SubjectCode,//SubjectCode
//                                             SubjectName,//SubjectName
//                                             EnrollmentNo,//EnrollmentNo
//                                             StudentName,//StudentName
//                                             "ET",
//                                             ExternalTheoryMaxMark,//MaxMark
//                                             ExternalTheoryPassingMark,//PassingMark
//                                             ExternalTheoryObtainedMark,//ObtainedMark   
//                                             null,
//                                             null,
//                                             null,
//                                             IsFailInExternalTheory == 1 ? 0 : 1,//IsPass
//                                             null,//GradeName
//                                             null,//GradePoint
//                                             null,//CreditPoint
//                                             null,//Credit
//                                             TotalCreditPoint,//TotalCreditPoint
//                                             SemesterTotalMark,//SemesterTotalMark
//                                             TotalCreditOffered,//TotalCreditOffered
//                                             SGPA,//SGPA
//                                             null,//CurrentBacklog
//                                             BacklogCount,//TotalBacklog
//                                             EarnCredit,//EarnCredit
//                                             ExamType,
//                                             academicYear,
//                                             courseName,
//                                             semester,
//                                             monthYear,
//                                             ExamName
//                                             );


//                                     }
//                                     else if (subject.NumberOfColumn == 6)
//                                     {
//                                         IsCompositeRequired = 1;
//                                         InternalTheoryObtainedMark = (fields[Index].Equals("AB", StringComparison.OrdinalIgnoreCase) ? null : fields[Index]);
//                                         ExternalTheoryObtainedMark = fields[Index + 1].Equals("AB", StringComparison.OrdinalIgnoreCase) ? null : fields[Index + 1];
//                                         TheoryObtainedMark = fields[Index + 2];



//                                         if (TheoryObtainedMark.Equals("AB", StringComparison.OrdinalIgnoreCase))
//                                         {
//                                             TheoryObtainedMark = (Convert.ToDouble(InternalTheoryObtainedMark) + Convert.ToDouble(ExternalTheoryObtainedMark)).ToString();
//                                         }

//                                         if (row.Cell(Index + 3).FormulaA1.Contains("*2") ||
//                                             row.Cell(Index + 3).FormulaA1.Contains("* 2")
//                                         )
//                                         {
//                                             TheoryObtainedMark = (Convert.ToDouble(fields[Index + 2]) / 2).ToString();
//                                             TheoryOutOfMark = fields[Index + 2];
//                                         }

//                                         GradePoint = fields[Index + 3].Equals("AB", StringComparison.OrdinalIgnoreCase) ? "0" : fields[Index + 3];

//                                         GradeName = fields[Index + 4];

//                                         TheoryCreditGradePoint = fields[Index + 5].Equals("AB", StringComparison.OrdinalIgnoreCase) ? "0" : fields[Index + 5];

//                                         if ((InternalTheoryObtainedMark?.Trim() == "-" || InternalTheoryObtainedMark?.Trim() == "0" || InternalTheoryObtainedMark?.Trim() == "" || string.IsNullOrWhiteSpace(InternalTheoryObtainedMark)) &&
//                                             (ExternalTheoryObtainedMark?.Trim() == "-" || ExternalTheoryObtainedMark?.Trim() == "0" || ExternalTheoryObtainedMark?.Trim() == "" || string.IsNullOrWhiteSpace(ExternalTheoryObtainedMark)) &&
//                                             (TheoryObtainedMark.Trim() == "-" || TheoryObtainedMark.Trim() == "0" || TheoryObtainedMark.Trim() == "" || string.IsNullOrWhiteSpace(TheoryObtainedMark)) &&
//                                             (GradePoint.Trim() == "-" || GradePoint.Trim() == "0" || GradePoint.Trim() == "" || string.IsNullOrWhiteSpace(GradePoint)) &&
//                                             (GradeName.Trim() == "-" || GradeName.Trim() == "0" || GradeName.Trim() == "" || string.IsNullOrWhiteSpace(GradeName)) &&
//                                             (TheoryCreditGradePoint.Trim() == "-" || TheoryCreditGradePoint.Trim() == "0" || TheoryCreditGradePoint.Trim() == "" || string.IsNullOrWhiteSpace(TheoryCreditGradePoint))
//                                             )
//                                         {
//                                             continue;
//                                         }

//                                         if (Convert.ToDouble(InternalTheoryObtainedMark) < Convert.ToDouble(InternalTheoryPassingMark))
//                                         {
//                                             PassFail = "FAIL";
//                                             IsFailInTheory = 1;
//                                             IsFailInInternalTheory = 1;
//                                             IsFailInInternal = 1;
//                                         }

//                                         if (Convert.ToDouble(ExternalTheoryObtainedMark) < Convert.ToDouble(ExternalTheoryPassingMark))
//                                         {
//                                             PassFail = "FAIL";
//                                             IsFailInTheory = 1;
//                                             IsFailInExternalTheory = 1;
//                                             IsFailInExternal = 1;
//                                         }

//                                         //IT
//                                         NewMarkHeadExcelFileTable.Rows.Add(
//                                             null,//ExamStudentSubjectLID
//                                             null,//ExamLID
//                                             null,//ExamStudentLID
//                                             null,//ExamStudentSubjectMarkHeadLID
//                                             SubjectCode,//SubjectCode
//                                             SubjectName,//SubjectName
//                                             EnrollmentNo,//EnrollmentNo
//                                             StudentName,//StudentName
//                                             "IT",
//                                             InternalTheoryMaxMark,//MaxMark
//                                             InternalTheoryPassingMark,//PassingMark
//                                             InternalTheoryObtainedMark,//ObtainedMark   
//                                             null,
//                                             null,
//                                             null,
//                                             IsFailInInternalTheory == 1 ? 0 : 1,//IsPass
//                                             null,//GradeName
//                                             null,//GradePoint
//                                             null,//CreditPoint
//                                             null,//Credit
//                                             TotalCreditPoint,//TotalCreditPoint
//                                             SemesterTotalMark,//SemesterTotalMark
//                                             TotalCreditOffered,//TotalCreditOffered
//                                             SGPA,//SGPA
//                                             null,//CurrentBacklog
//                                             BacklogCount,//TotalBacklog
//                                             EarnCredit,//EarnCredit
//                                             ExamType,
//                                             academicYear,
//                                             courseName,
//                                             semester,
//                                             monthYear,
//                                             ExamName
//                                             );


//                                         //ET

//                                         NewMarkHeadExcelFileTable.Rows.Add(
//                                             null,//ExamStudentSubjectLID
//                                             null,//ExamLID
//                                             null,//ExamStudentLID
//                                             null,//ExamStudentSubjectMarkHeadLID
//                                             SubjectCode,//SubjectCode
//                                             SubjectName,//SubjectName
//                                             EnrollmentNo,//EnrollmentNo
//                                             StudentName,//StudentName
//                                             "ET",
//                                             ExternalTheoryMaxMark,//MaxMark
//                                             ExternalTheoryPassingMark,//PassingMark
//                                             ExternalTheoryObtainedMark,//ObtainedMark   
//                                             null,
//                                             null,
//                                             null,
//                                             IsFailInExternalTheory == 1 ? 0 : 1,//IsPass
//                                             null,//GradeName
//                                             null,//GradePoint
//                                             null,//CreditPoint
//                                             null,//Credit
//                                             TotalCreditPoint,//TotalCreditPoint
//                                             SemesterTotalMark,//SemesterTotalMark
//                                             TotalCreditOffered,//TotalCreditOffered
//                                             SGPA,//SGPA
//                                             null,//CurrentBacklog
//                                             BacklogCount,//TotalBacklog
//                                             EarnCredit,//EarnCredit
//                                             ExamType,
//                                             academicYear,
//                                             courseName,
//                                             semester,
//                                             monthYear,
//                                             ExamName
//                                             );

//                                     }
//                                     else if (subject.NumberOfColumn == 5)
//                                     {
//                                         IsCompositeRequired = 1;
//                                         InternalTheoryObtainedMark = (fields[Index].Equals("AB", StringComparison.OrdinalIgnoreCase) ? null : fields[Index]);



//                                         TheoryObtainedMark = fields[Index + 1];

//                                         if (TheoryObtainedMark.Equals("AB", StringComparison.OrdinalIgnoreCase))
//                                         {
//                                             TheoryObtainedMark = (Convert.ToDouble(InternalTheoryObtainedMark) + Convert.ToDouble(ExternalTheoryObtainedMark)).ToString();
//                                         }

//                                         if (row.Cell(Index + 2).FormulaA1.Contains("*2") ||
//                                         row.Cell(Index + 2).FormulaA1.Contains("* 2")
//                                         )
//                                         {
//                                             TheoryObtainedMark = (Convert.ToDouble(fields[Index + 1]) / 2).ToString();
//                                             TheoryOutOfMark = fields[Index + 1];
//                                         }

//                                         GradePoint = fields[Index + 2].Equals("AB", StringComparison.OrdinalIgnoreCase) ? "0" : fields[Index + 2];
//                                         GradeName = fields[Index + 3];

//                                         TheoryCreditGradePoint = fields[Index + 4].Equals("AB", StringComparison.OrdinalIgnoreCase) ? "0" : fields[Index + 4];

//                                         if ((InternalTheoryObtainedMark?.Trim() == "-" || InternalTheoryObtainedMark?.Trim() == "0" || InternalTheoryObtainedMark?.Trim() == "" || string.IsNullOrWhiteSpace(InternalTheoryObtainedMark)) &&
//                                             (TheoryObtainedMark.Trim() == "-" || TheoryObtainedMark.Trim() == "0" || TheoryObtainedMark.Trim() == "" || string.IsNullOrWhiteSpace(TheoryObtainedMark)) &&
//                                             (GradePoint.Trim() == "-" || GradePoint.Trim() == "0" || GradePoint.Trim() == "" || string.IsNullOrWhiteSpace(GradePoint)) &&
//                                             (GradeName.Trim() == "-" || GradeName.Trim() == "0" || GradeName.Trim() == "" || string.IsNullOrWhiteSpace(GradeName)) &&
//                                             (TheoryCreditGradePoint.Trim() == "-" || TheoryCreditGradePoint.Trim() == "0" || TheoryCreditGradePoint.Trim() == "" || string.IsNullOrWhiteSpace(TheoryCreditGradePoint))
//                                             )
//                                         {
//                                             continue;
//                                         }

//                                         if (Convert.ToDouble(InternalTheoryObtainedMark) < Convert.ToDouble(InternalTheoryPassingMark))
//                                         {
//                                             PassFail = "FAIL";
//                                             IsFailInTheory = 1;
//                                             IsFailInInternalTheory = 1;
//                                             IsFailInInternal = 1;
//                                         }

//                                         //IT
//                                         NewMarkHeadExcelFileTable.Rows.Add(
//                                             null,//ExamStudentSubjectLID
//                                             null,//ExamLID
//                                             null,//ExamStudentLID
//                                             null,//ExamStudentSubjectMarkHeadLID
//                                             SubjectCode,//SubjectCode
//                                             SubjectName,//SubjectName
//                                             EnrollmentNo,//EnrollmentNo
//                                             StudentName,//StudentName
//                                             "IT",
//                                             InternalTheoryMaxMark,//MaxMark
//                                             InternalTheoryPassingMark,//PassingMark
//                                             InternalTheoryObtainedMark,//ObtainedMark   
//                                             null,
//                                             null,
//                                             null,
//                                             IsFailInInternalTheory == 1 ? 0 : 1,//IsPass
//                                             null,//GradeName
//                                             null,//GradePoint
//                                             null,//CreditPoint
//                                             null,//Credit
//                                             TotalCreditPoint,//TotalCreditPoint
//                                             SemesterTotalMark,//SemesterTotalMark
//                                             TotalCreditOffered,//TotalCreditOffered
//                                             SGPA,//SGPA
//                                             null,//CurrentBacklog
//                                             BacklogCount,//TotalBacklog
//                                             EarnCredit,//EarnCredit
//                                             ExamType,
//                                             academicYear,
//                                             courseName,
//                                             semester,
//                                             monthYear,
//                                             ExamName
//                                             );

//                                     }
//                                     else if (subject.NumberOfColumn == 4)
//                                     {
//                                         IsCompositeRequired = 1;
//                                         InternalTheoryObtainedMark = fields[Index].Equals("AB", StringComparison.OrdinalIgnoreCase) ? null : fields[Index];


//                                         TheoryObtainedMark = fields[Index];

//                                         if (TheoryObtainedMark.Equals("AB", StringComparison.OrdinalIgnoreCase))
//                                         {
//                                             TheoryObtainedMark = (Convert.ToDouble(InternalTheoryObtainedMark) + Convert.ToDouble(ExternalTheoryObtainedMark)).ToString();
//                                         }

//                                         GradePoint = fields[Index + 1].Equals("AB", StringComparison.OrdinalIgnoreCase) ? "0" : fields[Index + 1];
//                                         GradeName = fields[Index + 2];
//                                         TheoryCreditGradePoint = fields[Index + 3].Equals("AB", StringComparison.OrdinalIgnoreCase) ? "0" : fields[Index + 3];

//                                         if ((InternalTheoryObtainedMark?.Trim() == "-" || InternalTheoryObtainedMark?.Trim() == "0" || InternalTheoryObtainedMark?.Trim() == "" || string.IsNullOrWhiteSpace(InternalTheoryObtainedMark)) &&
//                                         (GradePoint.Trim() == "-" || GradePoint.Trim() == "0" || GradePoint.Trim() == "" || string.IsNullOrWhiteSpace(GradePoint)) &&
//                                             (GradeName.Trim() == "-" || GradeName.Trim() == "0" || GradeName.Trim() == "" || string.IsNullOrWhiteSpace(GradeName)) &&
//                                             (TheoryCreditGradePoint.Trim() == "-" || TheoryCreditGradePoint.Trim() == "0" || TheoryCreditGradePoint.Trim() == "" || string.IsNullOrWhiteSpace(TheoryCreditGradePoint))
//                                             )
//                                         {
//                                             continue;
//                                         }

//                                         if (Convert.ToDouble(InternalTheoryObtainedMark) < Convert.ToDouble(InternalTheoryPassingMark))
//                                         {
//                                             PassFail = "FAIL";
//                                             IsFailInTheory = 1;
//                                             IsFailInInternalTheory = 1;
//                                             IsFailInInternal = 1;
//                                         }

//                                         //IT
//                                         NewMarkHeadExcelFileTable.Rows.Add(
//                                             null,//ExamStudentSubjectLID
//                                             null,//ExamLID
//                                             null,//ExamStudentLID
//                                             null,//ExamStudentSubjectMarkHeadLID
//                                             SubjectCode,//SubjectCode
//                                             SubjectName,//SubjectName
//                                             EnrollmentNo,//EnrollmentNo
//                                             StudentName,//StudentName
//                                             "IT",
//                                             InternalTheoryMaxMark,//MaxMark
//                                             InternalTheoryPassingMark,//PassingMark
//                                             InternalTheoryObtainedMark,//ObtainedMark   
//                                             null,
//                                             null,
//                                             null,
//                                             IsFailInInternalTheory == 1 ? 0 : 1,//IsPass
//                                             null,//GradeName
//                                             null,//GradePoint
//                                             null,//CreditPoint
//                                             null,//Credit
//                                             TotalCreditPoint,//TotalCreditPoint
//                                             SemesterTotalMark,//SemesterTotalMark
//                                             TotalCreditOffered,//TotalCreditOffered
//                                             SGPA,//SGPA
//                                             null,//CurrentBacklog
//                                             BacklogCount,//TotalBacklog
//                                             EarnCredit,//EarnCredit
//                                             ExamType,
//                                             academicYear,
//                                             courseName,
//                                             semester,
//                                             monthYear,
//                                             ExamName
//                                             );
//                                     }


//                                     CreditPoint = TheoryCreditGradePoint;
//                                     if (double.TryParse(fields[Index], out double result))
//                                     {
//                                         InternalObtainedMark = result;
//                                     }
//                                     else
//                                     {
//                                         InternalObtainedMark = 0; // Default value if parsing fails
//                                     }

//                                     if (subject.NumberOfColumn != 4 && subject.NumberOfColumn != 5)
//                                     {
//                                         if (double.TryParse(fields[Index + 1], out double result1))
//                                         {
//                                             ExternalObtainedMark = result1;
//                                         }
//                                         else
//                                         {
//                                             ExternalObtainedMark = 0; // Default value if parsing fails
//                                         }
//                                     }

//                                     TotalObtainedMark = Convert.ToDouble(InternalObtainedMark) + Convert.ToDouble(ExternalObtainedMark);
//                                     //Composite T
//                                     if (IsCompositeRequired == 1)
//                                     {
//                                     NewMarkHeadExcelFileTable.Rows.Add(
//                                     null,//ExamStudentSubjectLID
//                                     null,//ExamLID
//                                     null,//ExamStudentLID
//                                     null,//ExamStudentSubjectMarkHeadLID
//                                     SubjectCode,//SubjectCode
//                                     SubjectName,//SubjectName
//                                     EnrollmentNo,//EnrollmentNo
//                                     StudentName,//StudentName
//                                     "T",
//                                     TotalMaxMark,//MaxMark
//                                     TotalPassingMark,//PassingMark
//                                     TotalObtainedMark,//ObtainedMark   
//                                     null,
//                                     null,
//                                     null,
//                                     IsFailInTheory == 1 ? 0 : 1,//IsPass
//                                     GradeName,//GradeName
//                                     GradePoint,//GradePoint
//                                     CreditPoint,//CreditPoint
//                                     Credit,//Credit
//                                     TotalCreditPoint,//TotalCreditPoint
//                                     SemesterTotalMark,//SemesterTotalMark
//                                     TotalCreditOffered,//TotalCreditOffered
//                                     SGPA,//SGPA
//                                     null,//CurrentBacklog
//                                     BacklogCount,//TotalBacklog
//                                     EarnCredit,//EarnCredit
//                                     ExamType,
//                                     academicYear,
//                                     courseName,
//                                     semester,
//                                     monthYear,
//                                     ExamName
//                                 );

//                                     }
                                

//                                 }
//                                 else if (subject.SubjectPracticalCredit != 0)
//                                 {

//                                     IsCompositeRequired = 1;
//                                     if (BacklogFlag == 1)
//                                     {
//                                         var studentRemSubjectData = studentRemData[EnrollmentNo];
//                                         var data = studentRemSubjectData[SubjectCode];
//                                         if (data.PracticalRemRequired == 0)
//                                         {

//                                             continue;
//                                         }

//                                     }


//                                     Credit = subject.SubjectPracticalCredit.ToString();
//                                     IsFailInPractical = dict.ContainsKey(SubjectCode) ? dict[SubjectCode].Item2 : 1;
//                                     PassFail = IsFailInPractical == 0 ? "PASS" : "FAIL";

//                                     InternalPracticalMaxMark = subject.InternalMaxMark.ToString();
//                                     InternalPracticalPassingMark = subject.InternalPassingMark.ToString();
//                                     ExternalPracticalMaxMark = subject.ExternalMaxMark.ToString();
//                                     ExternalPracticalPassingMark = subject.ExternalPassingMark.ToString();
//                                     PracticalMaxMark = subject.TotalMaxMark.ToString();
//                                     PracticalPassingMark = subject.TotalPassingMark.ToString();

//                                     TotalMaxMark = Convert.ToDouble(PracticalMaxMark);
//                                     TotalPassingMark = Convert.ToDouble(PracticalPassingMark);


//                                     int PracIndex = subject.Index;
//                                     InternalPracticalObtainedMark = fields[PracIndex];
//                                     ExternalPracticalObtainedMark = fields[PracIndex + 1];
//                                     PracticalObtainedMark = fields[PracIndex + 2];

//                                     if (subject.NumberOfColumn == 7)
//                                     {
//                                         InternalPracticalObtainedMark = fields[PracIndex].Equals("AB", StringComparison.OrdinalIgnoreCase) ? null : fields[PracIndex];
//                                         ExternalPracticalObtainedMark = fields[PracIndex + 1].Equals("AB", StringComparison.OrdinalIgnoreCase) ? null : fields[PracIndex + 1];

//                                         PracticalObtainedMark = fields[PracIndex + 2];


//                                         //if (PracticalObtainedMark.Equals("AB", StringComparison.OrdinalIgnoreCase))
//                                         //{
//                                         //    PracticalObtainedMark = (Convert.ToDouble(InternalPracticalObtainedMark) + Convert.ToDouble(ExternalPracticalObtainedMark)).ToString();
//                                         //}

//                                         string PracticalMark = fields[PracIndex + 3].ToString();
//                                         double practicalRoundedValue;

//                                         if (double.TryParse(PracticalMark, out practicalRoundedValue))
//                                         {
//                                             PracticalMark = Math.Round(practicalRoundedValue, 2).ToString();
//                                         }
//                                         else
//                                         {

//                                             PracticalMark = PracticalMark.Trim();

//                                             double mulfactor = 1;
//                                             if (Convert.ToDouble(PracticalMaxMark) == 100)
//                                             {
//                                                 mulfactor = 1;
//                                             }
//                                             else if (Convert.ToDouble(PracticalMaxMark) == 50)
//                                             {
//                                                 mulfactor = 2;
//                                             }
//                                             else if (Convert.ToDouble(PracticalMaxMark) == 25)
//                                             {
//                                                 mulfactor = 4;
//                                             }
//                                             else if (Convert.ToDouble(PracticalMaxMark) == 150)
//                                             {
//                                                 mulfactor = 2 / 3;
//                                             }
//                                             PracticalMark = (Convert.ToDouble(PracticalMaxMark) * mulfactor).ToString();

//                                         }

//                                         PracticalOutOfMark = PracticalMark;
//                                         GradePoint = fields[PracIndex + 4].Equals("AB", StringComparison.OrdinalIgnoreCase) ? "0" : fields[PracIndex + 4];
//                                         GradeName = fields[PracIndex + 5];
//                                         PracticalCreditGradePoint = fields[PracIndex + 6].Equals("AB", StringComparison.OrdinalIgnoreCase) ? "0" : fields[PracIndex + 6];

//                                         if ((InternalPracticalObtainedMark?.Trim() == "-" || InternalPracticalObtainedMark?.Trim() == "0" || InternalPracticalObtainedMark?.Trim() == "" || string.IsNullOrWhiteSpace(InternalPracticalObtainedMark)) &&
//                                             (ExternalPracticalObtainedMark?.Trim() == "-" || ExternalPracticalObtainedMark?.Trim() == "0" || ExternalPracticalObtainedMark?.Trim() == "" || string.IsNullOrWhiteSpace(ExternalPracticalObtainedMark)) &&
//                                             (PracticalObtainedMark.Trim() == "-" || PracticalObtainedMark.Trim() == "0" || PracticalObtainedMark.Trim() == "" || string.IsNullOrWhiteSpace(PracticalObtainedMark)) &&
//                                             (PracticalOutOfMark.Trim() == "-" || PracticalOutOfMark.Trim() == "0" || PracticalOutOfMark.Trim() == "" || string.IsNullOrWhiteSpace(PracticalOutOfMark)) &&
//                                             (GradePoint.Trim() == "-" || GradePoint.Trim() == "0" || GradePoint.Trim() == "" || string.IsNullOrWhiteSpace(GradePoint)) &&
//                                             (GradeName.Trim() == "-" || GradeName.Trim() == "0" || GradeName.Trim() == "" || string.IsNullOrWhiteSpace(GradeName)) &&
//                                             (PracticalCreditGradePoint.Trim() == "-" || PracticalCreditGradePoint.Trim() == "0" || PracticalCreditGradePoint.Trim() == "" || string.IsNullOrWhiteSpace(PracticalCreditGradePoint))
//                                             )
//                                         {
//                                             continue;
//                                         }

//                                         if (Convert.ToDouble(InternalPracticalObtainedMark) < Convert.ToDouble(InternalPracticalPassingMark))
//                                         {
//                                             PassFail = "FAIL";
//                                             IsFailInPractical = 1;
//                                             IsFailInInternalPractical = 1;
//                                             IsFailInInternal = 1;
//                                         }

//                                         if (Convert.ToDouble(ExternalPracticalObtainedMark) < Convert.ToDouble(ExternalPracticalPassingMark))
//                                         {
//                                             PassFail = "FAIL";
//                                             IsFailInPractical = 1;
//                                             IsFailInExternalPractical = 1;
//                                             IsFailInExternal = 1;
//                                         }

//                                         //IP
//                                         NewMarkHeadExcelFileTable.Rows.Add(
//                                             null,//ExamStudentSubjectLID
//                                             null,//ExamLID
//                                             null,//ExamStudentLID
//                                             null,//ExamStudentSubjectMarkHeadLID
//                                             SubjectCode,//SubjectCode
//                                             SubjectName,//SubjectName
//                                             EnrollmentNo,//EnrollmentNo
//                                             StudentName,//StudentName
//                                             "IP",
//                                             InternalPracticalMaxMark,//MaxMark
//                                             InternalPracticalPassingMark,//PassingMark
//                                             InternalPracticalObtainedMark,//ObtainedMark   
//                                             null,
//                                             null,
//                                             null,
//                                             IsFailInInternalPractical == 1 ? 0 : 1,//IsPass
//                                             null,//GradeName
//                                             null,//GradePoint
//                                             null,//CreditPoint
//                                             null,//Credit
//                                             TotalCreditPoint,//TotalCreditPoint
//                                             SemesterTotalMark,//SemesterTotalMark
//                                             TotalCreditOffered,//TotalCreditOffered
//                                             SGPA,//SGPA
//                                             null,//CurrentBacklog
//                                             BacklogCount,//TotalBacklog
//                                             EarnCredit,//EarnCredit
//                                             ExamType,
//                                             academicYear,
//                                             courseName,
//                                             semester,
//                                             monthYear,
//                                             ExamName
//                                             );


//                                         //EP
//                                         NewMarkHeadExcelFileTable.Rows.Add(
//                                             null,//ExamStudentSubjectLID
//                                             null,//ExamLID
//                                             null,//ExamStudentLID
//                                             null,//ExamStudentSubjectMarkHeadLID
//                                             SubjectCode,//SubjectCode
//                                             SubjectName,//SubjectName
//                                             EnrollmentNo,//EnrollmentNo
//                                             StudentName,//StudentName
//                                             "EP",
//                                             ExternalPracticalMaxMark,//MaxMark
//                                             ExternalPracticalPassingMark,//PassingMark
//                                             ExternalPracticalObtainedMark,//ObtainedMark   
//                                             null,
//                                             null,
//                                             null,
//                                             IsFailInExternalPractical == 1 ? 0 : 1,//IsPass
//                                             null,//GradeName
//                                             null,//GradePoint
//                                             null,//CreditPoint
//                                             null,//Credit
//                                             TotalCreditPoint,//TotalCreditPoint
//                                             SemesterTotalMark,//SemesterTotalMark
//                                             TotalCreditOffered,//TotalCreditOffered
//                                             SGPA,//SGPA
//                                             null,//CurrentBacklog
//                                             BacklogCount,//TotalBacklog
//                                             EarnCredit,//EarnCredit
//                                             ExamType,
//                                             academicYear,
//                                             courseName,
//                                             semester,
//                                             monthYear,
//                                             ExamName
//                                             );

//                                     }
//                                     else if (subject.NumberOfColumn == 6)
//                                     {
//                                         InternalPracticalObtainedMark = fields[PracIndex].Equals("AB", StringComparison.OrdinalIgnoreCase) ? null : fields[PracIndex];
//                                         ExternalPracticalObtainedMark = fields[PracIndex + 1].Equals("AB", StringComparison.OrdinalIgnoreCase) ? null : fields[PracIndex + 1];
//                                         PracticalObtainedMark = fields[PracIndex + 2];



//                                         if (PracticalObtainedMark.Equals("AB", StringComparison.OrdinalIgnoreCase))
//                                         {
//                                             PracticalObtainedMark = (Convert.ToDouble(InternalPracticalObtainedMark) + Convert.ToDouble(ExternalPracticalObtainedMark)).ToString();
//                                         }

//                                         if (row.Cell(PracIndex + 3).FormulaA1.Contains("*2") ||
//                                         row.Cell(PracIndex + 3).FormulaA1.Contains("* 2")
//                                     )
//                                         {
//                                             PracticalObtainedMark = (Convert.ToDouble(fields[PracIndex + 2]) / 2).ToString();
//                                             PracticalOutOfMark = fields[PracIndex + 2];
//                                         }

//                                         GradePoint = fields[PracIndex + 3].Equals("AB", StringComparison.OrdinalIgnoreCase) ? "0" : fields[PracIndex + 3];
//                                         GradeName = fields[PracIndex + 4];
//                                         PracticalCreditGradePoint = fields[PracIndex + 5].Equals("AB", StringComparison.OrdinalIgnoreCase) ? "0" : fields[PracIndex + 5];


//                                         if ((InternalPracticalObtainedMark?.Trim() == "-" || InternalPracticalObtainedMark?.Trim() == "0" || InternalPracticalObtainedMark?.Trim() == "" || string.IsNullOrWhiteSpace(InternalPracticalObtainedMark)) &&
//                                             (ExternalPracticalObtainedMark?.Trim() == "-" || ExternalPracticalObtainedMark?.Trim() == "0" || ExternalPracticalObtainedMark?.Trim() == "" || string.IsNullOrWhiteSpace(ExternalPracticalObtainedMark)) &&
//                                             (PracticalObtainedMark.Trim() == "-" || PracticalObtainedMark.Trim() == "0" || PracticalObtainedMark.Trim() == "" || string.IsNullOrWhiteSpace(PracticalObtainedMark)) &&
//                                             (GradePoint.Trim() == "-" || GradePoint.Trim() == "0" || GradePoint.Trim() == "" || string.IsNullOrWhiteSpace(GradePoint)) &&
//                                             (GradeName.Trim() == "-" || GradeName.Trim() == "0" || GradeName.Trim() == "" || string.IsNullOrWhiteSpace(GradeName)) &&
//                                             (PracticalCreditGradePoint.Trim() == "-" || PracticalCreditGradePoint.Trim() == "0" || PracticalCreditGradePoint.Trim() == "" || string.IsNullOrWhiteSpace(PracticalCreditGradePoint))
//                                             )
//                                         {

//                                             continue;
//                                         }
//                                         if (Convert.ToDouble(InternalPracticalObtainedMark) < Convert.ToDouble(InternalPracticalPassingMark))
//                                         {
//                                             PassFail = "FAIL";
//                                             IsFailInPractical = 1;
//                                             IsFailInInternalPractical = 1;
//                                             IsFailInInternal = 1;
//                                         }

//                                         if (Convert.ToDouble(ExternalPracticalObtainedMark) < Convert.ToDouble(ExternalPracticalPassingMark))
//                                         {
//                                             PassFail = "FAIL";
//                                             IsFailInPractical = 1;
//                                             IsFailInExternalPractical = 1;
//                                             IsFailInExternal = 1;
//                                         }

//                                         //IP
//                                         NewMarkHeadExcelFileTable.Rows.Add(
//                                             null,//ExamStudentSubjectLID
//                                             null,//ExamLID
//                                             null,//ExamStudentLID
//                                             null,//ExamStudentSubjectMarkHeadLID
//                                             SubjectCode,//SubjectCode
//                                             SubjectName,//SubjectName
//                                             EnrollmentNo,//EnrollmentNo
//                                             StudentName,//StudentName
//                                             "IP",
//                                             InternalPracticalMaxMark,//MaxMark
//                                             InternalPracticalPassingMark,//PassingMark
//                                             InternalPracticalObtainedMark,//ObtainedMark   
//                                             null,
//                                             null,
//                                             null,
//                                             IsFailInInternalPractical == 1 ? 0 : 1,//IsPass
//                                             null,//GradeName
//                                             null,//GradePoint
//                                             null,//CreditPoint
//                                             null,//Credit
//                                             TotalCreditPoint,//TotalCreditPoint
//                                             SemesterTotalMark,//SemesterTotalMark
//                                             TotalCreditOffered,//TotalCreditOffered
//                                             SGPA,//SGPA
//                                             null,//CurrentBacklog
//                                             BacklogCount,//TotalBacklog
//                                             EarnCredit,//EarnCredit
//                                             ExamType,
//                                             academicYear,
//                                             courseName,
//                                             semester,
//                                             monthYear,
//                                             ExamName
//                                             );


//                                         //EP
//                                         NewMarkHeadExcelFileTable.Rows.Add(
//                                             null,//ExamStudentSubjectLID
//                                             null,//ExamLID
//                                             null,//ExamStudentLID
//                                             null,//ExamStudentSubjectMarkHeadLID
//                                             SubjectCode,//SubjectCode
//                                             SubjectName,//SubjectName
//                                             EnrollmentNo,//EnrollmentNo
//                                             StudentName,//StudentName
//                                             "EP",
//                                             ExternalPracticalMaxMark,//MaxMark
//                                             ExternalPracticalPassingMark,//PassingMark
//                                             ExternalPracticalObtainedMark,//ObtainedMark   
//                                             null,
//                                             null,
//                                             null,
//                                             IsFailInExternalPractical == 1 ? 0 : 1,//IsPass
//                                             null,//GradeName
//                                             null,//GradePoint
//                                             null,//CreditPoint
//                                             null,//Credit
//                                             TotalCreditPoint,//TotalCreditPoint
//                                             SemesterTotalMark,//SemesterTotalMark
//                                             TotalCreditOffered,//TotalCreditOffered
//                                             SGPA,//SGPA
//                                             null,//CurrentBacklog
//                                             BacklogCount,//TotalBacklog
//                                             EarnCredit,//EarnCredit
//                                             ExamType,
//                                             academicYear,
//                                             courseName,
//                                             semester,
//                                             monthYear,
//                                             ExamName
//                                             );

//                                     }
//                                     else if (subject.NumberOfColumn == 5)
//                                     {
//                                         InternalPracticalObtainedMark = fields[PracIndex].Equals("AB", StringComparison.OrdinalIgnoreCase) ? null : fields[PracIndex];
//                                         PracticalObtainedMark = fields[PracIndex + 1];


//                                         if (PracticalObtainedMark.Equals("AB", StringComparison.OrdinalIgnoreCase))
//                                         {
//                                             PracticalObtainedMark = (Convert.ToDouble(InternalPracticalObtainedMark) + Convert.ToDouble(ExternalPracticalObtainedMark)).ToString();
//                                         }

//                                         if (row.Cell(PracIndex + 2).FormulaA1.Contains("*2") ||
//                                         row.Cell(PracIndex + 2).FormulaA1.Contains("* 2")
//                                             )
//                                         {
//                                             PracticalObtainedMark = (Convert.ToDouble(fields[PracIndex + 1]) / 2).ToString();
//                                             PracticalOutOfMark = fields[PracIndex + 1];
//                                         }


//                                         GradePoint = fields[PracIndex + 2].Equals("AB", StringComparison.OrdinalIgnoreCase) ? "0" : fields[PracIndex + 2];
//                                         GradeName = fields[PracIndex + 3];

//                                         PracticalCreditGradePoint = fields[PracIndex + 4].Equals("AB", StringComparison.OrdinalIgnoreCase) ? "0" : fields[PracIndex + 4];

//                                         if ((InternalPracticalObtainedMark?.Trim() == "-" || InternalPracticalObtainedMark?.Trim() == "0" || InternalPracticalObtainedMark?.Trim() == "" || string.IsNullOrWhiteSpace(InternalPracticalObtainedMark)) &&
//                                             (PracticalObtainedMark.Trim() == "-" || PracticalObtainedMark.Trim() == "0" || PracticalObtainedMark.Trim() == "" || string.IsNullOrWhiteSpace(PracticalObtainedMark)) &&
//                                             (GradePoint.Trim() == "-" || GradePoint.Trim() == "0" || GradePoint.Trim() == "" || string.IsNullOrWhiteSpace(GradePoint)) &&
//                                             (GradeName.Trim() == "-" || GradeName.Trim() == "0" || GradeName.Trim() == "" || string.IsNullOrWhiteSpace(GradeName)) &&
//                                             (PracticalCreditGradePoint.Trim() == "-" || PracticalCreditGradePoint.Trim() == "0" || PracticalCreditGradePoint.Trim() == "" || string.IsNullOrWhiteSpace(PracticalCreditGradePoint))
//                                             )
//                                         {
//                                             continue;
//                                         }
//                                         if (Convert.ToDouble(InternalPracticalObtainedMark) < Convert.ToDouble(InternalPracticalPassingMark))
//                                         {
//                                             PassFail = "FAIL";
//                                             IsFailInPractical = 1;
//                                             IsFailInInternalPractical = 1;
//                                             IsFailInInternal = 1;
//                                         }
//                                         //IP
//                                         NewMarkHeadExcelFileTable.Rows.Add(
//                                             null,//ExamStudentSubjectLID
//                                             null,//ExamLID
//                                             null,//ExamStudentLID
//                                             null,//ExamStudentSubjectMarkHeadLID
//                                             SubjectCode,//SubjectCode
//                                             SubjectName,//SubjectName
//                                             EnrollmentNo,//EnrollmentNo
//                                             StudentName,//StudentName
//                                             "IP",
//                                             InternalPracticalMaxMark,//MaxMark
//                                             InternalPracticalPassingMark,//PassingMark
//                                             InternalPracticalObtainedMark,//ObtainedMark   
//                                             null,
//                                             null,
//                                             null,
//                                             IsFailInInternalPractical == 1 ? 0 : 1,//IsPass
//                                             null,//GradeName
//                                             null,//GradePoint
//                                             null,//CreditPoint
//                                             null,//Credit
//                                             TotalCreditPoint,//TotalCreditPoint
//                                             SemesterTotalMark,//SemesterTotalMark
//                                             TotalCreditOffered,//TotalCreditOffered
//                                             SGPA,//SGPA
//                                             null,//CurrentBacklog
//                                             BacklogCount,//TotalBacklog
//                                             EarnCredit,//EarnCredit
//                                             ExamType,
//                                             academicYear,
//                                             courseName,
//                                             semester,
//                                             monthYear,
//                                             ExamName
//                                             );
//                                     }
//                                     else if (subject.NumberOfColumn == 4)
//                                     {
//                                         InternalPracticalObtainedMark = fields[PracIndex].Equals("AB", StringComparison.OrdinalIgnoreCase) ? null : fields[PracIndex];
//                                         PracticalObtainedMark = fields[PracIndex];

//                                         GradePoint = fields[PracIndex + 1].Equals("AB", StringComparison.OrdinalIgnoreCase) ? "0" : fields[PracIndex + 1];
//                                         GradeName = fields[PracIndex + 2];
//                                         PracticalCreditGradePoint = fields[PracIndex + 3].Equals("AB", StringComparison.OrdinalIgnoreCase) ? "0" : fields[PracIndex + 3];

//                                         if ((InternalPracticalObtainedMark?.Trim() == "-" || InternalPracticalObtainedMark?.Trim() == "0" || InternalPracticalObtainedMark?.Trim() == "" || string.IsNullOrWhiteSpace(InternalPracticalObtainedMark)) &&
//                                             (GradePoint.Trim() == "-" || GradePoint.Trim() == "0" || GradePoint.Trim() == "" || string.IsNullOrWhiteSpace(GradePoint)) &&
//                                             (GradeName.Trim() == "-" || GradeName.Trim() == "0" || GradeName.Trim() == "" || string.IsNullOrWhiteSpace(GradeName)) &&
//                                             (PracticalCreditGradePoint.Trim() == "-" || PracticalCreditGradePoint.Trim() == "0" || PracticalCreditGradePoint.Trim() == "" || string.IsNullOrWhiteSpace(PracticalCreditGradePoint))
//                                             )
//                                         {
//                                             continue;
//                                         }
//                                         if (Convert.ToDouble(InternalPracticalObtainedMark) < Convert.ToDouble(InternalPracticalPassingMark))
//                                         {
//                                             PassFail = "FAIL";
//                                             IsFailInPractical = 1;
//                                             IsFailInInternalPractical = 1;
//                                             IsFailInInternal = 1;
//                                         }

//                                         //IP
//                                         NewMarkHeadExcelFileTable.Rows.Add(
//                                             null,//ExamStudentSubjectLID
//                                             null,//ExamLID
//                                             null,//ExamStudentLID
//                                             null,//ExamStudentSubjectMarkHeadLID
//                                             SubjectCode,//SubjectCode
//                                             SubjectName,//SubjectName
//                                             EnrollmentNo,//EnrollmentNo
//                                             StudentName,//StudentName
//                                             "IP",
//                                             InternalPracticalMaxMark,//MaxMark
//                                             InternalPracticalPassingMark,//PassingMark
//                                             InternalPracticalObtainedMark,//ObtainedMark   
//                                             null,
//                                             null,
//                                             null,
//                                             IsFailInInternalPractical == 1 ? 0 : 1,//IsPass
//                                             null,//GradeName
//                                             null,//GradePoint
//                                             null,//CreditPoint
//                                             null,//Credit
//                                             TotalCreditPoint,//TotalCreditPoint
//                                             SemesterTotalMark,//SemesterTotalMark
//                                             TotalCreditOffered,//TotalCreditOffered
//                                             SGPA,//SGPA
//                                             null,//CurrentBacklog
//                                             BacklogCount,//TotalBacklog
//                                             EarnCredit,//EarnCredit
//                                             ExamType,
//                                             academicYear,
//                                             courseName,
//                                             semester,
//                                             monthYear,
//                                             ExamName
//                                             );
//                                     }

//                                     CreditPoint = PracticalCreditGradePoint;
//                                     if (double.TryParse(fields[PracIndex], out double result))
//                                     {
//                                         InternalObtainedMark = result;
//                                     }
//                                     else
//                                     {
//                                         InternalObtainedMark = 0; // Default value if parsing fails
//                                     }

//                                     if (subject.NumberOfColumn != 4 && subject.NumberOfColumn != 5)
//                                     {
//                                         if (double.TryParse(fields[PracIndex + 1], out double result1))
//                                         {
//                                             ExternalObtainedMark = result1;
//                                         }
//                                         else
//                                         {
//                                             ExternalObtainedMark = 0; // Default value if parsing fails
//                                         }
//                                     }




//                                     TotalObtainedMark = Convert.ToDouble(InternalObtainedMark) + Convert.ToDouble(ExternalObtainedMark);

//                                     //Composite P
//                                     if (IsCompositeRequired == 1)
//                                     {
//                                         NewMarkHeadExcelFileTable.Rows.Add(
//                                         null,//ExamStudentSubjectLID
//                                         null,//ExamLID
//                                         null,//ExamStudentLID
//                                         null,//ExamStudentSubjectMarkHeadLID
//                                         SubjectCode,//SubjectCode
//                                         SubjectName,//SubjectName
//                                         EnrollmentNo,//EnrollmentNo
//                                         StudentName,//StudentName
//                                         "P",
//                                         TotalMaxMark,//MaxMark
//                                         TotalPassingMark,//PassingMark
//                                         TotalObtainedMark,//ObtainedMark   
//                                         null,
//                                         null,
//                                         null,
//                                         IsFailInPractical == 1 ? 0 : 1,//IsPass
//                                         GradeName,//GradeName
//                                         GradePoint,//GradePoint
//                                         CreditPoint,//CreditPoint
//                                         Credit,//Credit
//                                         TotalCreditPoint,//TotalCreditPoint
//                                         SemesterTotalMark,//SemesterTotalMark
//                                         TotalCreditOffered,//TotalCreditOffered
//                                         SGPA,//SGPA
//                                         null,//CurrentBacklog
//                                         BacklogCount,//TotalBacklog
//                                         EarnCredit,//EarnCredit
//                                         ExamType,
//                                         academicYear,
//                                         courseName,
//                                         semester,
//                                         monthYear,
//                                         ExamName
//                                     );

//                                     }
//                                 }



//                             //RowAdd:

//                             //    Console.WriteLine();
//                                 //Add data to the DataTable
//                                 //NewExcelFileTable.Rows.Add(
//                                 //null,// ExamStudentSubjectLID
//                                 //null,// ExamLID
//                                 //null, //ExamStudentLID
//                                 //Credit, //Credit

//                                 //SubjectCode,  //SubjectCode
//                                 //SubjectName,  //SubjectName
//                                 //EnrollmentNo,   //EnrollmentNo
//                                 //StudentName,    //StudentName

//                                 //InternalTheoryMaxMark,  //InternalTheoryMaxMark
//                                 //InternalTheoryPassingMark, //InternalTheoryPassingMark
//                                 //InternalTheoryObtainedMark, //InternalTheoryObtainedMark
//                                 //InternalPracticalMaxMark,   //InternalPracticalMaxMark
//                                 //InternalPracticalPassingMark, //InternalPracticalPassingMark
//                                 //InternalPracticalObtainedMark, //InternalPracticalObtainedMark
//                                 //InternalMaxMark,
//                                 //InternalPassingMark,
//                                 //InternalObtainedMark,

//                                 //ExternalTheoryMaxMark,
//                                 //ExternalTheoryPassingMark,
//                                 //ExternalTheoryObtainedMark,
//                                 //ExternalPracticalMaxMark,
//                                 //ExternalPracticalPassingMark,
//                                 //ExternalPracticalObtainedMark,
//                                 //ExternalMaxMark,
//                                 //ExternalPassingMark,
//                                 //ExternalObtainedMark,

//                                 //TotalTheoryMaxMark,
//                                 //TotalTheoryPassingMark,
//                                 //TheoryObtainedMark,
//                                 //TheoryOutOfMark,
//                                 //GradePoint,
//                                 //GradeName,



//                                 //PracticalMaxMark,
//                                 //PracticalPassingMark,
//                                 //PracticalObtainedMark,
//                                 //PracticalOutOfMark,



//                                 //TotalMaxMark,
//                                 //TotalPassingMark,
//                                 //TotalObtainedMark,
//                                 //CreditPoint,
//                                 //TotalCreditPoint,
//                                 //SemesterTotalMark,
//                                 //TotalCreditOffered,
//                                 //SGPA,
//                                 //IsFailInTheory,
//                                 //IsFailInPractical,
//                                 //PassFail,
//                                 //IsFailInExternalTheory,
//                                 //IsFailInExternalPractical,
//                                 //IsFailInInternalTheory,
//                                 //IsFailInInternalPractical,
//                                 //IsFailInInternal,
//                                 //IsFailInExternal,

//                                 //null,
//                                 //BacklogCount,
//                                 //EarnCredit,

//                                 //ExamType,
//                                 //academicYear,
//                                 //courseName,
//                                 //semester,
//                                 //monthYear,
//                                 //ExamName
//                                 //);

//                             }

//                         }
//                         catch (Exception ex)
//                         {
//                             Console.WriteLine("Error Message: " + ex.Message);

//                             // Extract file name and line number
//                             var stackTrace = new StackTrace(ex, true);
//                             var frame = stackTrace.GetFrame(stackTrace.FrameCount - 1);

//                             var lineNumber = frame?.GetFileLineNumber();

//                             Console.WriteLine($"Error in file: {fileName}, Line: {lineNumber}");
//                         }
//                         //Console.WriteLine(BacklogByCount);
//                     }
//                 }

//                 // Save the processed data to Excel
//                 //SaveDataToExcel(NewExcelFileTable, outputFilePath);
//                 SaveDataToExcel(NewMarkHeadExcelFileTable, outputFilePath);

//             ATKTLabel:
//                 if (BacklogFlag == 1)
//                     Console.WriteLine("ExamType : ATKT");

//             }
//             catch (Exception ex)
//             {
//                 // Get the line number where the exception occurred
//                 var stackTrace = new StackTrace(ex, true); // Capture the stack trace
//                 var frame = stackTrace.GetFrame(stackTrace.FrameCount - 1); // Get the last frame
//                 int lineNumber = frame?.GetFileLineNumber() ?? -1;

//                 // Log the error message and line number
//                 Console.WriteLine($"Error in file: {inputPath}");
//                 Console.WriteLine($"Message: {ex.Message}");
//                 Console.WriteLine($"Line Number: {lineNumber}");

//                 // Add the file and error details to the list
//                 errorFiles.Add($"{inputPath}");
//                 errorMessages.Add($"{ex.Message} at line {lineNumber}");
//             }

//         }
//         #endregion



//         // Method to save DataTable to Excel
//         #region SaveDataToExcel
//         static void SaveDataToExcel(DataTable dataTable, string outputPath)
//         {
//             using (var workbook = new XLWorkbook())
//             {
//                 var worksheet = workbook.Worksheets.Add("ProcessedData");
//                 worksheet.Cell(1, 1).InsertTable(dataTable);
//                 workbook.SaveAs(outputPath);
//                 Console.WriteLine("Excel file saved at: " + outputPath);
//             }
//         }
//         #endregion

//         // Extract Details from filename
//         #region ExtractDetailsFromFileName
//         static FileNameDetails ExtractDetailsFromFilename(string FileName)
//         {
//             FileNameDetails details = new FileNameDetails();

//             //string pattern = @"(?<AcademicYear>B-\d{4}-\d{2})_(?<CourseName>[^_]+)_(?<Semester>Sem-\d+)_(?<MonthYear>\w+ \d{4})";
//             string pattern = @"(?<AcademicYear>B-\d{4}-\d{2})_(?<CourseName>[^_]+)_(?<Semester>Sem-\d+)_(?<MonthYear>\w+ \d{4})(?:\s+(?<ATKT>ATKT\d+))?";
//             var match = Regex.Match(FileName, pattern);

//             if (match.Success)
//             {
//                 details.AcademicYear = match.Groups["AcademicYear"].Value.Replace("B-", "");
//                 details.CourseName = match.Groups["CourseName"].Value;
//                 details.Semester = Convert.ToInt32(match.Groups["Semester"].Value.Replace("Sem-", ""));
//                 string monthYear = match.Groups["MonthYear"].Value;
//                 details.ExamType = "Supplymentary";

//                 string[] monthYearParts = monthYear.Split(' ');
//                 string Month = GetFullMonthName(monthYearParts[0]); // Convert to full month name
//                 string Year = monthYearParts[1];
//                 details.MonthYear = Month + "-" + Year;

//             }
//             else
//             {
//                 Console.WriteLine($"Filename: {FileName} - No match found!");
//             }

//             return details;
//         }
//         #endregion

//         // Method to get full name for the month
//         #region GetFullMonthName
//         static string GetFullMonthName(string abbreviatedMonth)
//         {
//             return abbreviatedMonth switch
//             {
//                 "Jan" => "January",
//                 "Feb" => "February",
//                 "Mar" => "March",
//                 "Apr" => "April",
//                 "May" => "May",
//                 "Jun" => "June",
//                 "Jul" => "July",
//                 "Aug" => "August",
//                 "Sep" => "September",
//                 "Oct" => "October",
//                 "Nov" => "November",
//                 "Dec" => "December",
//                 "January" => "January",
//                 "February" => "February",
//                 "March" => "March",
//                 "April" => "April",
//                 "June" => "June",
//                 "July" => "July",
//                 "August" => "August",
//                 "September" => "September",
//                 "October" => "October",
//                 "November" => "November",
//                 "December" => "December",
//                 _ => throw new ArgumentException("Invalid month abbreviation")
//             };
//         }
//         #endregion

//         // Method to extract subject code
//         #region ExtractSubjectCode
//         static string ExtractSubjectCode(string column)
//         {
//             // Extract the subject code before the colon
//             int cnt = CountColons(column);
//             if (cnt == 1)
//             {
//                 return null;
//             }
//             int colonIndex = column.IndexOf(':');
//             return colonIndex != -1 ? column.Substring(0, colonIndex).Trim() : null;
//         }
//         #endregion

//         // Helper Methods
//         #region Helper Methods
//         static int CountColons(string column)
//         {
//             int colonCount = 0;

//             // Loop through the string once, character by character
//             for (int i = 0; i < column.Length; i++)
//             {
//                 if (column[i] == ':') // Check if the character is a colon
//                 {
//                     colonCount++;
//                 }
//             }

//             return colonCount;
//         }
//         #endregion

//         // Metohd to Extract Subject Name
//         #region ExtractSubjectName
//         static string ExtractSubjectName(string column)
//         {
//             // Find the indices of ':' and '('
//             int colonIndex = column.IndexOf(':');
//             int creditIndex = column.IndexOf("(", StringComparison.OrdinalIgnoreCase);
//             if (creditIndex == -1)
//             {
//                 creditIndex = column.IndexOf("[", StringComparison.OrdinalIgnoreCase);
//             }
//             if (creditIndex == -1)
//             {
//                 creditIndex = column.IndexOf("\n", StringComparison.OrdinalIgnoreCase);
//             }
//             // Check if both indices are valid and in the correct order
//             if (colonIndex != -1 && creditIndex != -1 && creditIndex > colonIndex)
//             {
//                 // Extract and print the subject name
//                 string subjectName = column.Substring(colonIndex + 1, creditIndex - colonIndex - 1).Trim();
//                 return subjectName;
//             }
//             else
//             {
//                 return column.Substring(0, creditIndex).Trim();
//             }

//         }
//         #endregion

//         // Method to find Common Prefix
//         #region  Find Common Prefix
//         static string CommonPrefix(string str1, string str2)
//         {
//             int minLength = Math.Min(str1.Length, str2.Length);

//             for (int i = 0; i < minLength; i++)
//             {
//                 if (str1[i] != str2[i])
//                 {
//                     return str1.Substring(0, i);
//                 }
//             }

//             return str1.Substring(0, minLength);
//         }
//         #endregion
        
//     }
// }