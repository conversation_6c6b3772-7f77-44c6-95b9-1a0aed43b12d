using System;
using System.Collections.Generic;
using System.Data;
using DataTransformation.Common;

namespace DataTransformation.ITMClient
{
    
    // Handles the transformation of columns to rows for ITM data processing
    // Second step in the ITM transformation pipeline
    
    public class ColumnToRowTransformation : TransformationBase
    {
        #region 1.0 Legacy Implementation
        
        // Original transformation method (kept for reference)
        
        // public DataTable Transform(DataTable dataTable, Dictionary<string, List<string>> columnMapping)
        // {
        //     DataTable transformedTable = new DataTable();
        //     // Add transformation logic here
        //     return transformedTable;
        // }
        #endregion

        #region 2.0 Main Transformation
        
        // Primary transformation method that orchestrates the column-to-row conversion process
        
        public DataTable Transform(DataTable dataTable, Dictionary<string, List<string>> columnMapping)
        {
            DataTable dt_transformedData = TransformData(dataTable, columnMapping);
            
            List<string> columnsToCheck = new List<string> { "SubjectCode", "Sub_E", "Sub_I", "Sub_P", "Sub_E_OG", "Sub_E_CG", "Sub_E_M", "Sub_I_M", "Sub_P_M", "Sub_E_RM", "Sub_I_RM", "Sub_P_RM", "SubN", "Sub_E_G", "Sub_I_G", "Sub_P_G", "Sub_E_GP", "Sub_I_GP", "Sub_P_GP", "Sub_OGP", "Sub_OG", "Sub_GI", "Sub_E_G_P", "Sub_I_G_P", "Sub_P_G_P", "Sub_OG_P" };
            DataTable dt_filteredData = FilterDataTable(dt_transformedData, columnsToCheck);
            
            // string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            // FileHandler.WriteExcelFile(dt_filteredData, $@"..\DataStore\ITM\2_Transformation_ColumnToRow\Transformed_{timestamp}.xlsx");

            Console.WriteLine("Data transformation complete.");

            return dt_filteredData;
        }
        #endregion

        #region 3.0 Data Processing
        
        // Core transformation logic that converts column-based data to row-based format
        
        static DataTable TransformData(DataTable inputData, Dictionary<string, List<string>> columnMapping)
        {
            DataTable outputData = new DataTable();

            // Add all non-mapped columns to output
            foreach (DataColumn column in inputData.Columns)
            {
                if (!columnMapping.Values.Any(list => list.Contains(column.ColumnName)))
                {
                    outputData.Columns.Add(column.ColumnName);
                }
            }

            // Add mapped columns to output
            foreach (var key in columnMapping.Keys)
            {
                outputData.Columns.Add(key);
            }

            foreach (DataRow row in inputData.Rows)
            {
                // Create new rows for each group in the mapping
                int maxRows = columnMapping.Values.Max(list => list.Count);

                for (int i = 0; i < maxRows; i++)
                {
                    DataRow newRow = outputData.NewRow();

                    // Copy non-mapped column data
                    foreach (DataColumn column in inputData.Columns)
                    {
                        if (outputData.Columns.Contains(column.ColumnName) && !columnMapping.Values.Any(list => list.Contains(column.ColumnName)))
                        {
                            newRow[column.ColumnName] = row[column.ColumnName];
                        }
                    }

                    // Add mapped column data for each group
                    foreach (var map in columnMapping)
                    {
                        string outputColumn = map.Key;
                        List<string> inputColumns = map.Value;

                        if (i < inputColumns.Count)
                        {
                            string inputColumn = inputColumns[i];
                            newRow[outputColumn] = row[inputColumn];
                        }
                    }

                    outputData.Rows.Add(newRow);
                }
            }

            return outputData;
        }
        #endregion

        #region 4.0 Data Filtering
        
        // Filters the transformed data table by removing rows where specified columns are empty or null
        
        public static DataTable FilterDataTable(DataTable inputDataTable, List<string> columnNames)
        {
            DataTable resultTable = inputDataTable.Clone(); // Clone the schema of the original DataTable

            foreach (DataRow row in inputDataTable.Rows)
            {
                bool allColumnsEmptyOrNull = true;

                foreach (string columnName in columnNames)
                {
                    if (row[columnName] != DBNull.Value && !string.IsNullOrEmpty(row[columnName].ToString()))
                    {
                        allColumnsEmptyOrNull = false;
                        break;
                    }
                }

                // If not all columns are empty or null, add the row to the result table
                if (!allColumnsEmptyOrNull)
                {
                    resultTable.ImportRow(row);
                }
            }

            return resultTable;
        }
        #endregion
    }
}
