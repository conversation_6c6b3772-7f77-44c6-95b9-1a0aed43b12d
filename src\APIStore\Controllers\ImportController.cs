using System.IO.Compression;
using Microsoft.AspNetCore.Mvc;

namespace APIStore.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class ImportController : ControllerBase
    {
        private readonly IWebHostEnvironment _environment;

        public ImportController(IWebHostEnvironment environment)
        {
            _environment = environment;
        }

        [HttpPost("run")]
        [Consumes("multipart/form-data")]
        public async Task<IActionResult> RunImport([FromForm] ImportRequest request)
        {
            try
            {
                if (request.File == null || request.File.Length == 0)
                {
                    return BadRequest(new { error = "No file was uploaded" });
                }

                // Save the uploaded file
                var fileName = Path.GetFileName(request.File.FileName);
                var filePath = Path.Combine(_environment.ContentRootPath, "DataStore", "DataToImport", fileName);

                Directory.CreateDirectory(Path.GetDirectoryName(filePath));

                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await request.File.CopyToAsync(stream);
                }

                // Build connection string if not provided directly
                var connectionString = request.ConnectionString;
                if (string.IsNullOrEmpty(connectionString))
                {
                    connectionString = $"Data Source={request.Host};Initial Catalog={request.Database};User Id={request.Username};Password={request.Password};";
                }

                // Get input folder path
                var inputFolderPath = Path.Combine(_environment.ContentRootPath, "DataStore", "DataToImport");

                switch (request.College.ToUpper())
                {
                    case "ITM":
                        //var itmPipeline = new DataImport.ITMClient.ITMImportPipeline();
                        System.Console.WriteLine("______________________________________________");
                        System.Console.WriteLine(connectionString);
                        System.Console.WriteLine(request.IsLegacy.ToString());
                        System.Console.WriteLine("______________________________________________");
                        //itmPipeline.Run(inputFolderPath, connectionString, request.IsLegacy);
                        break;

                    case "DSU":
                        //var dsuPipeline = new DSUImportPipeline();
                        //dsuPipeline.Run(inputFolderPath, connectionString, request.IsLegacy);
                        break;

                    case "GU":
                        //var guPipeline = new GUImportPipeline();
                        //guPipeline.Run(inputFolderPath, connectionString);
                        break;

                    case "PPSU":
                        //var ppsuPipeline = new PPSUImportPipeline();
                        //ppsuPipeline.Run(inputFolderPath, connectionString);
                        break;

                    default:
                        return BadRequest(new { error = "Unsupported college format" });
                }

                return Ok(new { message = "Import completed successfully" });
            }
            catch (Exception ex)
            {
                // Log the full exception details
                Console.WriteLine($"Error: {ex.Message}");
                Console.WriteLine($"Stack Trace: {ex.StackTrace}");
                return StatusCode(500, new { error = ex.Message });
            }
        }

        [HttpGet("duplicatereportdownload/{college}")]
        public IActionResult DownloadDuplicateReport(string college)
        {
            try
            {
                // Define the directory where duplicate reports are stored
                string duplicateDataPath = Path.Combine(_environment.ContentRootPath, "result", "DublicateByCell");

                // Create a temporary zip file
                string tempZipPath = Path.Combine(Path.GetTempPath(), $"DuplicateReport_{college}_{DateTime.Now:yyyyMMddHHmmss}.zip");

                // Create the zip file
                using (var zipArchive = ZipFile.Open(tempZipPath, ZipArchiveMode.Create))
                {
                    // Get all Excel files in the directory
                    var files = Directory.GetFiles(duplicateDataPath, "*.xlsx");

                    foreach (var file in files)
                    {
                        // Add file to zip
                        zipArchive.CreateEntryFromFile(file, Path.GetFileName(file));
                    }
                }

                // Read the zip file
                var zipBytes = System.IO.File.ReadAllBytes(tempZipPath);

                // Delete the temporary zip file
                System.IO.File.Delete(tempZipPath);

                // Return the zip file
                return File(zipBytes, "application/zip", $"DuplicateReport_{college}.zip");
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = $"Failed to generate duplicate report: {ex.Message}" });
            }
        }

        [HttpGet("logs/{logType}")]
        public IActionResult GetLogs(string logType)
        {
            try
            {
                string logPath = logType switch
                {
                    "legacy" => Path.Combine(Directory.GetParent(_environment.ContentRootPath).FullName, "Logs", "legacy_import_logs.txt"),
                    "main" => Path.Combine(Directory.GetParent(_environment.ContentRootPath).FullName, "Logs", "main_import_logs.txt"),
                    _ => throw new ArgumentException("Invalid log type")
                };

                if (!System.IO.File.Exists(logPath))
                {
                    return NotFound(new { error = $"No logs found for {logType}" });
                }

                string logs = System.IO.File.ReadAllText(logPath);
                return Ok(new { logs });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = $"Failed to fetch logs: {ex.Message}" });
            }
        }
    }
}

public class ImportRequest
{
    public string College { get; set; }
    public string? ConnectionString { get; set; }
    public string? Host { get; set; }
    public string? Database { get; set; }
    public string? Username { get; set; }
    public string? Password { get; set; }
    public bool IsLegacy { get; set; }
    public IFormFile File { get; set; }
}











