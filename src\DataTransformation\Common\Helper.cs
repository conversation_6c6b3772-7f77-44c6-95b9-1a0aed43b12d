using System;
using System.Collections.Generic;
using System.IO;
using System.Data;
using ClosedXML.Excel;
using DataTransformation.Common;

namespace DataTransformation.Common
{
    public class HelperFunctions
    {

        public static int FormatSemesterValue(string semester)
        {
            semester = semester.Trim();
            Dictionary<string, int> semesterMap = new()
            {
                { "I", 1 },{ "II", 2 },{ "III", 3 },{ "IV", 4 },{ "V", 5 },
                { "VI", 6 },{ "VII", 7 },{ "VIII", 8 },{ "IX", 9 },{ "X", 10 },
                { "1", 1 },{ "2", 2 },{ "3", 3 },{ "4", 4 },{ "5", 5 },
                { "6", 6 },{ "7", 7 },{ "8", 8 },{ "9", 9 },{ "10", 10 }
            };

            if (!semesterMap.ContainsKey(semester.ToUpper()))
            {
                throw new ArgumentException($"Invalid semester numeral: {semester}");
            }

            return semesterMap[semester.ToUpper()];
        }

        public static int ConvertRomanToInteger(string? roman)
        {
            if (string.IsNullOrWhiteSpace(roman)) return 0;

            Dictionary<char, int> romanMap = new()
            {
                { 'I', 1 },
                { 'V', 5 },
                { 'X', 10 },
                { 'L', 50 },
                { 'C', 100 },
                { 'D', 500 },
                { 'M', 1000 }
            };

            int total = 0;
            int prevValue = 0;

            foreach (char c in roman.ToUpper())
            {
                if (!romanMap.ContainsKey(c))
                {
                    throw new ArgumentException($"Invalid Roman numeral: {roman}");
                }

                int currentValue = romanMap[c];
                total += currentValue > prevValue ? currentValue - 2 * prevValue : currentValue;
                prevValue = currentValue;
            }

            return total;   
        }


        public static void SaveToExcel(DataTable table, string filePath)
        {
            using (var workbook = new XLWorkbook())
            {
                var worksheet = workbook.Worksheets.Add(table, "Data");
                workbook.SaveAs(filePath);
            }
        }

        public static void SaveDataTableToExcelFileAsRelative(DataTable dataTable, string inputFilePath, string inputFolderPath, string outputFolderPath)
        {
            // Extract the relative path from the input folder path
            string relativePath = inputFilePath.Substring(inputFolderPath.Length).TrimStart(Path.DirectorySeparatorChar);
            string relativeDirectory = Path.GetDirectoryName(relativePath);
            string fileName = "processed_" + Path.GetFileName(inputFilePath);

            // Create output path that maintains the same hierarchy
            string outputDirectory = Path.Combine(outputFolderPath, relativeDirectory);
            string outputFilePath = Path.Combine(outputDirectory, fileName);

            // Ensure the directory exists
            if (!Directory.Exists(outputDirectory))
            {
                Directory.CreateDirectory(outputDirectory);
            }

            // Save the processed DataTable to an Excel file
            FileHandler.WriteExcelFile(dataTable, outputFilePath);
        }


        public static void LogMessage(string message, string filePath)
        {
            File.AppendAllText(filePath, message + "\n");
        }

        public static void ClearLogFile(string filePath)
        {
            File.WriteAllText(filePath, string.Empty);
        }

    }
}