using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using DataImport.Config;

namespace DataImport.Utility
{
    // This class contains all the utility functions that are used in the import process
    public static class ImportUtility
    {
        public static List<string> GetListOfExcelFiles(
            string folderPath
        )
        {
            List<string> files = new List<string>();
            foreach (string file in Directory.GetFiles(folderPath, "*.xlsx", SearchOption.AllDirectories))
            {
                files.Add(file);
            }
            return files;
        }

        public static DataTable FilterDataTableByColumns(
            DataTable masterDataTable, HashSet<string> selectedColumns
        )
        {
            // Create a new DataTable with the required columns
            DataTable filteredDataTable = new DataTable();

            // Add columns to the new DataTable based on the HashSet
            foreach (DataColumn column in masterDataTable.Columns)
            {
                if (selectedColumns.Contains(column.ColumnName))
                {
                    filteredDataTable.Columns.Add(column.ColumnName, column.DataType);
                }
            }

            // Copy rows from masterDataTable to filteredDataTable, ensuring only selected columns are copied
            foreach (DataRow row in masterDataTable.Rows)
            {
                DataRow newRow = filteredDataTable.NewRow();
                foreach (DataColumn column in filteredDataTable.Columns)
                {
                    newRow[column.ColumnName] = row[column.ColumnName];
                }
                filteredDataTable.Rows.Add(newRow);
            }

            return filteredDataTable;
        }

        public static DataTable GetUniqueRows(
            DataTable originalDataTable
        )
        {
            HashSet<string> uniqueValues = new HashSet<string>();
            DataTable uniqueDataTable = originalDataTable.Clone();

            foreach (DataRow row in originalDataTable.Rows)
            {
                string uniqueKey = "";
                foreach (DataColumn column in originalDataTable.Columns)
                {
                    uniqueKey += row[column].ToString().Trim() + "_";
                }

                if (!uniqueValues.Contains(uniqueKey))
                {
                    uniqueValues.Add(uniqueKey);
                    uniqueDataTable.ImportRow(row);
                }
            }

            return uniqueDataTable;
        }

        public static DataTable ValidateDataTableByCheckingNullablility(
            DataTable dataTable, List<string> notNullableColumns, string tableName, DataTable masterDataTable
        )
        {
            string invalidDataFolder = ImportConfig.invalidDataFolderPath;

            // Create DataTables for valid and related invalid data
            DataTable validDataTable = dataTable.Clone();  // Clone the structure of the original DataTable
            DataTable invalidDataTable = dataTable.Clone(); // Clone the structure of the master DataTable
            DataTable relatedMasterInvalidDataTable = masterDataTable.Clone(); // Clone the structure of the master DataTable

            // Iterate through the rows and check for nulls in not nullable columns
            foreach (DataRow row in dataTable.Rows)
            {
                bool isValid = true;

                // Check each non-nullable column
                foreach (var columnName in notNullableColumns)
                {
                    // Check if the value is null or DBNull
                    if (row[columnName] == DBNull.Value || row[columnName] == null || string.IsNullOrWhiteSpace(row[columnName].ToString()))
                    {
                        isValid = false;
                        break;
                    }
                }

                if (isValid)
                {
                    validDataTable.ImportRow(row);  // Add valid row to valid DataTable
                }
                else
                {
                    invalidDataTable.ImportRow(row);  // Add invalid row to invalid DataTable

                    // Find related rows in the masterDataTable based on matching columns
                    var relatedRows = masterDataTable.AsEnumerable()
                        .Where(masterRow =>
                            dataTable.Columns.Cast<DataColumn>().All(col => masterRow[col.ColumnName].Equals(row[col.ColumnName]))
                        );

                    foreach (var relatedRow in relatedRows)
                    {
                        relatedMasterInvalidDataTable.ImportRow(relatedRow);  // Add each related row to relatedMasterInvalidDataTable
                    }
                }
            }

            // Save related invalid rows to Excel if any invalid rows exist
            System.Console.WriteLine("No of Invalid row in " + tableName + " : " + Convert.ToString(relatedMasterInvalidDataTable.Rows.Count));

            if (ImportConfig.isExtractInvalidDataByCheckingNullability && relatedMasterInvalidDataTable.Rows.Count > 0)
            {
                // Check if the folder for invalid data exists, if not, create it
                if (!Directory.Exists(invalidDataFolder))
                {
                    Directory.CreateDirectory(invalidDataFolder);
                }
                string invalidDataFilePath = Path.Combine(invalidDataFolder, $"{tableName}_InvalidData.xlsx");
                string relatedMasterInvalidDataFilePath = Path.Combine(invalidDataFolder, $"{tableName}_relatedInvalidData.xlsx");
                Console.WriteLine("Related invalid data detected. Storing in Excel file : " + invalidDataFilePath);
                FileHandler.SaveDataTableToExcel(relatedMasterInvalidDataTable, relatedMasterInvalidDataFilePath);
                FileHandler.SaveDataTableToExcel(invalidDataTable, invalidDataFilePath);
            }

            return validDataTable;
        }

        public static void TrimStringValueOfDataTable(ref DataTable dataTable)
        {
            foreach (DataRow row in dataTable.Rows)
            {
                foreach (DataColumn column in dataTable.Columns)
                {
                    if (row[column] != DBNull.Value && row[column].GetType() == typeof(string))
                    {
                        row[column] = row[column].ToString().Trim();
                    }
                }
            }
        }

        public static DataTable FilterDataTableByRemovingAlreadyInsertedData(
            string tableName, DataTable createdTable, string connectionString, List<string> uniqueColumns
        )
        {
            string query = "SELECT * FROM " + tableName; // Query to fetch all data from the table

            DataTable dbTable = DatabaseHandler.FetchDataTable(connectionString, query);

            // Clone the structure of createdTable to store filtered rows
            DataTable filteredTable = createdTable.Clone();
            DataTable alreadyExistTable = createdTable.Clone();

            // Convert dbTable rows (considering only uniqueColumns) to HashSet of string for fast lookup
            var dbRowsSet = new HashSet<string>(
                dbTable.AsEnumerable().Select(row =>
                    string.Join(",", uniqueColumns.Select(column => row[column].ToString())))
            );

            int removedRow = 0;

            foreach (DataRow createdRow in createdTable.Rows)
            {
                // Create a string representation of the createdRow considering only uniqueColumns
                string createdRowKey = string.Join(",",
                    uniqueColumns.Select(column => createdRow[column].ToString()));

                // Add the row to the filtered table if it doesn't exist in the dbRowsSet
                if (!dbRowsSet.Contains(createdRowKey))
                {
                    filteredTable.ImportRow(createdRow);
                }
                else
                {
                    alreadyExistTable.ImportRow(createdRow);
                    removedRow++;
                }
            }

            if (ImportConfig.isExtractAlreadyInsertedData && alreadyExistTable.Rows.Count > 0)
            {
                // Check if the folder for invalid data exists, if not, create it
                string alreadyExistDataFolderPath = ImportConfig.alreadyInsertedDataFolderPath;
                if (!Directory.Exists(alreadyExistDataFolderPath))
                {
                    Directory.CreateDirectory(alreadyExistDataFolderPath);
                }
                string alreadyExistDataFilePath = Path.Combine(alreadyExistDataFolderPath, $"{tableName}_AlreadyExistData.xlsx");
                Console.WriteLine("Already inserted data detected. Storing in Excel file : " + alreadyExistDataFilePath);
                FileHandler.SaveDataTableToExcel(alreadyExistTable, alreadyExistDataFilePath);
            }


            // List<string> exceptUpdate = ["Created"];

            // FilterDataTable_UpdateExistingData(tableName, createdTable, connectionString, uniqueColumns, exceptUpdate);

            System.Console.WriteLine("\nRemoved " + removedRow + " rows from the createdTable as they already exist in the database.");

            return filteredTable;
        }

        public static void UpdateOrAddColumnIntoDataTable(
            ref DataTable dataTable, string columnName, object defaultValue
        )
        {
            try
            {

                if (dataTable == null)
                {
                    throw new ArgumentNullException(nameof(dataTable), "DataTable cannot be null.");
                }

                if (string.IsNullOrWhiteSpace(columnName))
                {
                    throw new ArgumentException("Column name cannot be null or empty.", nameof(columnName));
                }

                // Check if the column exists in the DataTable
                if (dataTable.Columns.Contains(columnName))
                {
                    // If the column exists, replace null values with the default value
                    foreach (DataRow row in dataTable.Rows)
                    {
                        if (row[columnName] == DBNull.Value || string.IsNullOrWhiteSpace(row[columnName]?.ToString()))
                        {
                            row[columnName] = defaultValue;
                        }
                    }
                }
                else
                {
                    // If the column does not exist, add it and set the default value for all rows
                    DataColumn newColumn = new DataColumn(columnName, defaultValue.GetType())
                    {
                        DefaultValue = defaultValue
                    };
                    dataTable.Columns.Add(newColumn);

                    // Ensure all existing rows in the table get the default value
                    foreach (DataRow row in dataTable.Rows)
                    {
                        row[columnName] = defaultValue;
                    }
                }
                System.Console.WriteLine("Column updated or added successfully : " + columnName + " with default value : " + defaultValue);
            }
            catch (Exception ex)
            {
                Console.WriteLine("Error updating or adding column: " + ex.Message);
            }
        }

        public static DataTable ConvertDataTableTypes(DataTable dataTable, Dictionary<string, Type> columnSchema)
        {
            DataTable convertedTable = dataTable.Clone();

            // Adjust column data types to match the database schema
            foreach (DataColumn column in convertedTable.Columns)
            {
                if (columnSchema.TryGetValue(column.ColumnName, out Type targetType))
                {
                    column.DataType = targetType;
                }
            }

            // Process each row and apply type conversion
            foreach (DataRow row in dataTable.Rows)
            {
                DataRow newRow = convertedTable.NewRow();
                foreach (DataColumn column in convertedTable.Columns)
                {
                    object cellValue = row[column.ColumnName];
                    if (cellValue == DBNull.Value)
                    {
                        newRow[column.ColumnName] = DBNull.Value;
                    }
                    else
                    {
                        // Convert the value to match the database column type
                        newRow[column.ColumnName] = ConvertToDatabaseType(cellValue, column.DataType, column.ColumnName, dataTable);
                    }
                }
                convertedTable.Rows.Add(newRow);
            }

            return convertedTable;
        }

        public static (DataTable RelatedDuplicateData, DataTable AbsoluteDuplicateData, DataTable ValidData) SeparateDuplicates(
            DataTable inputTable, List<string> checkUniquenessOn, DataTable masterDataTable
        )
        {
            //DataTableViewer.ShowDataTableInExcel(inputTable, "Input Data");
            if (inputTable == null)
                throw new ArgumentNullException(nameof(inputTable), "Input DataTable cannot be null.");

            if (checkUniquenessOn == null || checkUniquenessOn.Count == 0)
                throw new ArgumentException("Column names must be provided.", nameof(checkUniquenessOn));

            // Validate column names
            foreach (var column in checkUniquenessOn)
            {
                if (!inputTable.Columns.Contains(column))
                {
                    throw new ArgumentException($"Column '{column}' does not exist in the DataTable.");
                }
            }

            // Group rows based on the given columns
            var groupedData = inputTable.AsEnumerable()
                .GroupBy(row => string.Join("|", checkUniquenessOn.Select(col => row[col]?.ToString() ?? string.Empty)));

            // Separate duplicate and valid data
            var duplicateGroups = groupedData.Where(g => g.Count() > 1);
            var validRows = groupedData.Where(g => g.Count() == 1).SelectMany(g => g);

            // Create result DataTables
            DataTable relatedDuplicateData = masterDataTable.Clone();
            DataTable absoluteDuplicateData = inputTable.Clone();
            DataTable validData = inputTable.Clone();

            // Collect duplicate rows from masterDataTable
            foreach (var group in duplicateGroups)
            {
                foreach (var row in group)
                {
                    // Build a filter expression to match rows in the masterDataTable
                    string filterExpression = string.Join(" AND ", checkUniquenessOn.Select(col => $"{col} = '{row[col].ToString()}'"));
                    DataRow[] relatedRows = masterDataTable.Select(filterExpression);

                    foreach (var relatedRow in relatedRows)
                    {
                        relatedDuplicateData.ImportRow(relatedRow);  // Import related rows into duplicateData
                        absoluteDuplicateData.ImportRow(row);  // Import absolute rows into duplicateData
                    }
                }
            }

            foreach (var row in validRows)
            {
                validData.ImportRow(row);  // Import valid rows into validData
            }

            // get unique rows of related and absolute dublicate data
            relatedDuplicateData = GetUniqueRows(relatedDuplicateData);
            absoluteDuplicateData = GetUniqueRows(absoluteDuplicateData);

            System.Console.WriteLine("No of Absolute Duplicate row in " + inputTable.TableName + " : " + Convert.ToString(absoluteDuplicateData.Rows.Count));

            return (relatedDuplicateData, absoluteDuplicateData, validData);
        }

        public static void FillMappedIDInMasterTable(
            ref DataTable masterDataTable, Dictionary<string, int> mapping, string targetColumnName, List<string> sourceColumnNames
        )
        {
            // Iterate over each row in the masterDataTable
            foreach (DataRow row in masterDataTable.Rows)
            {
                try
                {
                    // Create the composite key from source columns
                    string compositeKey = string.Join("_",
                        sourceColumnNames.Select(col => row[col]?.ToString() ?? string.Empty));

                    // Check if the composite key exists in the dictionary
                    if (mapping.ContainsKey(compositeKey))
                    {
                        // If the key exists in the dictionary, set the target column value
                        row[targetColumnName] = mapping[compositeKey];
                    }
                    else
                    {
                        // If key does not exist in the dictionary, set it as DBNull
                        row[targetColumnName] = DBNull.Value;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error filling {targetColumnName}: {ex.Message}");
                    // Continue processing other rows
                }
            }
        }

        public static void FillExamLIDInMasterTable(
            ref DataTable masterDataTable, Dictionary<string, int> examMapping
        )
        {
            FillMappedIDInMasterTable(
                ref masterDataTable,
                examMapping,
                "ExamLID",
                new List<string> { "ExamName" });
        }

        public static void FillExamStudentLIDInMasterTable(
            ref DataTable masterDataTable, Dictionary<string, int> enrollmentNoAndExamLIdToExamStudentLIdMapping
        )
        {
            FillMappedIDInMasterTable(
                ref masterDataTable,
                enrollmentNoAndExamLIdToExamStudentLIdMapping,
                "ExamStudentLID",
                new List<string> { "EnrollmentNo", "ExamLID" });
        }

        public static void FillExamStudentSubjectLIDInMasterTable(
            ref DataTable masterDataTable, Dictionary<string, int> ExamStudentSubjectLID_Mapping
        )
        {
            FillMappedIDInMasterTable(
                ref masterDataTable,
                ExamStudentSubjectLID_Mapping,
                "ExamStudentSubjectLID",
                new List<string> { "ExamStudentLID", "ExamLID", "SubjectCode" });
        }

        static object ConvertToDatabaseType(object value, Type targetType, string ColumnName, DataTable dataTable)
        {
            try
            {
                if (value == null || value == DBNull.Value)
                {
                    return DBNull.Value;
                }

                // Handle string type specially
                if (targetType == typeof(string))
                {
                    string strValue = value.ToString().Trim();
                    return string.IsNullOrWhiteSpace(strValue) ? DBNull.Value : strValue;
                }
                
                // Handle numeric types
                if (targetType == typeof(int) || targetType == typeof(Int32))
                    return Convert.ToInt32(value);

                if (targetType == typeof(long) || targetType == typeof(Int64))
                    return Convert.ToInt64(value);

                if (targetType == typeof(double))
                    return Convert.ToDouble(value);
                    
                if (targetType == typeof(decimal))
                    return Convert.ToDecimal(value);

                if (targetType == typeof(float) || targetType == typeof(Single))
                    return Convert.ToSingle(value);
                
                // Handle date/time
                if (targetType == typeof(DateTime))
                    return Convert.ToDateTime(value);
                
                // Handle boolean
                if (targetType == typeof(bool) || targetType == typeof(Boolean))
                    return Convert.ToBoolean(value);
                
                // Handle GUID
                if (targetType == typeof(Guid))
                    return Guid.Parse(value.ToString());
                
                // Handle byte
                if (targetType == typeof(byte) || targetType == typeof(Byte))
                    return Convert.ToByte(value);
                
                // Handle char
                if (targetType == typeof(char) || targetType == typeof(Char))
                    return Convert.ToChar(value);

                // Default fallback for unsupported types
                return Convert.ChangeType(value, targetType);
            }
            catch (Exception ex)
            {
                // DataTableViewer.ShowDataTableInExcel(dataTable, "Data");
                // Log the error with more details
                Console.WriteLine($"Error converting '{value}' to {targetType.Name} in {ColumnName}: {ex.Message}");
                return DBNull.Value;
            }
        }

    }
}
