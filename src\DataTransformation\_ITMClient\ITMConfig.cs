// using System;
// using System.Collections.Generic;
// using System.Data;
// using System.IO;

// namespace DataTransformation.ITMClient
// {
//     public static class ITMConfig
//     {
//         #region Configuration Variables
//         public static readonly string logFilePath = "../Logs/nad_transformation_logs.txt";

//         public static string inputFolderPath = @"..\DataStore\ITM\InputData";
//         public static string outputFolderPath = @"..\DataStore\ITM\TransformedOutput";

//         #endregion Configuration Variables

//     }
// }