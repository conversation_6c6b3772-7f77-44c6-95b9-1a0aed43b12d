using System;
using System.Collections.Generic;
using System.Data;
using DataTransformation.Common;


namespace DataTransformation.ITMClient
{
    public class FilterColumns : TransformationBase
    {
        #region 1.0 Legacy Implementation
        // Original filter method (kept for reference)
        // public DataTable Filter(DataTable dataTable, List<string> columnsToKeep)
        // {
        //     DataTable filteredTable = new DataTable();
        //     // Add filtering logic here
        //     return filteredTable;
        // }
        #endregion

        #region 2.0 Main Filter Processing
        // Main entry point for filtering data
        // Handles mark modifications, backlog filtering, and column renaming
        public DataTable Filter(DataTable dataTable, List<string> columnsToKeep, Dictionary<string, string> columnRenameMapping)
        {
            // ModifyExternalMarks(dataTable, "Sub_E_RM", "Sub_E_CG");

            System.Console.WriteLine("Want to Modify Marks Compared to RM? (Y/N) : ");
            System.Console.WriteLine("for testing purpose, assusmed yes!");

            // string modifyMarks = Console.ReadLine();

            // if(modifyMarks == "Y" || modifyMarks == "y") {
            ModifyMarksComparedTo_RM(dataTable, "Sub_E_RM", "Sub_E_CG");
                ModifyMarksComparedTo_RM(dataTable, "Sub_I_RM", "Sub_I");
                ModifyMarksComparedTo_RM(dataTable, "Sub_P_RM", "Sub_P");
            // }


            // Apply the semester-based filtering on the Backlog Sem_* columns
            DataTable finalFilteredDataTable = FilterBacklogColumnsBySemester(dataTable);
            System.Console.WriteLine("Filtered Backlog Columns and Created CurrentBacklog Successfully!");

            // Filter the data based on the specified columns
            DataTable filteredDataTable = FilterColumnsFromDataTable(finalFilteredDataTable, columnsToKeep);

            List<string> notNullColumns_reducingSubjects = new List<string> { "SubjectCode" };

            filteredDataTable = RemoveRowsWithAllNullColumns(filteredDataTable, notNullColumns_reducingSubjects);


            // Dictionary with column name mapping (old name -> new name)
            DataTable renamedDataTable = RenameColumnsInDataTable(filteredDataTable, columnRenameMapping);


            // string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            // FileHandler.WriteExcelFile(renamedDataTable, $@"..\DataStore\ITM\3_FilterAndRenamed\FilteredAndRenamed_{timestamp}.xlsx");

            Console.WriteLine("Excel file filtered successfully.");

            return renamedDataTable;
        }
        #endregion

        #region 3.0 Mark Modification
        // Modifies marks in the reference column based on the check column values
        public static void ModifyMarksComparedTo_RM(DataTable dt, string needsToCheckColumn, string referColumn)
        {
            int count = 0;

            foreach (DataRow row in dt.Rows)
            {
                if (row[needsToCheckColumn] != DBNull.Value && !string.IsNullOrWhiteSpace(row[needsToCheckColumn].ToString()))
                {
                    row[referColumn] = row[needsToCheckColumn]; // Replace referColumn value
                    count++;
                }
            }

            System.Console.WriteLine("Modiefied " + referColumn + " Marks based on " + needsToCheckColumn + " : " + Convert.ToString(count));
        }
        #endregion

        #region 4.0 Column Filtering
        // Filters DataTable to keep only specified columns
        public static DataTable FilterColumnsFromDataTable(DataTable dataTable, List<string> columnsToKeep)
        {
            DataTable filteredDataTable = new DataTable();

            // Adding the specified columns to the new DataTable
            foreach (string columnName in columnsToKeep)
            {
                if (dataTable.Columns.Contains(columnName))
                {
                    filteredDataTable.Columns.Add(columnName);
                }
            }

            // Adding the rows to the new DataTable based on the selected columns
            foreach (DataRow row in dataTable.Rows)
            {
                DataRow newRow = filteredDataTable.NewRow();
                foreach (string columnName in columnsToKeep)
                {
                    if (dataTable.Columns.Contains(columnName))
                    {
                        newRow[columnName] = row[columnName];
                    }
                }
                filteredDataTable.Rows.Add(newRow);
            }

            return filteredDataTable;
        }
        #endregion

        #region 5.0 Backlog Processing
        // Filters backlog columns based on semester and creates CurrentBacklog column
        public static DataTable FilterBacklogColumnsBySemester(DataTable dataTable)
        {
            List<string> backlogColumns = new List<string>
            {
                "Backlog Sem_1", "Backlog Sem_2", "Backlog Sem_3", "Backlog Sem_4", "Backlog Sem_5", "Backlog Sem_6", "Backlog Sem_7", "Backlog Sem_8", "Backlog Sem_9", "Backlog Sem_10"
            };

            // Adding a new column named "CurrentBacklog"
            dataTable.Columns.Add("CurrentBacklog");

            // Loop through each row and filter the backlog column based on the Semester value
            foreach (DataRow row in dataTable.Rows)
            {
                string semester = row["Semester"].ToString(); // Get the semester value from the row

                // Iterate through all the Backlog Sem_* columns
                foreach (string backlogColumn in backlogColumns)
                {
                    if (backlogColumn.Contains(semester)) // Check if the column matches the semester
                    {
                        row["CurrentBacklog"] = row[backlogColumn]; // Assign the value of the matched column to "CurrentBacklog"
                        break; // No need to check further columns once a match is found
                    }
                    else
                    {
                        row["CurrentBacklog"] = DBNull.Value; // Set the value as null for non-matching semesters
                    }
                }
            }

            return dataTable;
        }
        #endregion

        #region 6.0 Column Renaming
        // Renames columns in DataTable based on provided mapping
        public static DataTable RenameColumnsInDataTable(DataTable dataTable, Dictionary<string, string> columnMapping)
        {
            // Create a copy of the DataTable to avoid modifying the original reference
            DataTable renamedTable = dataTable.Copy();

            // Loop through each column and rename if it exists in the mapping dictionary
            foreach (DataColumn column in renamedTable.Columns)
            {
                if (columnMapping.ContainsKey(column.ColumnName))
                {
                    column.ColumnName = columnMapping[column.ColumnName];
                }
            }

            return renamedTable;
        }
        #endregion

        #region 7.0 Row Filtering
        // Removes rows where specified columns are all null or empty
        public static DataTable RemoveRowsWithAllNullColumns(DataTable dataTable, List<string> columnNames)
        {
            // Create a list to store rows that need to be removed
            List<DataRow> rowsToRemove = new List<DataRow>();

            foreach (DataRow row in dataTable.Rows)
            {
                bool allNullOrEmpty = true;

                foreach (var columnName in columnNames)
                {
                    if (row.Table.Columns.Contains(columnName))
                    {
                        var value = row[columnName];
                        // System.Console.WriteLine(value.ToString().Length);
                        // System.Console.WriteLine("columnName : " + columnName + " value : " + value);
                        if (value.ToString() != "")
                        {
                            allNullOrEmpty = false;
                            break;
                        }
                    }
                }

                if (allNullOrEmpty)
                {
                    rowsToRemove.Add(row);
                }
            }

            // Remove the rows from the DataTable
            foreach (var row in rowsToRemove)
            {
                dataTable.Rows.Remove(row);
            }
    
            System.Console.WriteLine("Removed Rows with NULL Columns : " + Convert.ToString(rowsToRemove.Count));


            // Accept changes to finalize the removal
            return dataTable;
        }
        #endregion
    }
}
