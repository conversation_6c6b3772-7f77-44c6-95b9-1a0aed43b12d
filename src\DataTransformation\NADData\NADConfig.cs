using System;
using System.Collections.Generic;
using System.Data;
using System.IO;

namespace DataTransformation.NADData
{
    public static class NADConfig
    {
        #region Configuration Variables

        // public static readonly string LogFilePath = "../../../../../data/Logs/nad_transformation_logs.txt";

        // public static string inputFolderPath = @"../../../../../data/ETLDataStore/NAD Data/InputData";
        // public static string outputFolderPath = @"../../../../../data/ETLDataStore/NAD Data/TransformedOutput";

        public static readonly string LogFilePath = "../../data/Logs/nad_transformation_logs.txt";
        public static string inputFolderPath = @"../../data/ETLDataStore/NAD Data/InputData";
        public static string outputFolderPath = @"../../data/ETLDataStore/NAD Data/TransformedOutput";

        public static bool IsColumnToRowTransformationRequired = true;

        public static bool IsExamTypeDefaultRegular = true;

        #endregion Configuration Variables

        #region Configure Exam Name 
        // append exam name with manual configuration
        public static void AppendExamNameWithManulaConfiguration(ref DataTable dataTable, string inputFilePath)
        {
            // Get the file name without extension
            string fileName = Path.GetFileNameWithoutExtension(inputFilePath);

            // Add a new column for ExamName
            dataTable.Columns.Add("ExamName", typeof(string));

            // Populate the ExamName column
            foreach (DataRow row in dataTable.Rows)
            {
                row["ExamName"] = fileName + " Semester - " + row["Semester"];
            }
        }
        #endregion Configure Exam Name 

        #region Data Store

        // renaming header name as per our requirement for our database
        public static Dictionary<string, string> GetHeaderMappings()
        {
            return new Dictionary<string, string>()
            {
                { "COURSE_NAME", "CourseName" },
                { "REGN_NO", "EnrollmentNo" },
                { "CNAME", "StudentName" },
                { "SEM", "Semester" },
                { "TERM_TYPE", "Term" },
                { "EXAM_TYPE", "ExamType" },
                { "CGPA", "CGPA" },
                { "SGPA", "SGPA" },
                { "MONTH", "Month" },
                { "YEAR", "Year" },


                {"SUBNM", "SubjectName"},
                {"SUB", "SubjectCode"},
                {"SUBMAX", "TotalMaxMark"},
                {"SUBMIN", "TotalPassingMark"},
                {"SUB_TH_MAX", "ExternalTheoryMaxMark"},
                {"SUB_TH_MIN", "ExternalTheoryPassingMark"},
                {"SUB_PR_MAX", "ExternalPracticalMaxMark"},
                {"SUB_PR_MIN", "ExternalPracticalPassingMark"},
                {"SUB_CE_MAX", "InternalMaxMark"},
                {"SUB_CE_MIN", "InternalPassingMark"},
                {"SUB_VV_MAX", "ExternalVivaVoceMaxMark"},
                {"SUB_VV_MIN", "ExternalVivaVocePassingMark"},
                {"SUB_TH_MRKS", "TheoryObtainedMark"},
                {"SUB_PR_MRKS", "PracticalObtainedMark"},
                {"SUB_CE_MRKS", "InternalObtainedMark"},
                {"SUB_VV_MRKS", "ExternalVivaVoceObtainedMark"},
                {"SUB_TOT", "TotalObtainedMark"},
                {"SUB_STATUS", "SubjectStatus"},
                {"SUB_GRADE", "GradeName"},
                {"SUB_GRADE_POINTS", "GradePoint"},
                {"SUB_CREDIT", "Credit"},
                {"SUB_CREDIT_POINTS", "CreditPoint"},
                {"SUB_GRACE", "Grace"},
                { "SUB_REMARKS", "ResultRemarks"},

                // { "SUBNM", "SubjectName" },
                // { "SUB", "SubjectCode" },

                // { "SUB_TH_MRKS", "TheoryObtainedMark" },
                // { "SUB_PR_MRKS", "PracticalObtainedMark" },
                // { "SUB_CE_MRKS", "InternalObtainedMark" },
                // { "SUB_VV_MRKS", "ExternalVivaVoceObtainedMark" },
                // { "SUB_TOT", "TotalObtainedMark" },
                
                // { "SUB_GRADE", "GradeName" },
                // { "SUB_GRADE_POINTS", "GradePoint" },
                // { "SUB_CREDIT", "Credit" },
                // { "SUB_CREDIT_POINTS", "CreditPoint" },
                // { "SUB_REMARKS", "ResultRemarks" },

                { "USN", "EnrollmentNo" },
                { "Name", "StudentName" },
                { "Semester", "Semester" },
                { "Course Code", "SubjectCode" },
                { "Course Title", "SubjectName" },
                { "CIA-SEE", "TotalObtainedMarks" },
                { "Credits", "Credit" },
                { "Grade", "GradeName" },
                { "Grade Point", "GradePoint" },
                { "credit_hours", "CreditHours" },
                // { "SGPA", "SGPA" },
                // { "CGPA", "CGPA" },
                { "Result Year", "ResultRemarks" },
                { "TOTAL CIA", "InternalObtainedMarks" },
                { "TOTAL SEE", "ExternalObtainedMarks" }
            };
        }

        public static List<string> GetColumnsToKeep()
        {
            return new List<string>()
            {
                // NAD Ideal Format 
                "COURSE_NAME","REGN_NO","CNAME","SEM", /* "TERM_TYPE",*/ "EXAM_TYPE","CGPA","SGPA", "MONTH", "YEAR", 

                "SUB1NM", "SUB1", "SUB1MAX", "SUB1MIN", "SUB1_TH_MAX", "SUB1_TH_MIN", "SUB1_PR_MAX", "SUB1_PR_MIN", "SUB1_CE_MAX", "SUB1_CE_MIN", "SUB1_VV_MAX", "SUB1_VV_MIN", "SUB1_TH_MRKS", "SUB1_PR_MRKS", "SUB1_CE_MRKS", "SUB1_VV_MRKS", "SUB1_TOT", "SUB1_STATUS", "SUB1_GRADE", "SUB1_GRADE_POINTS", "SUB1_CREDIT", "SUB1_CREDIT_POINTS", "SUB1_GRACE", "SUB1_REMARKS",
                "SUB2NM", "SUB2", "SUB2MAX", "SUB2MIN", "SUB2_TH_MAX", "SUB2_TH_MIN", "SUB2_PR_MAX", "SUB2_PR_MIN", "SUB2_CE_MAX", "SUB2_CE_MIN", "SUB2_VV_MAX", "SUB2_VV_MIN", "SUB2_TH_MRKS", "SUB2_PR_MRKS", "SUB2_CE_MRKS", "SUB2_VV_MRKS", "SUB2_TOT", "SUB2_STATUS", "SUB2_GRADE", "SUB2_GRADE_POINTS", "SUB2_CREDIT", "SUB2_CREDIT_POINTS", "SUB2_GRACE", "SUB2_REMARKS",
                "SUB3NM", "SUB3", "SUB3MAX", "SUB3MIN", "SUB3_TH_MAX", "SUB3_TH_MIN", "SUB3_PR_MAX", "SUB3_PR_MIN", "SUB3_CE_MAX", "SUB3_CE_MIN", "SUB3_VV_MAX", "SUB3_VV_MIN", "SUB3_TH_MRKS", "SUB3_PR_MRKS", "SUB3_CE_MRKS", "SUB3_VV_MRKS", "SUB3_TOT", "SUB3_STATUS", "SUB3_GRADE", "SUB3_GRADE_POINTS", "SUB3_CREDIT", "SUB3_CREDIT_POINTS", "SUB3_GRACE", "SUB3_REMARKS",
                "SUB4NM", "SUB4", "SUB4MAX", "SUB4MIN", "SUB4_TH_MAX", "SUB4_TH_MIN", "SUB4_PR_MAX", "SUB4_PR_MIN", "SUB4_CE_MAX", "SUB4_CE_MIN", "SUB4_VV_MAX", "SUB4_VV_MIN", "SUB4_TH_MRKS", "SUB4_PR_MRKS", "SUB4_CE_MRKS", "SUB4_VV_MRKS", "SUB4_TOT", "SUB4_STATUS", "SUB4_GRADE", "SUB4_GRADE_POINTS", "SUB4_CREDIT", "SUB4_CREDIT_POINTS", "SUB4_GRACE", "SUB4_REMARKS",
                "SUB5NM", "SUB5", "SUB5MAX", "SUB5MIN", "SUB5_TH_MAX", "SUB5_TH_MIN", "SUB5_PR_MAX", "SUB5_PR_MIN", "SUB5_CE_MAX", "SUB5_CE_MIN", "SUB5_VV_MAX", "SUB5_VV_MIN", "SUB5_TH_MRKS", "SUB5_PR_MRKS", "SUB5_CE_MRKS", "SUB5_VV_MRKS", "SUB5_TOT", "SUB5_STATUS", "SUB5_GRADE", "SUB5_GRADE_POINTS", "SUB5_CREDIT", "SUB5_CREDIT_POINTS", "SUB5_GRACE", "SUB5_REMARKS",
                "SUB6NM", "SUB6", "SUB6MAX", "SUB6MIN", "SUB6_TH_MAX", "SUB6_TH_MIN", "SUB6_PR_MAX", "SUB6_PR_MIN", "SUB6_CE_MAX", "SUB6_CE_MIN", "SUB6_VV_MAX", "SUB6_VV_MIN", "SUB6_TH_MRKS", "SUB6_PR_MRKS", "SUB6_CE_MRKS", "SUB6_VV_MRKS", "SUB6_TOT", "SUB6_STATUS", "SUB6_GRADE", "SUB6_GRADE_POINTS", "SUB6_CREDIT", "SUB6_CREDIT_POINTS", "SUB6_GRACE", "SUB6_REMARKS",
                "SUB7NM", "SUB7", "SUB7MAX", "SUB7MIN", "SUB7_TH_MAX", "SUB7_TH_MIN", "SUB7_PR_MAX", "SUB7_PR_MIN", "SUB7_CE_MAX", "SUB7_CE_MIN", "SUB7_VV_MAX", "SUB7_VV_MIN", "SUB7_TH_MRKS", "SUB7_PR_MRKS", "SUB7_CE_MRKS", "SUB7_VV_MRKS", "SUB7_TOT", "SUB7_STATUS", "SUB7_GRADE", "SUB7_GRADE_POINTS", "SUB7_CREDIT", "SUB7_CREDIT_POINTS", "SUB7_GRACE", "SUB7_REMARKS",
                "SUB8NM", "SUB8", "SUB8MAX", "SUB8MIN", "SUB8_TH_MAX", "SUB8_TH_MIN", "SUB8_PR_MAX", "SUB8_PR_MIN", "SUB8_CE_MAX", "SUB8_CE_MIN", "SUB8_VV_MAX", "SUB8_VV_MIN", "SUB8_TH_MRKS", "SUB8_PR_MRKS", "SUB8_CE_MRKS", "SUB8_VV_MRKS", "SUB8_TOT", "SUB8_STATUS", "SUB8_GRADE", "SUB8_GRADE_POINTS", "SUB8_CREDIT", "SUB8_CREDIT_POINTS", "SUB8_GRACE", "SUB8_REMARKS",
                "SUB9NM", "SUB9", "SUB9MAX", "SUB9MIN", "SUB9_TH_MAX", "SUB9_TH_MIN", "SUB9_PR_MAX", "SUB9_PR_MIN", "SUB9_CE_MAX", "SUB9_CE_MIN", "SUB9_VV_MAX", "SUB9_VV_MIN", "SUB9_TH_MRKS", "SUB9_PR_MRKS", "SUB9_CE_MRKS", "SUB9_VV_MRKS", "SUB9_TOT", "SUB9_STATUS", "SUB9_GRADE", "SUB9_GRADE_POINTS", "SUB9_CREDIT", "SUB9_CREDIT_POINTS", "SUB9_GRACE", "SUB9_REMARKS",
                "SUB10NM", "SUB10", "SUB10MAX", "SUB10MIN", "SUB10_TH_MAX", "SUB10_TH_MIN", "SUB10_PR_MAX", "SUB10_PR_MIN", "SUB10_CE_MAX", "SUB10_CE_MIN", "SUB10_VV_MAX", "SUB10_VV_MIN", "SUB10_TH_MRKS", "SUB10_PR_MRKS", "SUB10_CE_MRKS", "SUB10_VV_MRKS", "SUB10_TOT", "SUB10_STATUS", "SUB10_GRADE", "SUB10_GRADE_POINTS", "SUB10_CREDIT", "SUB10_CREDIT_POINTS", "SUB10_GRACE", "SUB10_REMARKS",

                // "SUB1NM", "SUB1", "SUB1_TH_MRKS","SUB1_PR_MRKS","SUB1_CE_MRKS","SUB1_VV_MRKS","SUB1_TOT", "SUB1_GRADE", "SUB1_GRADE_POINTS", "SUB1_CREDIT", "SUB1_CREDIT_POINTS", "SUB1_REMARKS",
                // "SUB2NM", "SUB2", "SUB2_TH_MRKS","SUB2_PR_MRKS","SUB2_CE_MRKS","SUB2_VV_MRKS","SUB2_TOT", "SUB2_GRADE", "SUB2_GRADE_POINTS", "SUB2_CREDIT", "SUB2_CREDIT_POINTS", "SUB2_REMARKS",
                // "SUB3NM", "SUB3", "SUB3_TH_MRKS","SUB3_PR_MRKS","SUB3_CE_MRKS","SUB3_VV_MRKS","SUB3_TOT", "SUB3_GRADE", "SUB3_GRADE_POINTS", "SUB3_CREDIT", "SUB3_CREDIT_POINTS", "SUB3_REMARKS",
                // "SUB4NM", "SUB4", "SUB4_TH_MRKS","SUB4_PR_MRKS","SUB4_CE_MRKS","SUB4_VV_MRKS","SUB4_TOT", "SUB4_GRADE", "SUB4_GRADE_POINTS", "SUB4_CREDIT", "SUB4_CREDIT_POINTS", "SUB4_REMARKS",
                // "SUB5NM", "SUB5", "SUB5_TH_MRKS","SUB5_PR_MRKS","SUB5_CE_MRKS","SUB5_VV_MRKS","SUB5_TOT", "SUB5_GRADE", "SUB5_GRADE_POINTS", "SUB5_CREDIT", "SUB5_CREDIT_POINTS", "SUB5_REMARKS",
                // "SUB6NM", "SUB6", "SUB6_TH_MRKS","SUB6_PR_MRKS","SUB6_CE_MRKS","SUB6_VV_MRKS","SUB6_TOT", "SUB6_GRADE", "SUB6_GRADE_POINTS", "SUB6_CREDIT", "SUB6_CREDIT_POINTS", "SUB6_REMARKS",
                // "SUB7NM", "SUB7", "SUB7_TH_MRKS","SUB7_PR_MRKS","SUB7_CE_MRKS","SUB7_VV_MRKS","SUB7_TOT", "SUB7_GRADE", "SUB7_GRADE_POINTS", "SUB7_CREDIT", "SUB7_CREDIT_POINTS", "SUB7_REMARKS",
                // "SUB8NM", "SUB8", "SUB8_TH_MRKS","SUB8_PR_MRKS","SUB8_CE_MRKS","SUB8_VV_MRKS","SUB8_TOT", "SUB8_GRADE", "SUB8_GRADE_POINTS", "SUB8_CREDIT", "SUB8_CREDIT_POINTS", "SUB8_REMARKS",
                // "SUB9NM", "SUB9", "SUB9_TH_MRKS","SUB9_PR_MRKS","SUB9_CE_MRKS","SUB9_VV_MRKS","SUB9_TOT", "SUB9_GRADE", "SUB9_GRADE_POINTS", "SUB9_CREDIT", "SUB9_CREDIT_POINTS", "SUB9_REMARKS",
                // "SUB10NM", "SUB10", "SUB10_TH_MRKS","SUB10_PR_MRKS","SUB10_CE_MRKS","SUB10_VV_MRKS","SUB10_TOT", "SUB10_GRADE", "SUB10_GRADE_POINTS", "SUB10_CREDIT", "SUB10_CREDIT_POINTS", "SUB10_REMARKS",

                // Exceptional Format From DSI
                "USN","Name","Semester","Course Code","Course Title","CIA-SEE","Credits","Grade","Grade Point","credit_hours" /*"SGPA","CGPA",*/,"Result Year","TOTAL CIA","TOTAL SEE"
            };
        }

        public static Dictionary<string, string> GetSubjectDetailHeaderMapping()
        {
            return new Dictionary<string, string>()
            {
                {"SUB1NM", "SUBNM"},
                {"SUB1", "SUB"},
                {"SUB1MAX", "SUBMAX"},
                {"SUB1MIN", "SUBMIN"},
                {"SUB1_TH_MAX", "SUB_TH_MAX"},
                {"SUB1_TH_MIN", "SUB_TH_MIN"},
                {"SUB1_PR_MAX", "SUB_PR_MAX"},
                {"SUB1_PR_MIN", "SUB_PR_MIN"},
                {"SUB1_CE_MAX", "SUB_CE_MAX"},
                {"SUB1_CE_MIN", "SUB_CE_MIN"},
                {"SUB1_VV_MAX", "SUB_VV_MAX"},
                {"SUB1_VV_MIN", "SUB_VV_MIN"},
                {"SUB1_TH_MRKS", "SUB_TH_MRKS"},
                {"SUB1_PR_MRKS", "SUB_PR_MRKS"},
                {"SUB1_CE_MRKS", "SUB_CE_MRKS"},
                {"SUB1_VV_MRKS", "SUB_VV_MRKS"},
                {"SUB1_TOT", "SUB_TOT"},
                {"SUB1_STATUS", "SUB_STATUS"},
                {"SUB1_GRADE", "SUB_GRADE"},
                {"SUB1_GRADE_POINTS", "SUB_GRADE_POINTS"},
                {"SUB1_CREDIT", "SUB_CREDIT"},
                {"SUB1_CREDIT_POINTS", "SUB_CREDIT_POINTS"},
                {"SUB1_GRACE", "SUB_GRACE"},
                {"SUB1_REMARKS", "SUB_REMARKS"},


                {"SUB2NM", "SUBNM"}, {"SUB2", "SUB"}, {"SUB2MAX", "SUBMAX"}, {"SUB2MIN", "SUBMIN"}, {"SUB2_TH_MAX", "SUB_TH_MAX"}, {"SUB2_TH_MIN", "SUB_TH_MIN"}, {"SUB2_PR_MAX", "SUB_PR_MAX"}, {"SUB2_PR_MIN", "SUB_PR_MIN"}, {"SUB2_CE_MAX", "SUB_CE_MAX"}, {"SUB2_CE_MIN", "SUB_CE_MIN"}, {"SUB2_VV_MAX", "SUB_VV_MAX"}, {"SUB2_VV_MIN", "SUB_VV_MIN"}, {"SUB2_TH_MRKS", "SUB_TH_MRKS"}, {"SUB2_PR_MRKS", "SUB_PR_MRKS"}, {"SUB2_CE_MRKS", "SUB_CE_MRKS"}, {"SUB2_VV_MRKS", "SUB_VV_MRKS"}, {"SUB2_TOT", "SUB_TOT"}, {"SUB2_STATUS", "SUB_STATUS"}, {"SUB2_GRADE", "SUB_GRADE"}, {"SUB2_GRADE_POINTS", "SUB_GRADE_POINTS"}, {"SUB2_CREDIT", "SUB_CREDIT"}, {"SUB2_CREDIT_POINTS", "SUB_CREDIT_POINTS"}, {"SUB2_GRACE", "SUB_GRACE"}, {"SUB2_REMARKS", "SUB_REMARKS"},
                {"SUB3NM", "SUBNM"}, {"SUB3", "SUB"}, {"SUB3MAX", "SUBMAX"}, {"SUB3MIN", "SUBMIN"}, {"SUB3_TH_MAX", "SUB_TH_MAX"}, {"SUB3_TH_MIN", "SUB_TH_MIN"}, {"SUB3_PR_MAX", "SUB_PR_MAX"}, {"SUB3_PR_MIN", "SUB_PR_MIN"}, {"SUB3_CE_MAX", "SUB_CE_MAX"}, {"SUB3_CE_MIN", "SUB_CE_MIN"}, {"SUB3_VV_MAX", "SUB_VV_MAX"}, {"SUB3_VV_MIN", "SUB_VV_MIN"}, {"SUB3_TH_MRKS", "SUB_TH_MRKS"}, {"SUB3_PR_MRKS", "SUB_PR_MRKS"}, {"SUB3_CE_MRKS", "SUB_CE_MRKS"}, {"SUB3_VV_MRKS", "SUB_VV_MRKS"}, {"SUB3_TOT", "SUB_TOT"}, {"SUB3_STATUS", "SUB_STATUS"}, {"SUB3_GRADE", "SUB_GRADE"}, {"SUB3_GRADE_POINTS", "SUB_GRADE_POINTS"}, {"SUB3_CREDIT", "SUB_CREDIT"}, {"SUB3_CREDIT_POINTS", "SUB_CREDIT_POINTS"}, {"SUB3_GRACE", "SUB_GRACE"}, {"SUB3_REMARKS", "SUB_REMARKS"},
                {"SUB4NM", "SUBNM"}, {"SUB4", "SUB"}, {"SUB4MAX", "SUBMAX"}, {"SUB4MIN", "SUBMIN"}, {"SUB4_TH_MAX", "SUB_TH_MAX"}, {"SUB4_TH_MIN", "SUB_TH_MIN"}, {"SUB4_PR_MAX", "SUB_PR_MAX"}, {"SUB4_PR_MIN", "SUB_PR_MIN"}, {"SUB4_CE_MAX", "SUB_CE_MAX"}, {"SUB4_CE_MIN", "SUB_CE_MIN"}, {"SUB4_VV_MAX", "SUB_VV_MAX"}, {"SUB4_VV_MIN", "SUB_VV_MIN"}, {"SUB4_TH_MRKS", "SUB_TH_MRKS"}, {"SUB4_PR_MRKS", "SUB_PR_MRKS"}, {"SUB4_CE_MRKS", "SUB_CE_MRKS"}, {"SUB4_VV_MRKS", "SUB_VV_MRKS"}, {"SUB4_TOT", "SUB_TOT"}, {"SUB4_STATUS", "SUB_STATUS"}, {"SUB4_GRADE", "SUB_GRADE"}, {"SUB4_GRADE_POINTS", "SUB_GRADE_POINTS"}, {"SUB4_CREDIT", "SUB_CREDIT"}, {"SUB4_CREDIT_POINTS", "SUB_CREDIT_POINTS"}, {"SUB4_GRACE", "SUB_GRACE"}, {"SUB4_REMARKS", "SUB_REMARKS"},
                {"SUB5NM", "SUBNM"}, {"SUB5", "SUB"}, {"SUB5MAX", "SUBMAX"}, {"SUB5MIN", "SUBMIN"}, {"SUB5_TH_MAX", "SUB_TH_MAX"}, {"SUB5_TH_MIN", "SUB_TH_MIN"}, {"SUB5_PR_MAX", "SUB_PR_MAX"}, {"SUB5_PR_MIN", "SUB_PR_MIN"}, {"SUB5_CE_MAX", "SUB_CE_MAX"}, {"SUB5_CE_MIN", "SUB_CE_MIN"}, {"SUB5_VV_MAX", "SUB_VV_MAX"}, {"SUB5_VV_MIN", "SUB_VV_MIN"}, {"SUB5_TH_MRKS", "SUB_TH_MRKS"}, {"SUB5_PR_MRKS", "SUB_PR_MRKS"}, {"SUB5_CE_MRKS", "SUB_CE_MRKS"}, {"SUB5_VV_MRKS", "SUB_VV_MRKS"}, {"SUB5_TOT", "SUB_TOT"}, {"SUB5_STATUS", "SUB_STATUS"}, {"SUB5_GRADE", "SUB_GRADE"}, {"SUB5_GRADE_POINTS", "SUB_GRADE_POINTS"}, {"SUB5_CREDIT", "SUB_CREDIT"}, {"SUB5_CREDIT_POINTS", "SUB_CREDIT_POINTS"}, {"SUB5_GRACE", "SUB_GRACE"}, {"SUB5_REMARKS", "SUB_REMARKS"},
                {"SUB6NM", "SUBNM"}, {"SUB6", "SUB"}, {"SUB6MAX", "SUBMAX"}, {"SUB6MIN", "SUBMIN"}, {"SUB6_TH_MAX", "SUB_TH_MAX"}, {"SUB6_TH_MIN", "SUB_TH_MIN"}, {"SUB6_PR_MAX", "SUB_PR_MAX"}, {"SUB6_PR_MIN", "SUB_PR_MIN"}, {"SUB6_CE_MAX", "SUB_CE_MAX"}, {"SUB6_CE_MIN", "SUB_CE_MIN"}, {"SUB6_VV_MAX", "SUB_VV_MAX"}, {"SUB6_VV_MIN", "SUB_VV_MIN"}, {"SUB6_TH_MRKS", "SUB_TH_MRKS"}, {"SUB6_PR_MRKS", "SUB_PR_MRKS"}, {"SUB6_CE_MRKS", "SUB_CE_MRKS"}, {"SUB6_VV_MRKS", "SUB_VV_MRKS"}, {"SUB6_TOT", "SUB_TOT"}, {"SUB6_STATUS", "SUB_STATUS"}, {"SUB6_GRADE", "SUB_GRADE"}, {"SUB6_GRADE_POINTS", "SUB_GRADE_POINTS"}, {"SUB6_CREDIT", "SUB_CREDIT"}, {"SUB6_CREDIT_POINTS", "SUB_CREDIT_POINTS"}, {"SUB6_GRACE", "SUB_GRACE"}, {"SUB6_REMARKS", "SUB_REMARKS"},
                {"SUB7NM", "SUBNM"}, {"SUB7", "SUB"}, {"SUB7MAX", "SUBMAX"}, {"SUB7MIN", "SUBMIN"}, {"SUB7_TH_MAX", "SUB_TH_MAX"}, {"SUB7_TH_MIN", "SUB_TH_MIN"}, {"SUB7_PR_MAX", "SUB_PR_MAX"}, {"SUB7_PR_MIN", "SUB_PR_MIN"}, {"SUB7_CE_MAX", "SUB_CE_MAX"}, {"SUB7_CE_MIN", "SUB_CE_MIN"}, {"SUB7_VV_MAX", "SUB_VV_MAX"}, {"SUB7_VV_MIN", "SUB_VV_MIN"}, {"SUB7_TH_MRKS", "SUB_TH_MRKS"}, {"SUB7_PR_MRKS", "SUB_PR_MRKS"}, {"SUB7_CE_MRKS", "SUB_CE_MRKS"}, {"SUB7_VV_MRKS", "SUB_VV_MRKS"}, {"SUB7_TOT", "SUB_TOT"}, {"SUB7_STATUS", "SUB_STATUS"}, {"SUB7_GRADE", "SUB_GRADE"}, {"SUB7_GRADE_POINTS", "SUB_GRADE_POINTS"}, {"SUB7_CREDIT", "SUB_CREDIT"}, {"SUB7_CREDIT_POINTS", "SUB_CREDIT_POINTS"}, {"SUB7_GRACE", "SUB_GRACE"}, {"SUB7_REMARKS", "SUB_REMARKS"},
                {"SUB8NM", "SUBNM"}, {"SUB8", "SUB"}, {"SUB8MAX", "SUBMAX"}, {"SUB8MIN", "SUBMIN"}, {"SUB8_TH_MAX", "SUB_TH_MAX"}, {"SUB8_TH_MIN", "SUB_TH_MIN"}, {"SUB8_PR_MAX", "SUB_PR_MAX"}, {"SUB8_PR_MIN", "SUB_PR_MIN"}, {"SUB8_CE_MAX", "SUB_CE_MAX"}, {"SUB8_CE_MIN", "SUB_CE_MIN"}, {"SUB8_VV_MAX", "SUB_VV_MAX"}, {"SUB8_VV_MIN", "SUB_VV_MIN"}, {"SUB8_TH_MRKS", "SUB_TH_MRKS"}, {"SUB8_PR_MRKS", "SUB_PR_MRKS"}, {"SUB8_CE_MRKS", "SUB_CE_MRKS"}, {"SUB8_VV_MRKS", "SUB_VV_MRKS"}, {"SUB8_TOT", "SUB_TOT"}, {"SUB8_STATUS", "SUB_STATUS"}, {"SUB8_GRADE", "SUB_GRADE"}, {"SUB8_GRADE_POINTS", "SUB_GRADE_POINTS"}, {"SUB8_CREDIT", "SUB_CREDIT"}, {"SUB8_CREDIT_POINTS", "SUB_CREDIT_POINTS"}, {"SUB8_GRACE", "SUB_GRACE"}, {"SUB8_REMARKS", "SUB_REMARKS"},
                {"SUB9NM", "SUBNM"}, {"SUB9", "SUB"}, {"SUB9MAX", "SUBMAX"}, {"SUB9MIN", "SUBMIN"}, {"SUB9_TH_MAX", "SUB_TH_MAX"}, {"SUB9_TH_MIN", "SUB_TH_MIN"}, {"SUB9_PR_MAX", "SUB_PR_MAX"}, {"SUB9_PR_MIN", "SUB_PR_MIN"}, {"SUB9_CE_MAX", "SUB_CE_MAX"}, {"SUB9_CE_MIN", "SUB_CE_MIN"}, {"SUB9_VV_MAX", "SUB_VV_MAX"}, {"SUB9_VV_MIN", "SUB_VV_MIN"}, {"SUB9_TH_MRKS", "SUB_TH_MRKS"}, {"SUB9_PR_MRKS", "SUB_PR_MRKS"}, {"SUB9_CE_MRKS", "SUB_CE_MRKS"}, {"SUB9_VV_MRKS", "SUB_VV_MRKS"}, {"SUB9_TOT", "SUB_TOT"}, {"SUB9_STATUS", "SUB_STATUS"}, {"SUB9_GRADE", "SUB_GRADE"}, {"SUB9_GRADE_POINTS", "SUB_GRADE_POINTS"}, {"SUB9_CREDIT", "SUB_CREDIT"}, {"SUB9_CREDIT_POINTS", "SUB_CREDIT_POINTS"}, {"SUB9_GRACE", "SUB_GRACE"}, {"SUB9_REMARKS", "SUB_REMARKS"},
                {"SUB10NM", "SUBNM"}, {"SUB10", "SUB"}, {"SUB10MAX", "SUBMAX"}, {"SUB10MIN", "SUBMIN"}, {"SUB10_TH_MAX", "SUB_TH_MAX"}, {"SUB10_TH_MIN", "SUB_TH_MIN"}, {"SUB10_PR_MAX", "SUB_PR_MAX"}, {"SUB10_PR_MIN", "SUB_PR_MIN"}, {"SUB10_CE_MAX", "SUB_CE_MAX"}, {"SUB10_CE_MIN", "SUB_CE_MIN"}, {"SUB10_VV_MAX", "SUB_VV_MAX"}, {"SUB10_VV_MIN", "SUB_VV_MIN"}, {"SUB10_TH_MRKS", "SUB_TH_MRKS"}, {"SUB10_PR_MRKS", "SUB_PR_MRKS"}, {"SUB10_CE_MRKS", "SUB_CE_MRKS"}, {"SUB10_VV_MRKS", "SUB_VV_MRKS"}, {"SUB10_TOT", "SUB_TOT"}, {"SUB10_STATUS", "SUB_STATUS"}, {"SUB10_GRADE", "SUB_GRADE"}, {"SUB10_GRADE_POINTS", "SUB_GRADE_POINTS"}, {"SUB10_CREDIT", "SUB_CREDIT"}, {"SUB10_CREDIT_POINTS", "SUB_CREDIT_POINTS"}, {"SUB10_GRACE", "SUB_GRACE"}, {"SUB10_REMARKS", "SUB_REMARKS"}, 


                
                // { "SUB1NM",              "SUBNM"},
                // {"SUB1",                "SUB"},
                // {"SUB1_GRADE",          "SUB_GRADE"},
                // {"SUB1_GRADE_POINTS",   "SUB_GRADE_POINTS"},
                // {"SUB1_CREDIT",         "SUB_CREDIT"},
                // {"SUB1_CREDIT_POINTS",  "SUB_CREDIT_POINTS"},
                // {"SUB1_REMARKS",        "SUB_REMARKS"},

                // {"SUB2NM", "SUBNM"}, {"SUB2", "SUB"}, {"SUB2_GRADE", "SUB_GRADE"}, {"SUB2_GRADE_POINTS", "SUB_GRADE_POINTS"}, {"SUB2_CREDIT", "SUB_CREDIT"}, {"SUB2_CREDIT_POINTS", "SUB_CREDIT_POINTS"}, {"SUB2_REMARKS", "SUB_REMARKS"},
                // {"SUB3NM", "SUBNM"}, {"SUB3", "SUB"}, {"SUB3_GRADE", "SUB_GRADE"}, {"SUB3_GRADE_POINTS", "SUB_GRADE_POINTS"}, {"SUB3_CREDIT", "SUB_CREDIT"}, {"SUB3_CREDIT_POINTS", "SUB_CREDIT_POINTS"}, {"SUB3_REMARKS", "SUB_REMARKS"},
                // {"SUB4NM", "SUBNM"}, {"SUB4", "SUB"}, {"SUB4_GRADE", "SUB_GRADE"}, {"SUB4_GRADE_POINTS", "SUB_GRADE_POINTS"}, {"SUB4_CREDIT", "SUB_CREDIT"}, {"SUB4_CREDIT_POINTS", "SUB_CREDIT_POINTS"}, {"SUB4_REMARKS", "SUB_REMARKS"},
                // {"SUB5NM", "SUBNM"}, {"SUB5", "SUB"}, {"SUB5_GRADE", "SUB_GRADE"}, {"SUB5_GRADE_POINTS", "SUB_GRADE_POINTS"}, {"SUB5_CREDIT", "SUB_CREDIT"}, {"SUB5_CREDIT_POINTS", "SUB_CREDIT_POINTS"}, {"SUB5_REMARKS", "SUB_REMARKS"},
                // {"SUB6NM", "SUBNM"}, {"SUB6", "SUB"}, {"SUB6_GRADE", "SUB_GRADE"}, {"SUB6_GRADE_POINTS", "SUB_GRADE_POINTS"}, {"SUB6_CREDIT", "SUB_CREDIT"}, {"SUB6_CREDIT_POINTS", "SUB_CREDIT_POINTS"}, {"SUB6_REMARKS", "SUB_REMARKS"},
                // {"SUB7NM", "SUBNM"}, {"SUB7", "SUB"}, {"SUB7_GRADE", "SUB_GRADE"}, {"SUB7_GRADE_POINTS", "SUB_GRADE_POINTS"}, {"SUB7_CREDIT", "SUB_CREDIT"}, {"SUB7_CREDIT_POINTS", "SUB_CREDIT_POINTS"}, {"SUB7_REMARKS", "SUB_REMARKS"},
                // {"SUB8NM", "SUBNM"}, {"SUB8", "SUB"}, {"SUB8_GRADE", "SUB_GRADE"}, {"SUB8_GRADE_POINTS", "SUB_GRADE_POINTS"}, {"SUB8_CREDIT", "SUB_CREDIT"}, {"SUB8_CREDIT_POINTS", "SUB_CREDIT_POINTS"}, {"SUB8_REMARKS", "SUB_REMARKS"},
                // {"SUB9NM", "SUBNM"}, {"SUB9", "SUB"}, {"SUB9_GRADE", "SUB_GRADE"}, {"SUB9_GRADE_POINTS", "SUB_GRADE_POINTS"}, {"SUB9_CREDIT", "SUB_CREDIT"}, {"SUB9_CREDIT_POINTS", "SUB_CREDIT_POINTS"}, {"SUB9_REMARKS", "SUB_REMARKS"},
                // {"SUB10NM", "SUBNM"}, {"SUB10", "SUB"}, {"SUB10_GRADE", "SUB_GRADE"}, {"SUB10_GRADE_POINTS", "SUB_GRADE_POINTS"}, {"SUB10_CREDIT", "SUB_CREDIT"}, {"SUB10_CREDIT_POINTS", "SUB_CREDIT_POINTS"}, {"SUB10_REMARKS", "SUB_REMARKS"}
            };
        }

        public static List<string> GetPrimaryHeaders()
        {
            // must be renamed header name (as per our database, executed after renaming header function)
            return new List<string>()
            {
                "CourseName", "EnrollmentNo", "StudentName", "Semester", /* "Term", */ "ExamType", "CGPA", "SGPA", "Month", "Year"
            };
        }

        public static List<List<string>> GetSecondaryHeaderGroups(int maxSubjectCount)
        {
            List<List<string>> secondaryHeaderGroups = new List<List<string>>();

            for (int i = 0; i < maxSubjectCount; i++)
            {
                secondaryHeaderGroups.Add(
                                new List<string>() {
                                                        $"SUB{i+1}NM",
                                                        $"SUB{i+1}",
                                                        $"SUB{i+1}MAX",
                                                        $"SUB{i+1}MIN",
                                                        $"SUB{i+1}_TH_MAX",
                                                        $"SUB{i+1}_TH_MIN",
                                                        $"SUB{i+1}_PR_MAX",
                                                        $"SUB{i+1}_PR_MIN",
                                                        $"SUB{i+1}_CE_MAX",
                                                        $"SUB{i+1}_CE_MIN",
                                                        $"SUB{i+1}_VV_MAX",
                                                        $"SUB{i+1}_VV_MIN",
                                                        $"SUB{i+1}_TH_MRKS",
                                                        $"SUB{i+1}_PR_MRKS",
                                                        $"SUB{i+1}_CE_MRKS",
                                                        $"SUB{i+1}_VV_MRKS",
                                                        $"SUB{i+1}_TOT",
                                                        $"SUB{i+1}_STATUS",
                                                        $"SUB{i+1}_GRADE",
                                                        $"SUB{i+1}_GRADE_POINTS",
                                                        $"SUB{i+1}_CREDIT",
                                                        $"SUB{i+1}_CREDIT_POINTS",
                                                        $"SUB{i+1}_GRACE",
                                                        $"SUB{i+1}_REMARKS"

                                                        // $"SUB{i+1}NM",
                                                        // $"SUB{i+1}",
                                                        // $"SUB{i+1}_TH_MRKS",
                                                        // $"SUB{i+1}_PR_MRKS",
                                                        // $"SUB{i+1}_CE_MRKS",
                                                        // $"SUB{i+1}_VV_MRKS",
                                                        // $"SUB{i+1}_TOT",
                                                        // $"SUB{i+1}_GRADE",
                                                        // $"SUB{i+1}_GRADE_POINTS",
                                                        // $"SUB{i+1}_CREDIT",
                                                        // $"SUB{i+1}_CREDIT_POINTS",
                                                        // $"SUB{i+1}_REMARKS"
                                                    }
                                );
            }

            return secondaryHeaderGroups;
        }

        public static List<string> GetCommonSecondaryHeaders()
        {
            return new List<string>()
            {
                "SubjectName", "SubjectCode", "TotalMaxMark", "TotalPassingMark", "ExternalTheoryMaxMark", "ExternalTheoryPassingMark", "ExternalPracticalMaxMark", "ExternalPracticalPassingMark", "InternalMaxMark", "InternalPassingMark", "ExternalVivaVoceMaxMark", "ExternalVivaVocePassingMark", "TheoryObtainedMark", "PracticalObtainedMark", "InternalObtainedMark", "ExternalVivaVoceObtainedMark", "TotalObtainedMark", "SubjectStatus", "GradeName", "GradePoint", "Credit", "CreditPoint", "Grace", "ResultRemarks", 
            };
        }

        #endregion Data Store
        
    }
}