using System;
using System.IO;
using System.Data;
using System.Linq;
using System.Collections.Generic;
using ClosedXML.Excel;

namespace DataTransformation.PPSUData
{
    public class PPSUTransformationPipeline
    {
        public PPSUTransformationPipeline()
        {

        }

        public void Run(string inputPath, string outputFolderPath)
        {
            Directory.CreateDirectory(outputFolderPath);

            List<string> files = new List<string>();

            if (Directory.Exists(inputPath))
            {
                // If inputPath is a directory, get all .xlsx files
                files.AddRange(Directory.GetFiles(inputPath, "*.xlsx", SearchOption.AllDirectories));
            }
            else if (File.Exists(inputPath) && inputPath.EndsWith(".xlsx"))
            {
                // If inputPath is a single .xlsx file, add it to the list
                files.Add(inputPath);
            }
            else
            {
                Console.WriteLine("Invalid input path. Please provide a valid folder or Excel file.");
                return;
            }

            foreach (var inputFilePath in files)
            {
                ProcessFile(inputFilePath, inputPath, outputFolderPath);
            }

            Console.WriteLine("Processing complete!");
        }

        static void ProcessFile(string inputFilePath, string inputFolderPath, string outputFolderPath)
        {
            try
            {
                string relativePath = inputFilePath.Substring(inputFolderPath.Length).TrimStart(Path.DirectorySeparatorChar);
                string outputFilePath = Path.Combine(outputFolderPath, relativePath);
                Directory.CreateDirectory(Path.GetDirectoryName(outputFilePath));
                string fileNameWithoutExtension = Path.GetFileNameWithoutExtension(inputFilePath);
                string parentFolderName = new DirectoryInfo(Path.GetDirectoryName(inputFilePath)).Name;

                using (var workbook = new XLWorkbook(inputFilePath))
                {
                    var worksheet = workbook.Worksheet(1);
                    var dataTable = worksheet.RangeUsed().AsTable().AsNativeDataTable();

                    DataTable outputTable = new DataTable();
                    outputTable.Columns.Add("EnrollmentNo", typeof(string));
                    outputTable.Columns.Add("StudentName", typeof(string));
                    outputTable.Columns.Add("CourseName", typeof(string));
                    outputTable.Columns.Add("ResultDeclarationdate", typeof(string));
                    outputTable.Columns.Add("Semester", typeof(string));
                    outputTable.Columns.Add("ResultRemarks", typeof(string));
                    outputTable.Columns.Add("SemesterTotalCredit", typeof(string));
                    outputTable.Columns.Add("SubjectCode", typeof(string));
                    outputTable.Columns.Add("SubjectName", typeof(string));
                    outputTable.Columns.Add("AssessmentType", typeof(string));
                    outputTable.Columns.Add("Credit", typeof(string));
                    outputTable.Columns.Add("GradeName", typeof(string));
                    outputTable.Columns.Add("ExamName", typeof(string));

                    string currentEnrollmentNo = string.Empty;
                    string currentStudentName = string.Empty;
                    string currentCourseName = string.Empty;
                    string currentResultDate = string.Empty;
                    string currentSemester = string.Empty;
                    string currentRemark = string.Empty;
                    string currentTotalCredit = string.Empty;

                    foreach (var row in dataTable.AsEnumerable().Skip(1))
                    {
                        var enrollmentNo = row.Field<string>("Enrollment No.");
                        var subjectDetails = row.Field<string>("Subject Details");

                        if (string.IsNullOrEmpty(subjectDetails))
                        {
                            currentEnrollmentNo = enrollmentNo;
                            currentStudentName = row.Field<string>("Student Name");
                            currentCourseName = row.Field<string>("Program");
                            currentResultDate = row.Field<string>("Date");
                            currentSemester = row.Field<string>("Semester");
                            currentRemark = row.Field<string>("Month-Year");
                            currentTotalCredit = row.Field<string>("Total Credit");
                        }

                        if (!string.IsNullOrEmpty(subjectDetails))
                        {
                            ProcessSubjectDetails(row, outputTable, currentEnrollmentNo, currentStudentName, currentCourseName, currentResultDate, currentSemester, currentRemark, currentTotalCredit, fileNameWithoutExtension, parentFolderName);
                        }
                    }

                    using (var outputWorkbook = new XLWorkbook())
                    {
                        var outputWorksheet = outputWorkbook.Worksheets.Add(outputTable, "ProcessedData");
                        outputWorkbook.SaveAs(outputFilePath);
                    }

                    Console.WriteLine($"Processed file: {inputFilePath} -> {outputFilePath}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error processing file '{inputFilePath}': {ex.Message}");
            }
        }

        static void ProcessSubjectDetails(DataRow row, DataTable outputTable, string enrollmentNo, string studentName, string courseName, string resultDate, string semester, string remark, string totalCredit, string fileNameWithoutExtension, string parentFolderName)
        {
            var subjectDetails = row.Field<string>("Subject Details");
            var columns = subjectDetails.Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
            string subjectCode = new string(columns[0].Take(8).ToArray());
            string subjectName = row.Field<string>("Column10");
            string evalType = row.Field<string>("Column11")?.Trim().ToUpper();
            string credit = row.Field<string>("Column12");
            string grade = row.Field<string>("Column13");

            var newRow = outputTable.NewRow();
            newRow["EnrollmentNo"] = enrollmentNo;
            newRow["StudentName"] = studentName;
            newRow["CourseName"] = courseName;
            newRow["ResultDeclarationdate"] = resultDate;
            newRow["Semester"] = semester;
            newRow["ResultRemarks"] = remark;
            newRow["SemesterTotalCredit"] = totalCredit;
            newRow["SubjectCode"] = subjectCode;
            newRow["SubjectName"] = subjectName;
            newRow["AssessmentType"] = evalType;
            newRow["Credit"] = credit;
            newRow["GradeName"] = grade;
            newRow["ExamName"] = $"{remark} - {fileNameWithoutExtension} - {parentFolderName}";

            outputTable.Rows.Add(newRow);
        }

    }
}