using System;
using System.Data;
using System.Collections.Generic;
using OfficeOpenXml;
using DataImport.Config;
using DataImport.Utility;

namespace DataImport.ImportPipeline
{
    public class MainImportBase
    {
        public static void ImportIntoMain(string connectionString)
        {
            // confirmation pop up for selective examlids
            MessageBox.Show("Configure ExamLID Condition in ImportConfig.cs \n\nList of ExamLIDs : " + ImportConfig.GetExamLIDCondition(), "Configure ExamLID Condition", MessageBoxButtons.OK, MessageBoxIcon.Information);

            TransformAndImportIntoDataBase(connectionString, "EXM_ExamSeason");
            TransformAndImportIntoDataBase(connectionString, "EXM_Exam");

            UpdateMappedExamIDIntoLegacy(connectionString);

            TransformAndImportIntoDataBase(connectionString, "EXM_ExamConfig");
            TransformAndImportIntoDataBase(connectionString, "EXM_ExamWiseSubject");
            TransformAndImportIntoDataBase(connectionString, "EXM_ExamSchedule");
            TransformAndImportIntoDataBase(connectionString, "EXM_ResultStudent");
            TransformAndImportIntoDataBase(connectionString, "EXM_ResultGrade");
            TransformAndImportIntoDataBase(connectionString, "EXM_StudentWiseSubject");
        }

        public static void TransformAndImportIntoDataBase(string connectionString, string tableName)
        {
            DataTable DataToImport = TransformDataTableAsPerDataBase(connectionString, tableName);

            if (DataToImport.Rows.Count > 0)
            {
                DataTableViewer.ShowDataTableInExcel(DataToImport, "Data To Import Into " + tableName);
                
                // Confirm import with popup dialog
                DialogResult result = MessageBox.Show(
                    $"Do you want to proceed with importing {DataToImport.Rows.Count} rows into {tableName}?",
                    "Confirm Import",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);
            
                if (result != DialogResult.Yes)
                {
                    return;
                }

                DatabaseHandler.InsertDataIntoDatabase(DataToImport, connectionString, tableName);
                Console.WriteLine($"Successfully imported data into {tableName}");
            }
            else
            {
                // confirmation with popup dialog
                DialogResult result = MessageBox.Show(
                    $"No data to import in {tableName}. Want to continue?",
                    "Confirm Continue",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);
                if (result != DialogResult.Yes)
                {
                    return;
                }
                Console.WriteLine($"No data to import in {tableName}");
            }
        }


        public static DataTable TransformDataTableAsPerDataBase(string connectionString, string tableName)
        {
            // string ExamLIDCondition = ImportConfig.GetExamLIDCondition();

            string query = MainTableQueryPool.GetSQLSelectQuery(tableName);

            // query pop up
            // write this query into LOG_queryTest.txt
            FileHandler.ClearLogFile("LOG_queryTest.txt");
            FileHandler.LogMessage(query, "LOG_queryTest.txt");

            MessageBox.Show(query, $"{tableName} - Query", MessageBoxButtons.OK, MessageBoxIcon.Information);

            DataTable dataTable = DatabaseHandler.FetchDataTable(connectionString, query); // Master DataTable to collect all data


            HashSet<string> tableColumns = DatabaseHandler.GetDatabaseTableColumns(connectionString, tableName);

            DataTable dt = ImportUtility.FilterDataTableByColumns(dataTable, tableColumns);

            // trim all values of datatable (remove extra spaces from left and right side, only if data type is string)
            ImportUtility.TrimStringValueOfDataTable(ref dt);

            DataTable uniqueDataTable = ImportUtility.GetUniqueRows(dt);
            List<string> notNullableColumns = ImportConfig.NotNullableColumns(tableName);

            DataTable validDataTable = ImportUtility.ValidateDataTableByCheckingNullablility(uniqueDataTable, notNullableColumns, tableName, dataTable);

            List<string> checkUniquenessOn = ImportConfig.CheckUniquenessOn(tableName);

            DataTable filteredDataTable = ImportUtility.FilterDataTableByRemovingAlreadyInsertedData(tableName, validDataTable, connectionString, checkUniquenessOn);

            var (dt_relativeDuplicateData, dt_absoluteDuplicateData, dt_validateData) = ImportUtility.SeparateDuplicates(filteredDataTable, checkUniquenessOn, dataTable);

            if (ImportConfig.isExtractDublicateByCell && dt_absoluteDuplicateData.Rows.Count > 0)
            {
                // Check if the folder for invalid data exists, if not, create it
                string duplicateDataFolderPath = ImportConfig.dublicateDataByCellFolderPath;
                if (!Directory.Exists(duplicateDataFolderPath))
                {
                    Directory.CreateDirectory(duplicateDataFolderPath);
                }
                string duplicateDataFilePath = Path.Combine(duplicateDataFolderPath, $"{tableName}_DuplicateData.xlsx");
                Console.WriteLine("Duplicate data detected. Storing in Excel file : " + duplicateDataFilePath);
                FileHandler.SaveDataTableToExcel(dt_absoluteDuplicateData, duplicateDataFilePath);
            }
            else if (!ImportConfig.isExtractDublicateByCell && dt_absoluteDuplicateData.Rows.Count > 0)
            {
                // Console.WriteLine("\nisExtractDublicateByCell is set to false. Duplicate data will not be extracted.");
                // Console.WriteLine("want to continue? (y/n)");
                // string response = Console.ReadLine();
                // if (response.ToLower() != "y")
                // {
                //     throw new Exception("isExtractDublicateByCell is set to false. Duplicate data will not be extracted.");
                // }

                // Confirm with popup dialog
                DialogResult result = MessageBox.Show(
                    $"isExtractDublicateByCell is set to false. Duplicate data will EXIST and will not be extracted. Want to continue?",
                    "Proceed",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);
                if (result != DialogResult.Yes)
                {
                    throw new Exception("isExtractDublicateByCell is set to false. Duplicate data will not be extracted.");
                }
            }

            return dt_validateData; // Return the filtered DataTable
        }


        public static void UpdateMappedExamIDIntoLegacy(string connectionString)
        {
            string query_Update_MappedExamID_IN_EXL_ExamL = ImportConfig.GetQuery("Update_MappedExamID_IN_EXL_ExamL");

            // query pop up
            MessageBox.Show(query_Update_MappedExamID_IN_EXL_ExamL, "Update Query of MappedExamID in EXL_ExamL", MessageBoxButtons.OK, MessageBoxIcon.Information);
            
            DatabaseHandler.ExecuteQuery(connectionString, query_Update_MappedExamID_IN_EXL_ExamL);
            System.Console.WriteLine("Updated MappedExamID in EXL_ExamL");
        }

    }
}