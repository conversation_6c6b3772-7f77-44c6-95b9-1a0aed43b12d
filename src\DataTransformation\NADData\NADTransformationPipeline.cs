using DataTransformation.Common;
using System;
using System.Data;
using System.IO;
using Microsoft.VisualBasic.FileIO;
using ClosedXML.Excel;
using OfficeOpenXml;
using System.Text;
using DataTransformation.NADData;

namespace DataTransformation.NADData
{
    public class NADTransformationPipeline
    {
        //public NADTransformationPipeline()
        //{

        //}

        public void Run(string inputFolderPath, string outputFolderPath)
        {
            try
            {
                //DataTransformation.Common.HelperFunctions.ClearLogFile(NADConfig.LogFilePath);

                // Process All Excel Files
                HelperFunctions.ProcessDataInDirectory(inputFolderPath, outputFolderPath); 
            }
            catch (Exception ex)
            {
                //DataTransformation.Common.HelperFunctions.LogMessage("Error: " + ex.Message, NADConfig.LogFilePath);
                throw new Exception($"NAD transformation failed: {ex.Message}", ex);
            }
        }

        
    }
}
