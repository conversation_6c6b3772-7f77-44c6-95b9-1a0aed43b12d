using System;
using System.Collections.Generic;
using System.Data;
using System.IO;

namespace DataImport.Config
{
    public static class MainTableQueryPool
    {
        public static string GetSQLSelectQuery(string tableName)
        {
            string query;

            // throw new Exception("Configure Generic Select Query for Main tables");

            // fetch sql query from SQL Queries folder
            if (tableName == "EXM_ExamSeason")
            {
                query = File.ReadAllText("Config\\SQL Queries\\ImportQuery_EXM_ExamSeason.sql");
            }
            else if (tableName == "EXM_Exam")
            {
                query = File.ReadAllText("Config\\SQL Queries\\ImportQuery_EXM_Exam.sql");
            }
            else if (tableName == "EXM_ExamConfig")
            {
                query = File.ReadAllText("Config\\SQL Queries\\ImportQuery_EXM_ExamConfig.sql");
            }
            else if (tableName == "EXM_ExamWiseSubject")
            {
                query = File.ReadAllText("Config\\SQL Queries\\ImportQuery_EXM_ExamWiseSubject.sql");
            }
            else if (tableName == "EXM_ExamSchedule")
            {
                query = File.ReadAllText("Config\\SQL Queries\\ImportQuery_EXM_ExamSchedule.sql");
            }
            else if (tableName == "EXM_ResultStudent")
            {
                query = File.ReadAllText("Config\\SQL Queries\\ImportQuery_EXM_ResultStudent.sql");
            }
            else if (tableName == "EXM_ResultGrade")
            {
                query = File.ReadAllText("Config\\SQL Queries\\ImportQuery_EXM_ResultGrade.sql");
            }
            else if (tableName == "EXM_StudentWiseSubject")
            {
                query = File.ReadAllText("Config\\SQL Queries\\ImportQuery_EXM_StudentWiseSubject.sql");
            }
            else
            {
                throw new Exception("Invalid table name");
            }

            if (query.Contains("(_listOfExamLIDs_)"))
            {
                query = query.Replace("(_listOfExamLIDs_)", ImportConfig.GetExamLIDCondition());
            }
            else if (query.Contains("(_listOfExamIDs_)"))
            {
                query = query.Replace("(_listOfExamIDs_)", ImportConfig.GetExamIDCondition());
            }
            else
            {
                throw new Exception("Invalid query : No placeholder found for ExamLID or ExamID");
            }



            return query;
        }
    }
}