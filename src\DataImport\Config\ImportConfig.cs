using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;

namespace DataImport.Config
{
    public static class ImportConfig
    {
        public static readonly string LogFilePath = "../../data/Logs/import_logs.txt";
        // public static readonly string LogFilePath = "../../../../../data/Logs/import_logs.txt";

        //public static readonly string ITMConnectionString = "Your_Database_Connection_String";
        public static readonly string DSIConnectionString = "Data Source = dayananda.interactivedns.com; Initial Catalog = DSI_GNUMS_2022; User Id = u_dsi_gnums_live; Password = ************$9fe";  
        // public static readonly string LocalConnectionString = "Data Source=DESKTOP-5FHVJDB; Initial Catalog=DataImport; Integrated Security=True;";
        // public static readonly string DSIDemoConnectionString = "Data Source = dayananda.interactivedns.com; Initial Catalog = DSI_GNUMS_SandBox; User Id = dsi_gnums_sandboxuser; Password = ****************";



        //public static readonly string ITMIntermediateFolderPath = @"";
        public static readonly string DSIIntermediateFolderPath = @"..\..\data\ETLDataStore\NAD Data\DataToImport";
        // public static readonly string DSIIntermediateFolderPath = @"..\..\..\..\..\data\ETLDataStore\NAD Data\DataToImport";

        //public static readonly string LocalDataBaseIntermediateFolderPath = @"..\..\..\..\..\data\ETLDataStore\NAD Data\DataToImport";
        //public static readonly string LocalDataBaseIntermediateFolderPath = @"..\..\data\ETLDataStore\NAD Data\DataToImport";


        // public static readonly string alreadyInsertedDataFolderPath = @"..\..\..\..\..\data\ETLDataStore\AlreadyInsertedData";
        // public static readonly string dublicateDataByCellFolderPath = @"..\..\..\..\..\data\ETLDataStore\DuplicateData";
        // public static readonly string invalidDataFolderPath = @"..\..\..\..\..\data\ETLDataStore\InvalidData";
        
        public static readonly string alreadyInsertedDataFolderPath = @"..\..\data\ETLDataStore\AlreadyInsertedData";
        public static readonly string dublicateDataByCellFolderPath = @"..\..\data\ETLDataStore\DuplicateData";
        public static readonly string invalidDataFolderPath = @"..\..\data\ETLDataStore\InvalidData";


        public static readonly bool isExtractInvalidDataByCheckingNullability = true;
        // Extract Invalid Data (by checking nullability)
        // Set to true to extract invalid data by checking nullability, false to disable this feature

        public static readonly bool isExtractAlreadyInsertedData = true;
        // Extract Already Inserted Data (set configuration true if want to extract)

        public static readonly bool isExtractDublicateByCell = true;
        // Extract Dublicate Data (by checking duplicate cells)

        public static string GetExamLIDCondition()
        {
            // configure exams by examlid which you want to proceed further in main tables from legacy
            // throw new Exception("Configure ExamLID Condition");
            return "(0000)";
            // return "(979, 980, 981, 982, 983, 984, 985, 986, 987, 988, 989, 990, 991, 992, 993, 994, 995, 996, 997, 998, 999, 1000, 1001, 1002, 1003, 1004, 1005, 1006, 1007, 1008, 1009, 1010, 1011, 1012, 1013, 1014, 1015, 1016, 1017, 1018, 1019, 1020, 1021, 1022, 1023, 1024, 1025, 1026, 1027, 1028, 1029, 1030)";
        }

        public static string GetExamIDCondition()
        {
            // configure exams by examid which you want to proceed further in main tables from legacy
            // throw new Exception("Configure ExamID Condition");
            // return "";

            List<int> mappedExamIDs = GetMappedExamIDsFromExamL(DSIConnectionString, GetExamLIDCondition());

            string examIDCondition = "(" + string.Join(",", mappedExamIDs) + ")";

            System.Console.WriteLine("ExamID Condition : " + examIDCondition);

            return examIDCondition;
        }

        static List<int> GetExamLIDsFromExamL(string connectionString, string listofExamLIDs)
        {
            List<int> examLIds = new List<int>();

            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                connection.Open();
                string query = $@"
                    SELECT ExamLID 
                    FROM EXL_ExamL Where ExamLID in " + listofExamLIDs;

                using (SqlCommand command = new SqlCommand(query, connection))
                {
                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            examLIds.Add(Convert.ToInt32(reader["ExamLID"])); // Assuming ExamId is an INT
                        }
                    }
                }
            }

            return examLIds;
        }

        static List<int> GetMappedExamIDsFromExamL(string connectionString, string listofExamLIDs)
        {
            List<int> mappedExamIDs = new List<int>();

            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                connection.Open();
                string query = $@"
                    SELECT MappedExamID 
                    FROM EXL_ExamL Where ExamLID in "+ listofExamLIDs +" AND MappedExamID IS NOT NULL";

                System.Console.WriteLine("Query : " + query);

                using (SqlCommand command = new SqlCommand(query, connection))
                {
                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            mappedExamIDs.Add(Convert.ToInt32(reader["MappedExamID"])); // Assuming ExamId is an INT
                        }
                    }
                }
            }

            return mappedExamIDs;
        }


        public static string GetSQLSelectQuery(string tableName)
        {
            // configure select queries for each table
            throw new Exception("Configure Select Query");

            string query;

            if (tableName == "EXM_ExamSeason")
            {
                query = @"SELECT		
                                    DISTINCT
                                    'Imported' AS Remarks,
                                    INS_Faculty.FacultyShortName + ' - ' + EXL_ExamL.Term + ' - ' + MST_AdmissionYear.AdmissionYearName AS ExamSeasonName,
                                    EXL_ExamL.AcademicYearID,
                                    EXL_ExamL.FacultyID,
                                    EXL_ExamL.Term,
                                    INS_Faculty.UniversityID,
                                    EXL_ExamL.UserID,
                                    GETDATE() AS Created,
                                    GETDATE() AS Modified,
                                    GETDATE() AS 'ArchivalDate'
                        FROM EXL_ExamL
                        INNER JOIN INS_Faculty
                        ON EXL_ExamL.FacultyID = INS_Faculty.FacultyID

                        INNER JOIN MST_AdmissionYear 
                        ON EXL_ExamL.AcademicYearID = MST_AdmissionYear.AcademicYearID";
            }
            else if (tableName == "EXM_Exam")
            {
                query = @"SELECT 
                                    'Imported' as Remarks,
                                    'No Check' as AttendanceCriteria,
                                    ExamName as ExamPrintName,
                                    GETDATE() AS 'Created',
                                    GETDATE() AS 'Modiefied',
                                    GETDATE() AS 'ArchivalDate',

                                    EXL_ExamL.*, 

                                    EXM_ExamSeason.ExamSeasonID,
                                    MST_AcademicYear.FromDate,
                                    MST_AcademicYear.ToDate,
                                    INS_Faculty.UniversityID
                        FROM 
                                    EXL_ExamL
                        INNER JOIN	
                                    EXM_ExamSeason
                        ON 
                                    EXM_ExamSeason.AcademicYearID = EXL_ExamL.AcademicYearID
                                AND EXM_ExamSeason.Term = EXL_ExamL.Term
                                AND EXM_ExamSeason.FacultyID = EXL_ExamL.FacultyID
                        INNER JOIN
                                    MST_AcademicYear
                        ON
                                    MST_AcademicYear.AcademicYearID = EXM_ExamSeason.AcademicYearID
                        INNER JOIN
                                    INS_Faculty
                        ON
                                    INS_Faculty.FacultyID = EXL_ExamL.FacultyID";
            }
            else if (tableName == "EXM_ExamConfig")
            {
                query = @"SELECT
                                    'Entry' AS InternalMarkSubmissionMethod,
                                    'Imported' AS Description,
                                    GETDATE() AS Created,
                                    GETDATE() AS Modified,
                                    EXM_Exam.*
                            FROM EXM_Exam

                            LEFT OUTER JOIN  EXM_ExamConfig
                            ON		EXM_ExamConfig.ExamID = EXM_Exam.ExamID

                            WHERE EXM_ExamConfig.ExamID IS NULL";
            }
            else if (tableName == "EXM_ExamWiseSubject")
            {
                query = @"      DECLARE @CurrentDateTime datetime = GETDATE()

                                DECLARE	@dtDistinctSubject AS TABLE
                                (
                                        ExamID		int,
                                        SubjectID	int
                                )
                                INSERT INTO @dtDistinctSubject
                                SELECT
                                        DISTINCT
                                        MappedExamID,
                                        SubjectID
                                FROM	EXL_ExamL

                                INNER JOIN [dbo].[EXL_ExamStudentSubjectL]
                                ON		EXL_ExamStudentSubjectL.ExamLID = EXL_ExamL.ExamLID 

                                WHERE	[dbo].[EXL_ExamL].[MappedExamID] IS NOT NULL
                                AND		[dbo].[EXL_ExamStudentSubjectL].[SubjectID] IS NOT NULL
                                AND		[dbo].[EXL_ExamStudentSubjectL].[GradeName] <> 'Ex'
                                
                                SELECT	 [dtDistinctSubject].[ExamID]
                                        ,[dbo].[SUB_Subject].[SubjectID]
                                        ,[dbo].[SUB_Subject].[BaseDepartmentID]
                                        ,[dbo].[SUB_Subject].[SubjectCode]
                                        ,[dbo].[SUB_Subject].[SubjectName]
                                        ,[dbo].[SUB_Subject].[SubjectShortName]
                                        ,[dbo].[SUB_Subject].[SubjectGroupName]
                                        ,[dbo].[SUB_Subject].[Credit]
                                        ,[dbo].[SUB_Subject].[MarkCETheory]
                                        ,[dbo].[SUB_Subject].[MarkCEPractical]
                                        ,[dbo].[SUB_Subject].[MarkESETheory]
                                        ,[dbo].[SUB_Subject].[MarkESEPractical]
                                        ,[dbo].[SUB_Subject].[MarkTotal]
                                        ,[dbo].[SUB_Subject].[PassingMarkCETheory]
                                        ,[dbo].[SUB_Subject].[PassingMarkCEPractical]
                                        ,[dbo].[SUB_Subject].[PassingMarkESETheory]
                                        ,[dbo].[SUB_Subject].[PassingMarkESEPractical]
                                        ,NULL AS [PassingMarkTotal]  -- PassingOverAllPCT Not Entered In Exam
                                        ,[dbo].[SUB_Subject].[PaperMarkESETheory]
                                        ,[dbo].[SUB_Subject].[PaperPassingMarkESETheory]
                                        ,[dbo].[SUB_Subject].[ESETheoryEligibilityBy]
                                        ,[dbo].[SUB_Subject].[ESEPracticalEligibilityBy]

                                        ,ISNULL([dbo].[SUB_Subject].[MarkCETheory],0) + ISNULL([dbo].[SUB_Subject].[MarkESETheory],0) AS [MarkTotalTheory]
                                        ,NULL AS [PassingMarkTheory]
                                        ,ISNULL([dbo].[SUB_Subject].[MarkCEPractical],0) + ISNULL([dbo].[SUB_Subject].[MarkESEPractical],0) +  ISNULL([dbo].[SUB_Subject].[MarkCEPracticalElective],0) AS [MarkTotalPractical]
                                        ,NULL AS [PassingMarkPractical]

                                        ,[dbo].[SUB_Subject].[TheoryPassingPCT]
                                        ,[dbo].[SUB_Subject].[PracticalPassingPCT]
                                        ,[dbo].[SUB_Subject].[SubjectGroupID]
                                        ,[dbo].[SUB_Subject].[ExamDuration]
                                        ,[dbo].[SUB_Subject].[ExamRequirements]
                                        ,[dbo].[SUB_Subject].[ResultMergeFromSubjectID]
                                        ,[dbo].[SUB_Subject].[ResultMergeToSubjectID]
                                        ,[dbo].[SUB_Subject].[MarkVivaVoce]
                                        ,[dbo].[SUB_Subject].[PassingMarkVivaVoce]
                                        ,[dbo].[SUB_Subject].[IsNonGradialSubject]
                                        ,[dbo].[SUB_Subject].[PrerequisiteSubjectID]
                                        ,NULL AS [StudentsInternalTheory]
                                        ,NULL AS [StudentsInternalPractical]
                                        ,NULL AS [StudentsExternalTheory]
                                        ,NULL AS [StudentsExternalPractical]
                                        ,'Imported' as [Description]
                                        ,1 AS [UserID]
                                        ,@CurrentDateTime AS [Created]
                                        ,@CurrentDateTime AS [Modified]
                                        ,NULL AS [QuePaperFormatID]
                                        ,[dbo].[SUB_Subject].[PaperMarkESEPractical]
                                        ,[dbo].[SUB_Subject].[PassingPaperMarkESEPractical]
                                        ,NULL AS [ExternalTheoryPassingPCT]
                                        ,NULL AS [ExternalPracticalPassingPCT]
                                        ,[dbo].[SUB_Subject].[MarkCEPracticalElective]
                                        ,[dbo].[SUB_Subject].[PassingMarkCEPracticalElective]
                                        ,NULL AS [PassingMarkCETheoryMercy]
                                        ,NULL AS [PassingMarkCEPracticalMercy]
                                        ,NULL AS [PassingMarkESETheoryMercy]
                                        ,NULL AS [PassingMarkESEPracticalMercy]
                                        ,NULL AS [PassingMarkTotalMercy]
                                        ,NULL AS [PassingMarkCEPracticalElectiveMercy]
                                        ,NULL AS [PassingMarkVivaVoceMercy]

                                        ,NULL AS [NoOfSectionInPaper]
                                        ,NULL AS [TheoryFee]
                                        ,NULL AS [PracticalFee]
                                        ,NULL AS [ResultProcessedDateTime]
                                        ,NULL AS [ResultProcessedByUserID]

                                FROM	@dtDistinctSubject AS [dtDistinctSubject]

                                INNER JOIN [dbo].[SUB_Subject]
                                ON		[dbo].[SUB_Subject].[SubjectID] = [dtDistinctSubject].[SubjectID]

                                LEFT OUTER JOIN [dbo].[EXM_ExamWiseSubject]
                                ON		[dbo].[EXM_ExamWiseSubject].[SubjectID] = [dtDistinctSubject].[SubjectID]
                                AND		[dbo].[EXM_ExamWiseSubject].[ExamID] = [dtDistinctSubject].[ExamID]

                                WHERE	[dbo].[EXM_ExamWiseSubject].[ExamWiseSubjectID] IS NULL";
            }
            else if (tableName == "EXM_ExamSchedule")
            {
                query = @"  SELECT
                                    'Imported' AS Remarks,
                                    *
                            FROM EXM_ExamWiseSubject
                            WHERE ISNULL(MarkESETheory, 0) > 0";
            }
            else if (tableName == "EXM_ResultStudent")
            {
                query = @"  DECLARE @CurrentDateTime datetime = GETDATE()

                            DECLARE @dtResultStudent AS TABLE (
                                ExamID INT,
                                CourseID INT,
                                InstituteID INT,
                                ProgramID INT,
                                StudentID INT,
                                EnrollmentNo NVARCHAR(50), -- Specify appropriate length
                                SeatNo NVARCHAR(50),       -- Specify appropriate length
                                SPI DECIMAL(10, 2),        -- Example precision, adjust as needed
                                FailInSubject INT,
                                Remarks NVARCHAR(255),     -- Specify appropriate length
                                UserID INT,
                                Created DATETIME,
                                Modified DATETIME,
                                CGPA DECIMAL(10, 2),       -- Example precision, adjust as needed
                                TotalBackLog INT,
                                AttemptCount INT,
                                StudentLCName NVARCHAR(255),           -- Specify appropriate length
                                StudentResultRemarks NVARCHAR(255),   -- Specify appropriate length
                                CGPADisplay DECIMAL(10, 2)            -- Example precision, adjust as needed
                            );

                            INSERT INTO @dtResultStudent
                            SELECT
                                    [dbo].EXL_ExamL.[MappedExamID] AS ExamID,
                                    [dbo].EXL_ExamL.CourseID AS CourseID,
                                    [dbo].[STU_Student].InstituteID,
                                    [dbo].[STU_Student].ProgramID,
                                    [dbo].[STU_Student].[StudentID],
                                    [dbo].[STU_Student].[EnrollmentNo],
                                    [dbo].EXL_ExamStudentL.SeatNo,
                                    [dbo].EXL_ExamStudentL.SGPA AS SPI,
                                    [dbo].EXL_ExamStudentL.CurrentBacklog,
                                    'Imported' AS Remarks,
                                    1 AS UserID,
                                    GETDATE() AS Created,
                                    GETDATE() AS Modified,
                                    [dbo].EXL_ExamStudentL.CGPA,
                                    [dbo].EXL_ExamStudentL.TotalBacklog,
                                    [dbo].EXL_ExamStudentSubjectL.[AttemptNo] AS AttemptCount,

                                    [dbo].[STU_Student].[StudentLCName],
                                    --[dbo].EXL_ExamStudentL.OverallPassFail AS StudentResultRemarks,
                                    NULL AS StudentResultRemarks,
                                    [dbo].EXL_ExamStudentL.CGPA AS CGPADisplay
                            FROM	EXL_ExamL

                            INNER JOIN	EXL_ExamStudentL
                            ON			EXL_ExamStudentL.ExamLID = EXL_ExamL.ExamLID

                            INNER JOIN	[dbo].EXL_ExamStudentSubjectL
                            ON			 EXL_ExamStudentSubjectL.ExamLID = EXL_ExamL.ExamLID
                            AND			EXL_ExamStudentSubjectL.StudentID = EXL_ExamStudentL.StudentID


                            INNER JOIN [dbo].[STU_Student]
                            ON		[dbo].[STU_Student].[StudentID] = [dbo].EXL_ExamStudentL.[StudentID]
                            AND		[dbo].EXL_ExamL.[MappedExamID] IS NOT NULL
                            AND		[dbo].EXL_ExamStudentL.[StudentID] IS NOT NULL
                            --AND		ISNULL([dbo].EXL_ExamStudentSubjectL.[IsDuplicate], 0) = 0
                            AND		[dbo].EXL_ExamStudentSubjectL.[GradeName] <> 'Ex'
                            --AND		[dbo].EXL_ExamStudentSubjectL.[RecordNeedsToBeConsidered] = 'Yes'
                            --AND		[dbo].EXL_ExamStudentSubjectL.[Mapped_StudentID] = 2762

                            GROUP BY 
                                    [dbo].EXL_ExamL.[MappedExamID],
                                    [dbo].EXL_ExamL.CourseID,
                                    [dbo].[STU_Student].InstituteID,
                                    [dbo].[STU_Student].ProgramID,
                                    [dbo].[STU_Student].[StudentID],
                                    [dbo].[STU_Student].[EnrollmentNo],
                                    [dbo].EXL_ExamStudentL.SeatNo,
                                    [dbo].EXL_ExamStudentL.SGPA,
                                    [dbo].EXL_ExamStudentL.CurrentBacklog,
                                    [dbo].EXL_ExamStudentL.CGPA,
                                    [dbo].EXL_ExamStudentL.TotalBacklog,
                                    [dbo].EXL_ExamStudentSubjectL.[AttemptNo],

                                    [dbo].[STU_Student].[StudentLCName],
                                    --[dbo].EXL_ExamStudentL.OverallPassFail ,
                                    [dbo].EXL_ExamStudentL.CGPA


                            SELECT * FROM @dtResultStudent";
            }
            else if (tableName == "EXM_ResultGrade")
            {
                query = @"DECLARE	@CurrentDateTime AS DATETIME = GETDATE()

                            SELECT 
                                        [dbo].[EXL_ExamL].[MappedExamID] AS ExamID
                                        ,[dbo].[EXM_ResultStudent].[ResultStudentID]
                                        ,[dbo].[EXL_ExamStudentSubjectL].[StudentID]
                                        ,[dbo].[EXL_ExamStudentSubjectL].[EnrollmentNo]
                                        ,[dbo].[EXL_ExamStudentSubjectL].[SubjectID]
                                        ,[dbo].[EXL_ExamStudentSubjectL].[GradeID]
                                        ,[dbo].[EXL_ExamStudentSubjectL].[GradeName]
                                        ,CASE WHEN ISNULL([dbo].[EXL_ExamStudentSubjectL].[PassFail], 'Fail') = 'Pass'
                                            THEN 1
                                            ELSE 0
                                        END  AS   [IsPass]
                                        ,'Imported' AS [Remarks]
                                        , 1 AS [UserID]
                                        ,@CurrentDateTime AS [Created]
                                        ,@CurrentDateTime AS [Modified]
                                        ,(ISNULL(InternalTheoryObtainedMark, 0) + 
                                        ISNULL(ExternalTheoryObtainedMark, 0) + 
                                        ISNULL(PracticalObtainedMark, 0)) AS Mark
                                        ,InternalTheoryObtainedMark AS MarkInternalTheoryObtained
                                        ,ExternalTheoryObtainedMark AS MarkExternalTheoryObtained

                                        ,CASE WHEN ISNULL([dbo].[SUB_Subject].MarkCEPractical,0) > 0
                                            THEN PracticalObtainedMark 
                                            ELSE NULL
                                        END AS MarkInternalPracticalObtained
                                        
                                        ,CASE WHEN ISNULL([dbo].[SUB_Subject].MarkESEPractical,0) > 0
                                            THEN PracticalObtainedMark
                                            ELSE NULL
                                        END AS MarkExternalPracticalObtained

                                        --,PracticalObtainedMark AS MarkExternalPracticalObtained
                                                                                                                        
                                        ,CASE WHEN ISNULL([dbo].[SUB_Subject].[MarkCETheory],0) > 0
                                            AND  [dbo].[EXL_ExamStudentSubjectL].[InternalTheoryGrade] IS NOT NULL
                                            AND  [dbo].[EXL_ExamStudentSubjectL].[InternalTheoryGrade] = 'F'
                                            THEN 1
                                            ELSE 0
                                        END  AS   [IsFailInInternalTheory]
                                                                                                                        
                                        ,CASE WHEN ISNULL([dbo].[SUB_Subject].[MarkCEPractical],0) > 0
                                            AND  [dbo].[EXL_ExamStudentSubjectL].[PracticalGrade] IS NOT NULL
                                            AND  [dbo].[EXL_ExamStudentSubjectL].[PracticalGrade] = 'F'
                                            THEN 1
                                            ELSE 0
                                        END  AS   [IsFailInInternalPractical]
                                                                                                                        
                                        ,CASE WHEN ISNULL([dbo].[SUB_Subject].[MarkESETheory],0) > 0
                                            AND  [dbo].[EXL_ExamStudentSubjectL].[ExternalTheoryGrade] IS NOT NULL
                                            AND  [dbo].[EXL_ExamStudentSubjectL].[ExternalTheoryGrade] = 'F'
                                            THEN 1
                                            ELSE 0
                                        END  AS   [IsFailInExternalTheory]
                                                                                                                        
                                        ,CASE WHEN ISNULL([dbo].[SUB_Subject].[MarkESEPractical],0) > 0
                                            AND  [dbo].[EXL_ExamStudentSubjectL].[PracticalGrade] IS NOT NULL
                                            AND  [dbo].[EXL_ExamStudentSubjectL].[PracticalGrade] = 'F'
                                            THEN 1
                                            ELSE 0
                                        END  AS   [IsFailInExternalPractical]
                                                                                                                        
                                        ,[dbo].[SUB_Subject].[MarkCETheory] AS [MarkInternalTheoryMAX]
                                        ,[dbo].[SUB_Subject].[MarkCEPractical] AS [MarkInternalPracticalMAX]
                                        ,[dbo].[SUB_Subject].[MarkESETheory] AS [MarkExternalTheoryMAX]
                                        ,[dbo].[SUB_Subject].[MarkESEPractical] AS [MarkExternalPracticalMAX]
                                                                                                                                
                                        ,[dbo].[SUB_Subject].[PassingMarkCETheory] AS [MarkInternalTheoryPassing]
                                        ,[dbo].[SUB_Subject].[PassingMarkCEPractical] AS [MarkInternalPracticalPassing]
                                        ,[dbo].[SUB_Subject].[PassingMarkESETheory] AS [MarkExternalTheoryPassing]
                                        ,[dbo].[SUB_Subject].[PassingMarkESEPractical] AS [MarkExternalPracticalPassing]
                                                                                                                        
                                        ,[dbo].[SUB_Subject].[MarkTotal] AS [MarkTotalMAX]
                                        ,[dbo].[SUB_Subject].[Credit]
                                        ,0 AS [IsAbsentInESEPractical] --Not Given In Sheet
                                                                                                                                
                                        ,CASE WHEN ISNULL([dbo].[SUB_Subject].[MarkCETheory],0) > 0
                                            AND  [dbo].[EXL_ExamStudentSubjectL].[InternalTheoryGrade] IS NOT NULL
                                            AND  [dbo].[EXL_ExamStudentSubjectL].[InternalTheoryGrade] = 'F'
                                            THEN 1
                                            WHEN ISNULL([dbo].[SUB_Subject].[MarkESETheory],0) > 0
                                            AND  [dbo].[EXL_ExamStudentSubjectL].[ExternalTheoryGrade] IS NOT NULL
                                            AND  [dbo].[EXL_ExamStudentSubjectL].[ExternalTheoryGrade] = 'F'
                                            THEN 1
                                            ELSE 0
                                        END  AS   [IsFailInTheoryTotal]
                                                                                                                        
                                        ,CASE WHEN (ISNULL([dbo].[SUB_Subject].[MarkCEPractical],0) > 0 OR ISNULL([dbo].[SUB_Subject].[MarkESEPractical],0) > 0)
                                            AND  [dbo].[EXL_ExamStudentSubjectL].[PracticalGrade] IS NOT NULL
                                            AND  [dbo].[EXL_ExamStudentSubjectL].[PracticalGrade] = 'F'
                                            THEN 1
                                            ELSE 0
                                        END  AS   [IsFailInPracticalTotal]
                                                                                                                        
                                        ,0 AS [IsFailDueToLowAttendance] --Not Given In Sheet
                                        ,[dbo].[EXM_Grade].[GradePoint] AS [GradePoint]
                                        ,CAST(([dbo].[EXM_Grade].[GradePoint] * [dbo].[SUB_Subject].[Credit]) AS decimal(5,2)) AS [CreditPoint]
                                                                                                                                
                                        ,[dbo].[EXL_ExamStudentSubjectL].[ResultRemarks] AS   [ResultRemarks]
                                                                                                                        
                                        ,[dbo].[SUB_Subject].[MarkVivaVoce] AS [MarkExternalVivaVoceMAX]
                                        ,[dbo].[EXL_ExamStudentSubjectL].[AttemptNo] AS AttemptCount
                                            ,CASE WHEN ISNULL([dbo].[EXL_ExamStudentSubjectL].[PassFail], 'Fail') = 'Pass'
                                            THEN 0
                                            ELSE 1
                                        END  AS   [IsFailOverall]
                                                                                                                                
                                        ,[dbo].[EXL_ExamStudentSubjectL].[ExternalTheoryGrade] AS [ExternalTheoryGradeName]
                                        ,[dbo].[EXL_ExamStudentSubjectL].[InternalTheoryGrade] AS [InternalTheoryGradeName]
                                        ,[dbo].[EXL_ExamStudentSubjectL].[PracticalGrade] AS [PracticalGradeName]
                                                                                                    
                                        FROM	[dbo].[EXL_ExamL]

                                                                                                    
                                        INNER JOIN [dbo].[EXL_ExamStudentSubjectL]
                                        ON		[dbo].[EXL_ExamStudentSubjectL].[ExamLID] = [dbo].[EXL_ExamL].[ExamLID]

    -------------------------------------------------------------- Added due to specific condition ------------------------------- 
                                        AND		[dbo].[EXL_ExamStudentSubjectL].[GradeName] IS NOT NULL
    -------------------------------------------------------------- Added due to specific condition ------------------------------- 


                                        INNER JOIN [dbo].[EXM_ResultStudent]
                                        ON		[dbo].[EXM_ResultStudent].[StudentID] = [dbo].[EXL_ExamStudentSubjectL].[StudentID]
                                        AND		[dbo].[EXM_ResultStudent].[ExamID] = [dbo].[EXL_ExamL].[MappedExamID]
                                        AND		[dbo].[EXL_ExamL].[MappedExamID] IS NOT NULL
                                        AND		[dbo].[EXL_ExamStudentSubjectL].[StudentID] IS NOT NULL
                                        AND		[dbo].[EXL_ExamStudentSubjectL].[SubjectID] IS NOT NULL
                                --        AND		[dbo].[EXL_ExamStudentSubjectL].[GradeName] <> 'Ex'
                                                                                                    
                                        INNER JOIN [dbo].[SUB_Subject]
                                        ON		[dbo].[SUB_Subject].[SubjectID] = [dbo].[EXL_ExamStudentSubjectL].[SubjectID]
                                                                                                    
                                        LEFT OUTER JOIN	[dbo].[EXM_Grade]
                                        ON		[dbo].[EXM_Grade].[GradeID] = [dbo].[EXL_ExamStudentSubjectL].[GradeID]
                                        AND		[dbo].[EXM_Grade].[CourseID] = [dbo].[EXL_ExamL].[CourseID]
                                        AND		[dbo].[EXM_Grade].[GradeSystemID] = [dbo].[EXL_ExamStudentSubjectL].[GradeSystemID]

                                        LEFT OUTER JOIN [dbo].[EXM_ResultGrade]
                                        ON		[dbo].[EXM_ResultGrade].[ExamID] = [dbo].[EXL_ExamL].[MappedExamID]
                                        AND		[dbo].[EXM_ResultGrade].[ResultStudentID] = [dbo].[EXM_ResultStudent].[ResultStudentID]
                                        AND		[dbo].[EXM_ResultGrade].[StudentID] = [dbo].[EXL_ExamStudentSubjectL].[StudentID]
                                        AND		[dbo].[EXM_ResultGrade].[SubjectID] = [dbo].[EXL_ExamStudentSubjectL].[SubjectID]
                                                                                                    
                                        WHERE	[dbo].[EXM_ResultGrade].[ResultGradeID] IS NULL";
            }
            else if (tableName == "EXM_StudentWiseSubject")
            {
                query = @"--========= PARAMETERS ===========

                              DECLARE @CurrentDateTime datetime = [dbo].[GetPUMISDate]()

                            --========= PARAMETERS ===========


                                
                                SELECT
                                        [dbo].[EXL_ExamL].[MappedExamID] AS [FirstExamID],
                                        [dbo].[EXL_ExamL].[MappedExamID] AS [LastExamID],
                                        [dbo].[STU_Student].[ProgramID],
                                        [dbo].[STU_Student].[StudentID],
                                        [dbo].[STU_Student].[EnrollmentNo],
                                        [dbo].[STU_Student].[EnrollmentNo] AS [LastExamSeatNo],
                                        [dbo].[EXL_ExamL].[Semester],
                                        [dbo].[EXL_ExamStudentSubjectL].[SubjectID],
                                        NULL AS [AttendancePCT],
                                        NULL AS [AttendanceDetentionPCT],
                                        0 AS [IsDetainedDueToLowAttendance],
                                        [dbo].[EXM_ExamWiseSubject].[MarkCETheory] AS [MarkInternalTheoryMAX],
                                        [dbo].[EXM_ExamWiseSubject].[PassingMarkCETheory] AS [MarkInternalTheoryPassing],
                                        [dbo].[ACR_GracingMarkSubject].[TheoryGraceMark] AS [MarkInternalTheoryGracing],
                                        [dbo].[EXL_ExamStudentSubjectL].[InternalTheoryObtainedMark] AS [MarkInternalTheoryObtainedOriginal],
                                        [dbo].[EXL_ExamStudentSubjectL].[InternalTheoryObtainedMark] AS [MarkInternalTheoryObtainedGracing],
                                        [dbo].[EXM_ExamWiseSubject].[MarkCEPractical] AS [MarkInternalPracticalMAX],
                                        [dbo].[EXM_ExamWiseSubject].[PassingMarkCEPractical] AS [MarkInternalPracticalPassing],
                                        [dbo].[ACR_GracingMarkSubject].[PracticalGraceMark] AS [MarkInternalPracticalGracing],

                                        
                                        
                                        [dbo].[EXL_ExamStudentSubjectL].[PracticalObtainedMark] AS [MarkInternalPracticalObtainedOriginal],
                                        [dbo].[EXL_ExamStudentSubjectL].[PracticalObtainedMark] AS [MarkInternalPracticalObtainedGracing],
                                        
                                        [dbo].[EXM_ExamWiseSubject].[MarkESETheory] AS [MarkESETheoryMAX],
                                        [dbo].[EXM_ExamWiseSubject].[PassingMarkESETheory] AS [MarkESETheoryPassing],
                                        NULL AS [MarkESETheoryGracing],
                                        [dbo].[ACR_GracingMarkSubject].[ESETheoryGraceMarkToAll] AS [MarkESETheoryGracingToAll],
                                        NULL AS [MarkESETheorySectionA],
                                        NULL AS [MarkESETheorySectionB],
                                        [dbo].[EXM_ExamWiseSubject].[PaperMarkESETheory] AS [MarkESETheoryPaperMAX],
                                        [dbo].[EXM_ExamWiseSubject].[PaperPassingMarkESETheory] AS [MarkESETheoryPaperPassing],
                                        [dbo].[EXL_ExamStudentSubjectL].[ExternalTheoryObtainedMark] AS [MarkESETheoryObtainedMapped],
                                        [dbo].[EXL_ExamStudentSubjectL].[ExternalTheoryObtainedMark] AS [MarkESETheoryObtainedGracing],
                                        [dbo].[EXM_ExamWiseSubject].[MarkESEPractical] AS [MarkESEPracticalMAX],
                                        [dbo].[EXM_ExamWiseSubject].[PassingMarkESEPractical] AS [MarkESEPracticalPassing],
                                        [dbo].[ACR_GracingMarkSubject].[ESEPracticalGraceMark] AS [MarkESEPracticalGracing],
                                        [dbo].[ACR_GracingMarkSubject].[ESEPracticalGraceMarkToAll] AS [MarkESEPracticalGracingToAll],
                                        [dbo].[EXL_ExamStudentSubjectL].[PracticalObtainedMark] AS [MarkESEPracticalObtainedOriginal],
                                        [dbo].[EXL_ExamStudentSubjectL].[PracticalObtainedMark] AS [MarkESEPracticalObtainedGracing],
                                        
                                        NULL AS [MarkTheoryObtainedTotal],
                                        NULL AS [MarkPracticalObtainedTotal],
                                        [dbo].[EXM_ExamWiseSubject].[MarkTotal] AS [MarkTotalMAX],
                                        NULL AS [MarkTotalObtained],
                                        NULL AS [MarkTotalObtainedOutOf100],
                                        [dbo].[EXL_ExamStudentSubjectL].[AttemptNo] AS [AttemptCount],

                                        CASE WHEN ISNULL([dbo].[EXL_ExamStudentSubjectL].[PassFail], 'Fail') = 'Pass' THEN 1 ELSE 0 END AS [IsPass],
                                        [dbo].[EXL_ExamStudentSubjectL].[GradeID] AS [GradeID],
                                        [dbo].[EXL_ExamStudentSubjectL].[GradeName] AS [GradeName],
                                        NULL AS [UFMPunishmentID],
                                        NULL AS [InternalTheoryStatus],
                                        NULL AS [ExternalTheoryStatus],

                                        NULL AS [InternalPracticalStatus],
                                        NULL AS [ExternalPracticalStatus],

                                        'Imported' AS [Remarks],
                                        1 AS [UserID],
                                        @CurrentDateTime AS [Created],
                                        @CurrentDateTime AS [Modified],
                                        CASE WHEN [dbo].[EXL_ExamStudentSubjectL].[GradeName] IS NOT NULL
                                            THEN
                                                CASE WHEN [dbo].[EXL_ExamStudentSubjectL].[GradeName] = 'Ex'
                                                    THEN 1
                                                    ELSE 0
                                                    END
                                            ELSE 0
                                        END AS [IsExemption],
                                        NULL AS [ExemptionByUserID],
                                        NULL AS [ExemptionDateTime],
                                        'Imported' AS [ExemptionRemarks],
                                        [dbo].[EXM_ExamWiseSubject].[Credit] AS [Credit],
                                        NULL AS [GracingMark],
                                        NULL AS [IsShowResult],
                                        NULL AS [IsShowExemptionInResult],
                                        NULL AS [IsShowResultInExemption],
                                        [dbo].[EXL_ExamStudentSubjectL].[GradePoint],
                                        [EXL_ExamStudentSubjectL].[CreditPoint] AS [CreditPoint],
                                        
                                        [EXL_ExamStudentSubjectL].[ResultRemarks] AS [ResultRemarks],
                                        
                                        [dbo].[EXM_ExamWiseSubject].[MarkVivaVoce] AS [MarkESEVivaVoceMax],
                                        NULL AS [MarkESEVivaVoceObtained],
                                        0 AS [IsDetainedDueToBackLog],
                                        NULL AS [MarkESETheorySectionC],
                                        NULL AS [MarkESETheorySectionD],
                                        NULL AS [MarkESETheorySectionE],
                                        NULL AS [CurriculumWiseSubjectID],
                                        NULL AS [GradeSystemID],
                                        NULL AS [TheoryAttendancePCT],
                                        NULL AS [PracticalAttendancePCT],
                                        NULL AS [ExamStudentSubjectLID],
                                        NULL AS [StudentNotAppearingReasonID],
                                        NULL AS [StudentSubjectNotAppearingReasonID],
                                        NULL AS [ExamWiseFailReasonID],
                                        NULL AS [ExternalTheoryGradeID],
                                        NULL AS [ExternalPracticalGradeID],
                                        NULL AS [InternalTheoryGradeID],
                                        NULL AS [InternalPracticalGradeID],
                                        [dbo].[EXL_ExamStudentSubjectL].[ExternalTheoryGrade] AS [ExternalTheoryGradeName],
                                        CASE WHEN [dbo].[EXL_ExamStudentSubjectL].[PracticalGrade] IS NOT NULL
                                            AND  ISNULL([dbo].[EXM_ExamWiseSubject].[MarkESEPractical], 0) > 0
                                            THEN [dbo].[EXL_ExamStudentSubjectL].[PracticalGrade]
                                            ELSE NULL
                                        END AS [ExternalPracticalGradeName],
                                        [dbo].[EXL_ExamStudentSubjectL].[InternalTheoryGrade] AS [InternalTheoryGradeName],
                                        CASE WHEN [dbo].[EXL_ExamStudentSubjectL].[PracticalGrade] IS NOT NULL
                                            AND  ISNULL([dbo].[EXM_ExamWiseSubject].[MarkCEPractical], 0) > 0
                                            THEN [dbo].[EXL_ExamStudentSubjectL].[PracticalGrade]
                                            ELSE NULL
                                        END AS [InternalPracticalGradeName],
                                        [dbo].[EXL_ExamStudentSubjectL].[PracticalGrade] AS PracticalGradeName,

                                        NULL AS [IsEligibleForTheoryExam],
                                        NULL AS [IsEligibleForPracticalExam],
                                        NULL AS [MarkTotalOutOf100],
                                        NULL AS [MarkExternalTheoryObtainedOriginal],
                                        NULL AS [MarkExternalPracticalObtainedOriginal],
                                        NULL AS [MarkInternalPracticalElectiveObtained],
                                        NULL AS [MarkInternalPracticalElectiveObtainedGracing],
                                        NULL AS [MarkInternalMAX],
                                        NULL AS [ResultPCTExternalTheory],
                                        NULL AS [ResultPCTExternalPractical],
                                        NULL AS [ResultPCTInternalTheory],
                                        NULL AS [ResultPCTInternalPractical],
                                        NULL AS [InternalPracticalElectiveStatus],
                                        NULL AS [ExternalVivaVoceStatus],
                                        NULL AS [MarkInternalPracticalElectiveObtainedOriginal],
                                        [dbo].[EXM_ExamWiseSubject].[MarkCEPracticalElective] AS [MarkInternalPracticalElectiveMAX],
                                        [dbo].[EXM_ExamWiseSubject].[PassingMarkCEPracticalElective] AS [MarkInternalPracticalElectivePassing]
                                
                                FROM	[dbo].[EXL_ExamL]
                                
                                INNER JOIN [dbo].[EXL_ExamStudentSubjectL]
                                ON		[dbo].[EXL_ExamStudentSubjectL].[ExamLID] = [dbo].[EXL_ExamL].[ExamLID]

    -------------------------------------------------------------- Added due to specific condition ------------------------------- 
                                AND		[dbo].[EXL_ExamStudentSubjectL].[GradeName] IS NOT NULL
    -------------------------------------------------------------- Added due to specific condition ------------------------------- 

								INNER JOIN [dbo].[EXM_Exam]
                                ON		[dbo].[EXM_Exam].[ExamiD] = [dbo].[EXL_ExamL].[MappedExamID]
                                
                                INNER JOIN [dbo].[STU_Student]
                                ON		[dbo].[STU_Student].[StudentID] = [dbo].[EXL_ExamStudentSubjectL].[StudentID]
                                AND		[dbo].[EXL_ExamStudentSubjectL].[StudentID] IS NOT NULL
                                AND		[dbo].[EXL_ExamStudentSubjectL].[SubjectID] IS NOT NULL
                                AND		[dbo].[EXL_ExamStudentSubjectL].[GradeName] <> 'Ex'

                                INNER JOIN 
                                (
                                    SELECT
                                            [dbo].[EXL_ExamStudentSubjectL].[ExamStudentSubjectLID],
                                            [dbo].[EXL_ExamStudentSubjectL].[StudentID],
                                            [dbo].[EXL_ExamStudentSubjectL].[SubjectID],
                                            [dbo].[EXL_ExamL].[Semester],
                                            [dbo].[EXL_ExamStudentSubjectL].[AttemptNo],
                                            ROW_NUMBER()OVER(PARTITION BY [dbo].[EXL_ExamStudentSubjectL].[StudentID],[dbo].[EXL_ExamStudentSubjectL].[SubjectID],[dbo].[EXL_ExamL].[Semester] ORDER BY [dbo].[EXL_ExamStudentSubjectL].[AttemptNo] DESC) AS ANo
                                    FROM	[dbo].[EXL_ExamStudentSubjectL]

                                    INNER JOIN EXL_ExamL
                                    ON 		EXL_ExamL.ExamLID = [dbo].[EXL_ExamStudentSubjectL].[ExamLID]  

                                    WHERe	[dbo].[EXL_ExamStudentSubjectL].[StudentID] IS NOT NULL
                                    AND		[dbo].[EXL_ExamStudentSubjectL].[SubjectID] IS NOT NULL
                                    AND		[dbo].[EXL_ExamStudentSubjectL].[GradeName] <> 'Ex'

                                ) AS A
                                ON		A.[StudentID] = [dbo].[EXL_ExamStudentSubjectL].[StudentID]
                                AND		A.[SubjectID] = [dbo].[EXL_ExamStudentSubjectL].[SubjectID]
                                AND		A.[ExamStudentSubjectLID] = [dbo].[EXL_ExamStudentSubjectL].[ExamStudentSubjectLID]
                                AND		[dbo].[EXL_ExamStudentSubjectL].[GradeName] IS NOT NULL
                                AND		A.ANo = 1
                                
                                INNER JOIN [dbo].[EXM_ExamWiseSubject]
                                ON		[dbo].[EXM_ExamWiseSubject].[ExamID] = [dbo].[EXL_ExamL].[MappedExamID]
                                AND		[dbo].[EXM_ExamWiseSubject].[SubjectID] = [dbo].[EXL_ExamStudentSubjectL].[SubjectID]
                                
                                INNER JOIN	[INS_Course]
                                ON		[INS_Course].[CourseID] = [EXL_ExamL].[CourseID]
                                
                                LEFT OUTER JOIN [dbo].[ACR_GracingMarkSubject]
                                ON		[dbo].[ACR_GracingMarkSubject].[ExamID] = [dbo].[EXL_ExamL].[MappedExamID]
                                AND		[dbo].[ACR_GracingMarkSubject].[SubjectID] = [dbo].[EXL_ExamStudentSubjectL].[SubjectID]
                                
                                --LEFT OUTER JOIN	[dbo].[EXM_Grade]
                                --ON		[dbo].[EXM_Grade].[GradeID] = [dbo].[EXL_ExamStudentSubjectL].[GradeID]

                                --LEFT OUTER JOIN [EXM_GradeSystem]
                                --ON		[EXM_GradeSystem].[CourseID] = [INS_Course].[CourseID]
                                
                                LEFT OUTER JOIN [dbo].[EXM_StudentWiseSubject]
                                ON		[dbo].[EXM_StudentWiseSubject].[StudentID] = [dbo].[EXL_ExamStudentSubjectL].[StudentID]
                                AND		[dbo].[EXM_StudentWiseSubject].[Semester] = [dbo].[EXL_ExamL].[Semester]
                                AND		[dbo].[EXM_StudentWiseSubject].[SubjectID] = [dbo].[EXL_ExamStudentSubjectL].[SubjectID]
                                
                                WHERE	[dbo].[EXM_StudentWiseSubject].[StudentWiseSubjectID] IS NULL
                                
                                ORDER BY
                                        [dbo].[EXL_ExamStudentSubjectL].[StudentID],
                                        [dbo].[EXL_ExamL].[Semester],
                                        [dbo].[EXL_ExamStudentSubjectL].[SubjectID]";
            }
            else
            {
                throw new Exception("Invalid table name");
            }



            return query;
        }

        public static string GetQuery(string queryName)
        {
            // throw new Exception("Configure Query");

            string query;

            if (queryName == "Update_MappedExamID_IN_EXL_ExamL")
            {
                query = @"  UPDATE			EXL_ExamL 
                            SET				MappedExamID = EXM_Exam.ExamID
                            
                            FROM			EXL_ExamL 
                            INNER JOIN		EXM_Exam 
                                ON          EXL_ExamL.CourseID = EXM_Exam.CourseID
                                    AND     EXL_ExamL.Semester = EXM_Exam.Semester
                                    AND     EXL_ExamL.ExamType = EXM_Exam.ExamType
                                    AND     EXL_ExamL.FacultyID = EXM_Exam.FacultyID
                                    AND		EXL_ExamL.AcademicYearID = EXM_Exam.AcademicYearID 

                            INNER JOIN		EXM_ExamSeason
                                ON			EXM_ExamSeason.ExamSeasonID = EXM_Exam.ExamSeasonID
                                    AND		EXM_ExamSeason.Term = EXL_ExamL.Term
                            
                            WHERE			EXL_ExamL.MappedExamID IS NULL
                                AND			EXL_ExamL.ExamLID IN " + GetExamLIDCondition();
            }
            else
            {
                throw new Exception("Invalid query name");
            }

            return query;
        }


        public static List<string> NotNullableColumns(string tableName)
        {
            List<string> notNullableColumns = new List<string>();

            if (tableName == "EXL_ExamL")
            {
                notNullableColumns.Add("ExamName");
            }
            else if (tableName == "EXL_ExamStudentL")
            {
                notNullableColumns.Add("ExamLID");
            }
            else if (tableName == "EXL_ExamStudentSubjectL")
            {
                notNullableColumns.Add("ExamLID");
                notNullableColumns.Add("ExamStudentLID");
            }
            else if(tableName == "EXM_ExamSeason")
            {
                notNullableColumns.Add("AcademicYearID");
                notNullableColumns.Add("FacultyID");
                notNullableColumns.Add("Term");
            }
            else if(tableName == "EXM_Exam")
            {
                notNullableColumns.Add("FacultyID");
                notNullableColumns.Add("CourseID");
                notNullableColumns.Add("Semester");
                notNullableColumns.Add("ExamSeasonID");
                notNullableColumns.Add("ExamType");
            }
            else if(tableName == "EXM_ExamConfig")
            {
                notNullableColumns.Add("ExamID");
            }
            else if(tableName == "EXM_ExamWiseSubject")
            {
                notNullableColumns.Add("ExamID");
                notNullableColumns.Add("SubjectID");
            }
            else if(tableName == "EXM_ExamSchedule")
            {
                notNullableColumns.Add("ExamID");
                notNullableColumns.Add("SubjectID");
            }
            else if(tableName == "EXM_ResultStudent")
            {
                notNullableColumns.Add("ExamID");
                notNullableColumns.Add("StudentID");
            }
            else if(tableName == "EXM_ResultGrade")
            {
                notNullableColumns.Add("ExamID");
                notNullableColumns.Add("StudentID");
                notNullableColumns.Add("SubjectID");
            }
            else if(tableName == "EXM_StudentWiseSubject")
            {
                notNullableColumns.Add("StudentID");
                notNullableColumns.Add("SubjectID");
                notNullableColumns.Add("Semester");
            }


            return notNullableColumns;
        }

        public static List<string> CheckUniquenessOn(string tableName)
        {
            List<string> checkUniquenessOn = new List<string>();

            if (tableName == "EXL_ExamL")
            {
                checkUniquenessOn.Add("ExamName");
            }
            else if (tableName == "EXL_ExamStudentL")
            {
                checkUniquenessOn.Add("EnrollmentNo");
                checkUniquenessOn.Add("ExamLID");
            }
            else if (tableName == "EXL_ExamStudentSubjectL")
            {
                checkUniquenessOn.Add("EnrollmentNo");
                checkUniquenessOn.Add("SubjectCode");
                checkUniquenessOn.Add("ExamLID");
                checkUniquenessOn.Add("ExamStudentLID");
            }
            else if (tableName == "EXM_ExamSeason")
            {
                checkUniquenessOn.Add("FacultyID");
                checkUniquenessOn.Add("Term");
                checkUniquenessOn.Add("AcademicYearID");
            }
            else if (tableName == "EXM_Exam")
            {
                checkUniquenessOn.Add("FacultyID");
                checkUniquenessOn.Add("CourseID");
                checkUniquenessOn.Add("Semester");
                checkUniquenessOn.Add("ExamSeasonID");
                checkUniquenessOn.Add("ExamType");
            }
            else if (tableName == "EXM_ExamConfig")
            {
                checkUniquenessOn.Add("ExamID");
            }
            else if (tableName == "EXM_ExamWiseSubject")
            {
                checkUniquenessOn.Add("ExamID");
                checkUniquenessOn.Add("SubjectID");
                checkUniquenessOn.Add("SubjectCode");
            }
            else if (tableName == "EXM_ExamSchedule")
            {
                checkUniquenessOn.Add("ExamID");
                checkUniquenessOn.Add("SubjectID");
            }
            else if (tableName == "EXM_ResultStudent")
            {
                checkUniquenessOn.Add("ExamID");
                checkUniquenessOn.Add("StudentID");
                checkUniquenessOn.Add("EnrollmentNo");
            }
            else if (tableName == "EXM_ResultGrade")
            {
                checkUniquenessOn.Add("ExamID");
                checkUniquenessOn.Add("StudentID");
                checkUniquenessOn.Add("SubjectID");
            }
            else if (tableName == "EXM_StudentWiseSubject")
            {
                checkUniquenessOn.Add("StudentID");
                checkUniquenessOn.Add("SubjectID");
                checkUniquenessOn.Add("Semester");
            }

            return checkUniquenessOn;
        }

        

    }
}